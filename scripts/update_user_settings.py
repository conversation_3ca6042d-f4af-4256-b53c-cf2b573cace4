import asyncio
import logging

from services.base.dependency_bootstrapper import (
    bootstrapper,
)
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings

logger = logging.getLogger()
logger.setLevel(logging.INFO)


async def run():
    logging.info("Validating user settings")

    settings_repo = bootstrapper.get(interface=MemberUserSettingsRepository)
    count = 0
    user_count = 0
    async for settings_chunk in settings_repo.yield_results(wrapper=ReadFromDatabaseWrapper(search_keys={})):
        settings: MemberUserSettings
        for settings in settings_chunk:
            user_count += 1
            location = settings.profile.location
            if location.route == "":
                count += 1
                # await settings_repo.insert_or_update(settings=settings)
                logging.error(f"UUID: {settings.user_uuid}, {location}")
    logging.error(f"users: {user_count}, errors: {count}")

    logging.info("Validated user settings")


if __name__ == "__main__":
    asyncio.run(run())
