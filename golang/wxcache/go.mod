module llif.org/wxcache

go 1.25.0

require (
	github.com/aws/aws-sdk-go-v2/credentials v1.18.5
	github.com/caarlos0/env/v10 v10.0.0
	github.com/fatih/structs v1.1.0
	github.com/getsentry/sentry-go v0.35.0
	github.com/getsentry/sentry-go/echo v0.35.0
	github.com/go-playground/validator/v10 v10.27.0
	github.com/google/go-querystring v1.1.0
	github.com/google/uuid v1.6.0
	github.com/joho/godotenv v1.5.1
	github.com/labstack/echo/v4 v4.13.0
	github.com/opensearch-project/opensearch-go v1.1.0
	github.com/paulmach/orb v0.11.0
	github.com/stretchr/testify v1.11.1
	golang.org/x/time v0.12.0
)

require (
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.18.3 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.4.5 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.7.5 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.13.0 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.13.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.28.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.33.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.37.1 // indirect
	github.com/aws/smithy-go v1.23.0 // indirect
)

require (
	github.com/aws/aws-sdk-go-v2 v1.38.2
	github.com/aws/aws-sdk-go-v2/config v1.31.1
	github.com/aws/aws-sdk-go-v2/service/secretsmanager v1.39.1
	github.com/aws/aws-sdk-go-v2/service/sns v1.38.0
	github.com/aws/aws-sdk-go-v2/service/sqs v1.42.2
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	golang.org/x/crypto v0.36.0 // indirect
	golang.org/x/exp v0.0.0-20250128182459-e0ece0dbea4c
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
