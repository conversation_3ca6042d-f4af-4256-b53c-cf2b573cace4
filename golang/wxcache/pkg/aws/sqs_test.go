package llifaws_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	llifaws "llif.org/wxcache/pkg/aws"
	"llif.org/wxcache/pkg/testutil/testsetup"
)

func TestSQSCanCreateQueueIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("needs localstack")
	}

	sqs, err := createSQSClient()
	if err != nil {
		t.Fatal(err)
	}

	ctx := context.Background()
	output, err := sqs.Create(ctx, "test_queue")
	require.NoError(t, err)

	if output.QueueUrl == nil {
		t.Fatalf("could not create queue, received nil queue url")
	}
}

func TestSQSCanFetchQueueArnIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("needs localstack")
	}

	sqs, err := createSQSClient()
	if err != nil {
		t.Fatal(err)
	}

	ctx := context.Background()
	output, err := sqs.Create(ctx, "test_queue")
	require.NoError(t, err)

	if output.QueueUrl == nil {
		t.Fatalf("could not create queue, received nil queue url")
	}

	queueArn, err := sqs.GetQueueArn(ctx, *output.QueueUrl)
	require.NoError(t, err)

	if queueArn == nil {
		t.Fatalf("could not fetch queue ARN, received nil")
	}
}

func TestSQSCanSendAndReceiveMessageInQueueIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("needs localstack")
	}

	sqs, err := createSQSClient()
	require.NoError(t, err)

	ctx := context.Background()

	output, err := sqs.Create(ctx, "test_queue")
	require.NoError(t, err)

	if output.QueueUrl == nil {
		t.Fatalf("could not create queue, received nil queue url")
	}

	_, err = sqs.Send(ctx, "test_queue", "hello, world")
	require.NoError(t, err)

	messages, err := sqs.Receive(ctx, "test_queue", 1, 10)
	require.NoError(t, err)

	if len(messages.Messages) == 0 {
		t.Fatalf("received no messages from the test queue, got data=%#v", messages)
	}

	if *messages.Messages[0].Body != "hello, world" {
		t.Fatalf("received message=%s does not match the expected output=%s", *messages.Messages[0].Body, "hello, world")
	}
}

func createSQSClient() (*llifaws.SQS, error) {
	conf, err := testsetup.NewConfig()
	if err != nil {
		return nil, err
	}

	sqs, err := llifaws.NewSQS(context.Background(), llifaws.SQSOpts{
		AWSRegion:   conf.AWS.Region,
		AWSEndpoint: conf.AWS.Endpoint,
		RunEnv:      conf.RunEnv,
	})
	if err != nil {
		return nil, err
	}
	return sqs, nil
}
