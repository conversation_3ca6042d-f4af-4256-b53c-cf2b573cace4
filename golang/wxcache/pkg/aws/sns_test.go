package llifaws_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/config"
	llifaws "llif.org/wxcache/pkg/aws"
	"llif.org/wxcache/pkg/testutil/testsetup"
)

func createQueueAndGetArn(t *testing.T, ctx context.Context, conf *config.Config, queueName string) (string, error) {
	sqs, err := llifaws.NewSQS(context.Background(), llifaws.SQSOpts{
		AWSEndpoint: conf.AWS.Endpoint,
		AWSRegion:   conf.AWS.Region,
		RunEnv:      conf.RunEnv,
	})
	require.NoError(t, err)

	output, err := sqs.Create(ctx, queueName)
	require.NoError(t, err)
	t.Log(*output.QueueUrl)

	queueArn, err := sqs.GetQueueArn(ctx, *output.QueueUrl)
	require.NoError(t, err)
	if queueArn == nil {
		t.Fatalf("could not receive information about the queue, got nil queueArn")
	}
	return *queueArn, nil
}

func TestSNSCanCreateTopicAndSubscribeAndUnsubscribeIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("needs localstack")
	}
	conf, err := testsetup.NewConfig()
	require.NoError(t, err)

	ctx := context.Background()
	queueArn, err := createQueueAndGetArn(t, ctx, conf, "weather_queue")
	require.NoError(t, err)

	s, err := llifaws.NewSNS(context.Background(), llifaws.SNSOpts{
		AWSEndpoint: conf.AWS.Endpoint,
		AWSRegion:   conf.AWS.Region,
		RunEnv:      conf.RunEnv,
	})
	require.NoError(t, err)

	topic, err := s.CreateTopic(ctx, "load_finished")
	require.NoError(t, err)
	t.Log(topic)

	subscription, err := s.Subscribe(ctx, *topic.TopicArn, queueArn)
	require.NoError(t, err)
	t.Log(subscription)

	unsubscription, err := s.Unsubscribe(ctx, *subscription.SubscriptionArn)
	require.NoError(t, err)
	t.Log(unsubscription)
}
