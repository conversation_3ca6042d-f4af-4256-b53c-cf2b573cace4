package geo_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/testutil/location"
)

func TestCoordinatesInContinentMatchesEuropeShouldPass(t *testing.T) {
	tests := []struct {
		c              geo.Continent
		lat            float64
		long           float64
		expectedOutput bool
	}{
		{
			c:              geo.Europe,
			lat:            location.NewYork.Lat,
			long:           location.NewYork.Long,
			expectedOutput: false,
		},
		{
			c:              geo.Europe,
			lat:            location.London.Lat,
			long:           location.London.Long,
			expectedOutput: true,
		},
		{
			c:              geo.Europe,
			lat:            location.Prague.Lat,
			long:           location.Prague.Long,
			expectedOutput: true,
		},
		{
			c:              geo.Europe,
			lat:            location.SanDiego.Lat,
			long:           location.SanDiego.Long,
			expectedOutput: false,
		},
		{
			c:              geo.Europe,
			lat:            location.Singapore.Lat,
			long:           location.Singapore.Long,
			expectedOutput: false,
		},
		{
			c:              geo.Europe,
			lat:            location.Tokyo.Lat,
			long:           location.Tokyo.Long,
			expectedOutput: false,
		},
		{
			c:              geo.Europe,
			lat:            location.Seoul.Lat,
			long:           location.Seoul.Long,
			expectedOutput: false,
		},
		{
			c:              geo.Europe,
			lat:            location.Capetown.Lat,
			long:           location.Capetown.Long,
			expectedOutput: false,
		},
	}

	for i, tt := range tests {
		ok, err := geo.CoordinatesInContinent(geo.Europe, tt.lat, tt.long)
		require.NoError(t, err)

		if ok != tt.expectedOutput {
			t.Fatalf("[%d] expected to receive=%t, instead got=%t", i, tt.expectedOutput, ok)
		}
	}
}

func TestIsWithinRadiusPasses(t *testing.T) {
	tests := []struct {
		name        string
		expectedLat float64
		expectedLon float64
		actualLat   float64
		actualLon   float64
		radius      string
	}{
		{
			name:        "Point within 10km radius",
			expectedLat: 51.5074, // London
			expectedLon: -0.1278,
			actualLat:   51.5074,
			actualLon:   -0.1278,
			radius:      "10km",
		},
		{
			name:        "Point outside 1km radius",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5374, // ~3km away
			actualLon:   -0.1278,
			radius:      "5km",
		},
		{
			name:        "Point within 5000m radius",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5174,
			actualLon:   -0.1278,
			radius:      "5000m",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res := geo.IsWithinRadius(tt.expectedLat, tt.expectedLon, tt.actualLat, tt.actualLon, tt.radius)
			require.Equal(t, true, res)
		})
	}
}

func TestIsWithinRadiusRaises(t *testing.T) {
	tests := []struct {
		name        string
		expectedLat float64
		expectedLon float64
		actualLat   float64
		actualLon   float64
		radius      string
	}{
		{
			name:        "Invalid radius format",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5174,
			actualLon:   -0.1278,
			radius:      "5miles",
		},
		{
			name:        "Empty radius",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5174,
			actualLon:   -0.1278,
			radius:      "",
		},
		{
			name:        "Invalid numeric value",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5174,
			actualLon:   -0.1278,
			radius:      "invalidkm",
		},
		{
			name:        "Point outside 1km radius",
			expectedLat: 51.5074,
			expectedLon: -0.1278,
			actualLat:   51.5374, // ~3km away
			actualLon:   -0.1278,
			radius:      "2km",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res := geo.IsWithinRadius(tt.expectedLat, tt.expectedLon, tt.actualLat, tt.actualLon, tt.radius)
			require.Equal(t, false, res)
		})
	}
}
