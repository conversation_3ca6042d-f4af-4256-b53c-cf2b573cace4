package geo

import (
	"fmt"
	"log/slog"
	"math"
	"strconv"
	"strings"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/planar"
)

type Continent string

const (
	Asia             Continent = "asia"
	Africa           Continent = "africa"
	Europe           Continent = "europe"
	NorthAmerica     Continent = "north_america"
	SouthAmerica     Continent = "south_america"
	AustraliaOceania Continent = "australia_oceania"
)

var continentToBoundaries map[Continent]orb.Polygon = map[Continent]orb.Polygon{
	Europe: {
		{{-27.0, 35.0}, {50.0, 35.0}, {50.0, 72.0}, {-27.0, 72.0}, {-27.0, 35.0}},
	},
}

// Validates if given coordinates are within the boundaries of the given continent.
func CoordinatesInContinent(c Continent, lat, long float64) (bool, error) {
	boundary, ok := continentToBoundaries[c]
	if !ok {
		return false, fmt.Errorf("given continent=%s is not supported", c)
	}

	point := orb.Point{long, lat}
	return planar.PolygonContains(boundary, point), nil
}

// IsWithinRadius checks if actual coordinates are within the specified radius of expected coordinates
// radius should be in format "10km" or "10m"
func IsWithinRadius(latA, lonA, latB, lonB float64, radius string) bool {
	// Parse radius value and unit
	radiusValue, err := parseRadius(radius)
	if err != nil {
		slog.Error("invalid radius format", "radius", radius, "err", err)
		return false
	}

	// Calculate distance between points using Haversine formula
	distance := haversineDistance(latA, lonA, latB, lonB)

	return distance <= radiusValue
}

// parseRadius converts radius string (e.g., "10km" or "500m") to meters
func parseRadius(radius string) (float64, error) {
	radius = strings.ToLower(strings.TrimSpace(radius))

	if strings.HasSuffix(radius, "km") {
		value, err := strconv.ParseFloat(radius[:len(radius)-2], 64)
		if err != nil {
			return 0, fmt.Errorf("invalid radius value: %w", err)
		}
		return value * 1000, nil
	} else if strings.HasSuffix(radius, "m") {
		value, err := strconv.ParseFloat(radius[:len(radius)-1], 64)
		if err != nil {
			return 0, fmt.Errorf("invalid radius value: %w", err)
		}
		return value, nil
	}

	return 0, fmt.Errorf("unsupported radius unit (use 'km' or 'm')")
}

// haversineDistance calculates the distance between two points on Earth in meters
func haversineDistance(expectedLat, expectedLon, actualLat, actualLon float64) float64 {
	const earthRadius = 6371000 // Earth's radius in meters

	// Convert latitude and longitude to radians
	lat1Rad := expectedLat * math.Pi / 180
	lon1Rad := expectedLon * math.Pi / 180
	lat2Rad := actualLat * math.Pi / 180
	lon2Rad := actualLon * math.Pi / 180

	// Differences in coordinates
	dLat := lat2Rad - lat1Rad
	dLon := lon2Rad - lon1Rad

	// Haversine formula
	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(dLon/2)*math.Sin(dLon/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}
