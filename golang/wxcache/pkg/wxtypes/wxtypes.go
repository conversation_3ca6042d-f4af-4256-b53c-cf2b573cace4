package wxtypes

import (
	"fmt"
	"time"
)

type Provider int

func (p Provider) String() string {
	return [...]string{"", "Ambee", "OpenAQ", "WeatherAPI", "OpenMeteo"}[p]
}

type RequestType int

const (
	AirQuality RequestType = iota
	Weather
	Pollen
	ForecastAirQuality
	ForecastWeather
	ForecastPollen
)

func (r RequestType) String() string {
	return [...]string{"Air Quality", "Weather", "Pollen", "ForecastAirQuality", "ForecastWeather", "ForecastPollen"}[r]
}

type TimeBucket struct {
	TimeFrom time.Time `json:"time_from"`
	TimeTo   time.Time `json:"time_to"`
}

// SpaceTime represents a 3-dimensional (no elevation, ground level assumed) space-time location
type SpaceTime struct {
	Lat      float64   `json:"lat" validate:"required"`
	Long     float64   `json:"lon" validate:"required"`
	TimeFrom time.Time `json:"timestamp" validate:"required"`
	TimeTo   time.Time `json:"end_time" validate:"required"`
}

func (s SpaceTime) String() string {
	return fmt.Sprintf("Latitude: %f, Longtitude: %f, TimeFrom: %s, TimeTo: %s", s.Lat, s.Long, s.TimeFrom, s.TimeTo)
}

type Metadata struct {
	Provider string `json:"provider" validate:"required"`
}

type SystemProperties struct {
	CreatedAt time.Time `json:"created_at" validate:"required"`
	Backfill  bool      `json:"backfill"`
}

type Coordinates struct {
	Latitude  float64 `json:"lat" validate:"required,latitude"`
	Longitude float64 `json:"lon" validate:"required,longitude"`
}

type AirQualityPollutants struct {
	PM10 *float64 `json:"pm10" validate:"at_least_one,omitnil,gte=0"`
	PM25 *float64 `json:"pm25" validate:"at_least_one,omitnil,gte=0"`
	CO   *float64 `json:"co" validate:"at_least_one,omitnil,gte=0"`
	O3   *float64 `json:"o3" validate:"at_least_one,omitnil,gte=0"`
	SO2  *float64 `json:"so2" validate:"at_least_one,omitnil,gte=0"`
	NO2  *float64 `json:"no2" validate:"at_least_one,omitnil,gte=0"`
}

type AirQualityIndex struct {
	EU *int `json:"eu" validate:"at_least_one"`
	US *int `json:"us" validate:"at_least_one"`
	GB *int `json:"gb" validate:"at_least_one"`
}

type AirQualityV2 struct {
	Timestamp        time.Time            `json:"timestamp" validate:"required"`
	Pollutants       AirQualityPollutants `json:"pollutants" validate:"required"`
	AQI              AirQualityIndex      `json:"aqi" validate:"required"`
	Coordinates      Coordinates          `json:"coordinates" validate:"required"`
	Metadata         Metadata             `json:"metadata" validate:"required"`
	SystemProperties SystemProperties     `json:"system_properties" validate:"required"`
}

type WeatherTemperature struct {
	Temperature *float64 `json:"temperature" validate:"at_least_one,omitnil,gte=-100,lte=100"`
	FeelsLike   *float64 `json:"feels_like" validate:"at_least_one,omitnil,gte=-100,lte=100"`
}

type WeatherWind struct {
	Speed     *float64 `json:"speed" validate:"at_least_one,omitnil,gte=0,lte=400"`
	Gust      *float64 `json:"gust" validate:"omitnil"`
	Degree    *float64 `json:"degree" validate:"omitnil"`
	Direction *string  `json:"direction" validate:"omitnil,required"`
}

type WeatherV2 struct {
	Timestamp        time.Time          `json:"timestamp" validate:"required"`
	Temperature      WeatherTemperature `json:"temperature" validate:"at_least_one"`
	Wind             WeatherWind        `json:"wind" validate:"at_least_one"`
	Humidity         *float64           `json:"humidity" validate:"at_least_one,omitnil,gte=0"`
	CloudCover       *int               `json:"cloud_cover" validate:"at_least_one,omitnil,gte=0"`
	UV               *int               `json:"uv" validate:"at_least_one,omitnil,gte=0,lte=10"`
	Pressure         *float64           `json:"pressure" validate:"at_least_one,omitnil,gte=0"`
	Visiblity        *float64           `json:"visibility" validate:"at_least_one,omitnil,gte=0"`
	Precipitation    *float64           `json:"precipitation" validate:"at_least_one,omitnil,gte=0"`
	Coordinates      Coordinates        `json:"coordinates" validate:"required"`
	Metadata         Metadata           `json:"metadata" validate:"required"`
	SystemProperties SystemProperties   `json:"system_properties" validate:"required"`
}

type PollenSubspecies struct {
	Name  string `json:"name" validate:"required"`
	Count int    `json:"count" validate:"required"`
}

type PollenSpecies struct {
	Count      int                `json:"count" validate:"gte=0"`
	Subspecies []PollenSubspecies `json:"subspecies"`
}

type PollenV2 struct {
	Timestamp        time.Time        `json:"timestamp" validate:"required"`
	Tree             *PollenSpecies   `json:"tree" validate:"at_least_one,omitnil"`
	Weed             *PollenSpecies   `json:"weed" validate:"at_least_one,omitnil"`
	Grass            *PollenSpecies   `json:"grass" validate:"at_least_one,omitnil"`
	Coordinates      Coordinates      `json:"coordinates" validate:"required"`
	Metadata         Metadata         `json:"metadata" validate:"required"`
	SystemProperties SystemProperties `json:"system_properties" validate:"required"`
}

type DataType interface {
	AirQualityV2 | WeatherV2 | PollenV2
}
