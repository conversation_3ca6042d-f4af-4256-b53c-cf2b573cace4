package osquery

import "github.com/fatih/structs"

// dateHistorgramAggregationOpts holds dynamic options available for the date histogram aggregation
type dateHistorgramAggregationOpts struct {
	Field          string `structs:"field"`
	Interval       string `structs:"interval"`
	ExtendedBounds struct {
		Min string `structs:"min,omitempty"`
		Max string `structs:"max,omitempty"`
	} `structs:"extended_bounds"`
}

// dateHistogramAggregation represents the aggregation of type "date_histogram"
// https://opensearch.org/docs/latest/aggregations/bucket/date-histogram/
type dateHistogramAggregation struct {
	name string
	opts dateHistorgramAggregationOpts
}

// DateHistogramAggregation creates a new date histogram aggregation with a name.
// The field parameter represents the field in the database on which the aggregation will run
func DateHistogramAggregation(name, field string) *dateHistogramAggregation {
	return &dateHistogramAggregation{name: name, opts: dateHistorgramAggregationOpts{Field: field}}
}

// Interval sets the interval period for the aggregation bucket
func (a *dateHistogramAggregation) Interval(i string) *dateHistogramAggregation {
	a.opts.Interval = i
	return a
}

// ExtendedBounds sets the min,max boundaries for the aggregation query
func (a *dateHistogramAggregation) ExtendedBounds(min, max string) *dateHistogramAggregation {
	a.opts.ExtendedBounds.Min = min
	a.opts.ExtendedBounds.Max = max
	return a
}

// Map transforms the date histogram aggregation into an equal map representation
func (a *dateHistogramAggregation) Map() map[string]any {
	return map[string]any{
		a.name: map[string]any{
			"date_histogram": structs.Map(a.opts),
		},
	}
}
