package osquery

import "github.com/fatih/structs"

// rangeQueryOpts holds all dynamic values set by the caller of the package.
// They should not be directly accessed, but rather set through a method as a builder pattern
type rangeQueryOpts struct {
	Gt  any `structs:"gt,omitempty"`  // Greater than
	Gte any `structs:"gte,omitempty"` // Greater than or equal to
	Lt  any `structs:"lt,omitempty"`  // Less than
	Lte any `structs:"lte,omitempty"` // Less than or equal to
}

// rangeQuery represents the query of type "range"
// https://opensearch.org/docs/latest/query-dsl/term/range/
type rangeQuery struct {
	field string
	opts  rangeQueryOpts
}

// Range creates a new range query representation with the field name set
func Range(field string) *rangeQuery {
	return &rangeQuery{field: field}
}

// Gt sets the value of the specified field should be above the given value
func (q *rangeQuery) Gt(gt any) *rangeQuery {
	q.opts.Gt = gt
	return q
}

// Gte sets the value of the specified field should be above or equal to the given value
func (q *rangeQuery) Gte(gte any) *rangeQuery {
	q.opts.Gte = gte
	return q
}

// Lt sets the value of the specified field should be lower than the given value
func (q *rangeQuery) Lt(lt any) *rangeQuery {
	q.opts.Lt = lt
	return q
}

// Lte sets the value of the specified field should be lower or equal to the given value
func (q *rangeQuery) Lte(lte any) *rangeQuery {
	q.opts.Lte = lte
	return q
}

// Map transforms the range query into an equal map representation
func (q *rangeQuery) Map() map[string]any {
	return map[string]any{
		"range": map[string]any{
			q.field: structs.Map(q.opts),
		},
	}
}

// geoDistanceOpts holds all dynamic values set by the caller of the package.
// They should not be directly accessed, but rather set through a method as a builder pattern
type geoDistanceOpts struct {
	Distance string  // The radius of the circle centered on the specified location, i.e. "10km"
	Lat      float64 // Latitude
	Lon      float64 // Longitude
}

// geoDistanceQuery represents the query of type "geo_distance"
// https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-geo-distance-query.html
type geoDistanceQuery struct {
	field string
	opts  geoDistanceOpts
}

// GeoDistance creates a new query of type "geo_distance" for the specified field
func GeoDistance(field string) *geoDistanceQuery {
	return &geoDistanceQuery{field: field}
}

// Distance sets the distance radius for the specified field
func (q *geoDistanceQuery) Distance(distance string) *geoDistanceQuery {
	q.opts.Distance = distance
	return q
}

// LatLon sets the latitude, longitude coordinates for the specified field
func (q *geoDistanceQuery) LatLon(lat, lon float64) *geoDistanceQuery {
	q.opts.Lat = lat
	q.opts.Lon = lon
	return q
}

// Map transforms the geo distance query into an equal map representation
func (q *geoDistanceQuery) Map() map[string]any {
	return map[string]any{
		"geo_distance": map[string]any{
			"distance": q.opts.Distance,
			q.field: map[string]any{
				"lat": q.opts.Lat,
				"lon": q.opts.Lon,
			},
		},
	}
}
