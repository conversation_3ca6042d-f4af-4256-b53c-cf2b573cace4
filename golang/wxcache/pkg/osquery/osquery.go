package osquery

// Mappable is a low-level interface which any query has to support
// to serialize the query into a map so we can later turn it into a JSON
type Mappable interface {
	Map() map[string]any
}

// baseQuery holds the top-level "query" field and all underlying clauses
type BaseQuery struct {
	query Mappable
	aggs  Mappable
}

// Query creates a new base query consisting of the provided queries
func Query(q Mappable) *BaseQuery {
	return &BaseQuery{query: q}
}

// A<PERSON> adds all given aggregations to the base query
func (q *BaseQuery) Aggs(agg Mappable) *BaseQuery {
	q.aggs = agg
	return q
}

// Map transforms the query into an equal map representation
func (q *BaseQuery) Map() map[string]any {
	result := map[string]any{
		"query": q.query.Map(),
	}
	if q.aggs != nil {
		result["aggs"] = q.aggs.Map()
	}
	return result
}
