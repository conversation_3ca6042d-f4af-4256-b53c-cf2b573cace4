package aqi

import (
	"fmt"

	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/internal/convertor/concentration"
	"llif.org/wxcache/pkg/aqi/v2/breakpoint"
	"llif.org/wxcache/pkg/gas"
	"llif.org/wxcache/pkg/wxtypes"
)

type pollutantsUS struct {
	O3   *float64 `validate:"omitnil,required,gte=0"`
	PM25 *float64 `validate:"omitnil,required,gte=0"`
	PM10 *float64 `validate:"omitnil,required,gte=0"`
	CO   *float64 `validate:"omitnil,required,gte=0"`
	SO2  *float64 `validate:"omitnil,required,gte=0"`
	NO2  *float64 `validate:"omitnil,required,gte=0"`
}

type transformerAQI_US struct {
	pollutants pollutantsUS
}

func newTransformerAQI_US(c AQIConcentration) transformerAQI_US {
	/*
		As per https://www.airnow.gov/sites/default/files/2020-05/aqi-technical-assistance-document-sept2018.pdf
			PM2.5 (µg/m3) – truncate to 1 decimal place
			PM10 (µg/m3) – truncate to integer
			Ozone (ppm) – truncate to 3 decimal places
			CO (ppm) – truncate to 1 decimal place
			SO2 (ppb) – truncate to integer
			NO2 (ppb) – truncate to integer
	*/
	var (
		pm10 = convertor.RoundFloatPtr(c.PM10, 0)
		pm25 = convertor.RoundFloatPtr(c.PM25, 1)
		o3   = convertor.RoundFloat(convertor.FloatNilPtrToZero(c.O3), 3)
		co   = convertor.RoundFloat(convertor.FloatNilPtrToZero(c.CO), 1)
		so2  = convertor.RoundFloat(convertor.FloatNilPtrToZero(c.SO2), 0)
		no2  = convertor.RoundFloat(convertor.FloatNilPtrToZero(c.NO2), 0)

		concO3  = concentration.MicrogramsCubedToPpm(o3, gas.Ozone)
		concCO  = concentration.MicrogramsCubedToPpm(co, gas.CarbonMonoxide)
		concSO2 = concentration.MicrogramsCubedToPpb(so2, gas.SulphurDioxide)
		concNO2 = concentration.MicrogramsCubedToPpb(no2, gas.NitrogenDioxide)
	)

	return transformerAQI_US{
		pollutants: pollutantsUS{
			PM10: pm10,
			PM25: pm25,
			O3:   &concO3,
			CO:   &concCO,
			SO2:  &concSO2,
			NO2:  &concNO2,
		},
	}
}

func (t transformerAQI_US) calculatePollutantAQI(pollutantType breakpoint.Type, f *float64) *float64 {
	// check beforehand maybe?
	if f == nil {
		return nil
	}

	var (
		pollutantBreakpointTable = breakpoint.GetPollutantBreakpointTable(breakpoint.US, pollutantType)
		indexBreakpointTable     = breakpoint.GetIndexBreakpointTable(breakpoint.US)
		foundIndex               = -1
	)

	for i, row := range pollutantBreakpointTable {
		if *f >= row.Low && *f <= row.High {
			foundIndex = i
			break
		}
	}

	if foundIndex == -1 {
		// @REVIEW: is it correct to assume a value which is >= max value in breakpoint table is in the highest category?
		foundIndex = len(pollutantBreakpointTable) - 1
	}

	var (
		concentrationBreakpoint = pollutantBreakpointTable[foundIndex]
		aqIndexBreakpoint       = indexBreakpointTable[foundIndex]

		IHigh, ILow = aqIndexBreakpoint.High, aqIndexBreakpoint.Low
		CHigh, CLow = concentrationBreakpoint.High, concentrationBreakpoint.Low
	)

	// As per https://www.airnow.gov/sites/default/files/2020-05/aqi-technical-assistance-document-sept2018.pdf
	// US AQI calculation: I = ((IHigh-ILow)/(CHigh-CLow)) * (C-CLow) + ILow
	aqi := ((IHigh-ILow)/(CHigh-CLow))*(*f-CLow) + ILow

	// Constrain AQI to be lower-bound and upper-bound by the standard -- higher or lower values do not make sense
	// I.e. value >= 500 is set to 500, value <= 0 is set to 0
	aqi = Constrain(aqi, 0, 500)
	aqi = convertor.RoundFloat(aqi, 0)
	return &aqi
}

func (t transformerAQI_US) CalculateAQI() (aqiResult, error) {
	if err := wxtypes.ValidateStruct[pollutantsUS](t.pollutants); err != nil {
		return aqiResult{}, fmt.Errorf("AQI concentrations is invalid: %s", err)
	}

	var (
		co   = t.calculatePollutantAQI(breakpoint.CO, t.pollutants.CO)
		o3   = t.calculatePollutantAQI(breakpoint.O3, t.pollutants.O3)
		no2  = t.calculatePollutantAQI(breakpoint.NO2, t.pollutants.NO2)
		so2  = t.calculatePollutantAQI(breakpoint.SO2, t.pollutants.SO2)
		pm10 = t.calculatePollutantAQI(breakpoint.PM10, t.pollutants.PM10)
		pm25 = t.calculatePollutantAQI(breakpoint.PM25, t.pollutants.PM25)
	)

	// calculate the highest value out of the provided pollutants
	highest := -1.0
	if co != nil && *co > highest {
		highest = *co
	}
	if o3 != nil && *o3 > highest {
		highest = *o3
	}
	if no2 != nil && *no2 > highest {
		highest = *no2
	}
	if so2 != nil && *so2 > highest {
		highest = *so2
	}
	if pm10 != nil && *pm10 > highest {
		highest = *pm10
	}
	if pm25 != nil && *pm25 > highest {
		highest = *pm25
	}

	var aqi *int = nil

	// Only set AQI to non-nil value if the calculated max value is valid
	if highest > 0 {
		v := int(highest)
		aqi = &v
	}

	return aqiResult{
		IndexType: US,
		AQI:       aqi,
	}, nil
}
