package aqi

import (
	"fmt"

	"llif.org/wxcache/pkg/aqi/v2/breakpoint"
	"llif.org/wxcache/pkg/wxtypes"
)

type pollutantsEU struct {
	NO2  *float64 `validate:"omitnil,required,gte=0"`
	PM10 *float64 `validate:"omitnil,required,gte=0"`
	PM25 *float64 `validate:"omitnil,required,gte=0"`
	O3   *float64 `validate:"omitnil,required,gte=0"`
	SO2  *float64 `validate:"omitnil,required,gte=0"`
}

type transformerAQI_EU struct {
	pollutants pollutantsEU
}

func newTransformerAQI_EU(c AQIConcentration) transformerAQI_EU {
	return transformerAQI_EU{
		pollutants: pollutantsEU{
			O3:   c.O3,
			NO2:  c.NO2,
			SO2:  c.SO2,
			PM10: c.PM10,
			PM25: c.PM25,
		},
	}
}

func (t transformerAQI_EU) calculatePollutantAQI(pollutantType breakpoint.Type, f *float64) *float64 {
	if f == nil {
		return nil
	}

	var (
		pollutantBreakpointTable = breakpoint.GetPollutantBreakpointTable(breakpoint.EU, pollutantType)
		indexBreakpointTable     = breakpoint.GetIndexBreakpointTable(breakpoint.EU)
		foundIndex               = -1
	)

	for i, row := range pollutantBreakpointTable {
		if *f >= row.Low && *f <= row.High {
			foundIndex = i
			break
		}
	}

	if foundIndex == -1 {
		// @REVIEW: is it correct to assume a value which is >= max value in breakpoint table is in the highest category?
		foundIndex = len(pollutantBreakpointTable) - 1
	}

	// Does not matter if we return .Low or .High, they are the same value
	return &indexBreakpointTable[foundIndex].High
}

func (t transformerAQI_EU) CalculateAQI() (aqiResult, error) {
	if err := wxtypes.ValidateStruct[pollutantsEU](t.pollutants); err != nil {
		return aqiResult{}, fmt.Errorf("AQI concentrations is invalid: %s", err)
	}

	var (
		no2  = t.calculatePollutantAQI(breakpoint.NO2, t.pollutants.NO2)
		so2  = t.calculatePollutantAQI(breakpoint.SO2, t.pollutants.SO2)
		o3   = t.calculatePollutantAQI(breakpoint.O3, t.pollutants.O3)
		pm10 = t.calculatePollutantAQI(breakpoint.PM10, t.pollutants.PM10)
		pm25 = t.calculatePollutantAQI(breakpoint.PM25, t.pollutants.PM25)
	)

	// As per https://ecmwf-projects.github.io/copernicus-training-cams/proc-aq-index.html the AQI value is the highest AQ index from any of the pollutant.
	highest := -1.0
	if o3 != nil && *o3 > highest {
		highest = *o3
	}
	if no2 != nil && *no2 > highest {
		highest = *no2
	}
	if so2 != nil && *so2 > highest {
		highest = *so2
	}
	if pm10 != nil && *pm10 > highest {
		highest = *pm10
	}
	if pm25 != nil && *pm25 > highest {
		highest = *pm25
	}

	var aqi *int = nil

	// Only set AQI to non-nil value if the calculated max value is valid
	if highest > 0 {
		v := int(highest)
		aqi = &v
	}

	return aqiResult{
		AQI:       aqi,
		IndexType: EU,
	}, nil
}
