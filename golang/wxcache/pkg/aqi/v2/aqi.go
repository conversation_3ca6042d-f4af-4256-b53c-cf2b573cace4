package aqi

import (
	"errors"
	"log/slog"

	"llif.org/wxcache/pkg/wxtypes"
)

type IndexType string

const (
	EU IndexType = "eu"
	US IndexType = "us"
	GB IndexType = "gb"
)

type TransformerAQI interface {
	CalculateAQI(wxtypes.AirQualityV2) aqiResult
}

type aqiResult struct {
	AQI       *int
	IndexType IndexType
}

type AQI struct {
	USIndex *int
	GBIndex *int
	EUIndex *int
}

type AQIConcentration struct {
	NO2  *float64 // Nitrogen dioxide (μg/m3)
	PM10 *float64 // PM10 (μg/m3)
	O3   *float64 // Ozone (μg/m3)
	PM25 *float64 // PM25 (μg/m3)
	CO   *float64 // Carbon monoxide (μg/m3)
	SO2  *float64 // Sulfur dioxide (μg/m3)
}

func CalculateAQI(concentration AQIConcentration) (aqi AQI, err error) {
	usIndex, err := newTransformerAQI_US(concentration).CalculateAQI()
	if err != nil {
		slog.Warn("could not calculate US AQI", "err", err, "concentration", concentration)
	}
	gbIndex, err := newTransformerAQI_GB(concentration).CalculateAQI()
	if err != nil {
		slog.Warn("could not calculate GB AQI", "err", err, "concentration", concentration)
	}
	euIndex, err := newTransformerAQI_EU(concentration).CalculateAQI()
	if err != nil {
		slog.Warn("could not calculate EU AQI", "err", err, "concentration", concentration)
	}

	if usIndex.AQI == nil && gbIndex.AQI == nil && euIndex.AQI == nil {
		return aqi, errors.New("could not calculate any available AQI standard")
	}

	return AQI{
		USIndex: usIndex.AQI,
		GBIndex: gbIndex.AQI,
		EUIndex: euIndex.AQI,
	}, nil
}

// Concentration represents the various pollutants that contribute to AQI calculations
type Concentration struct {
	OZONE float64 `json:"OZONE"` // ppm
	PM25  float64 `json:"PM25"`  // μg/m^3
	PM10  float64 `json:"PM10"`  // μg/m^3
	CO    float64 `json:"CO"`    // ppm
	SO2   float64 `json:"SO2"`   // ppb
	NO2   float64 `json:"NO2"`   // ppb
	AQI   int     `json:"AQI"`   // index
}

// AirQuality has the calculated AQI score and the primary contributing pollutant
type AirQuality struct {
	Primary string `json:"DerivedPrimary"`
	AQI     int    `json:"DerivedAQI"`
}
