package breakpoint

type Type string
type Index string

const (
	O3   Type = "o3"
	PM25 Type = "pm25"
	PM10 Type = "pm10"
	CO   Type = "co"
	SO2  Type = "so2"
	NO2  Type = "no2"
)

const (
	US Index = "us"
	GB Index = "gb"
	EU Index = "eu"
)

type Boundary struct {
	Low  float64
	High float64
}

// GetPollutantBreakpointTable returns the proper boundary box for specified index (e.g. EU) and type (e.g. PM2.5)
func GetPollutantBreakpointTable(i Index, t Type) []Boundary {
	switch i {
	case US:
		return getBreakpoints_US(t)
	case EU:
		return getBreakpoints_EU(t)
	case GB:
		return getBreakpoints_GB(t)
	default:
		panic("GetBreakpointTable panic'd")
	}
}

// GetIndexBreakpointTable returns the categories of AQ indices for specified index (e.g. EU) sorted lowest to highest
func GetIndexBreakpointTable(i Index) []Boundary {
	switch i {
	case US:
		return getIndexBreakpointTable_US()
	case EU:
		return getIndexBreakpointTable_EU()
	case GB:
		return getIndexBreakpointTable_GB()
	default:
		panic("GetBreakpointTable panic'd")
	}
}
