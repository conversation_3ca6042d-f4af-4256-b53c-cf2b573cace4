package visualcrossing

// The system of units used for the output data. Supported values are us, uk, metric, and base.
type VisualCrossingUnitGroup string

const (
	VisualCrossingUnitGroupMetric   VisualCrossingUnitGroup = "metric"
	VisualCrossingUnitGroupImperial VisualCrossingUnitGroup = "us"
	VisualCrossingUnitGroupUK       VisualCrossingUnitGroup = "uk"
	VisualCrossingUnitGroupBase     VisualCrossingUnitGroup = "base"
)

type VisualCrossingWeatherIncludeParameter string

const (
	IncludeDays      VisualCrossingWeatherIncludeParameter = "days"      // Daily data
	IncludeHours     VisualCrossingWeatherIncludeParameter = "hours"     // Hourly data
	IncludeMinutes   VisualCrossingWeatherIncludeParameter = "minutes"   // Minute data (beta) See [Requesting sub-hourly data](https://www.visualcrossing.com/resources/documentation/weather-api/sub-hourly-data-in-the-timeline-weather-api/)
	IncludeAlerts    VisualCrossingWeatherIncludeParameter = "alerts"    // Weather alerts
	IncludeCurrent   VisualCrossingWeatherIncludeParameter = "current"   // Current conditions or conditions at requested time
	IncludeEvents    VisualCrossingWeatherIncludeParameter = "events"    // [historical events such as a hail, tornadoes, wind damage and earthquakes](https://www.visualcrossing.com/resources/documentation/weather-101/how-to-retrieve-hail-tornado-wind-damage-and-earthquakes-events-in-the-weather-api/) (not enabled by default)
	IncludeObs       VisualCrossingWeatherIncludeParameter = "obs"       // Historical observations from weather stations
	IncludeRemote    VisualCrossingWeatherIncludeParameter = "remote"    // Historical observations from remote source such as satellite or radar
	IncludeFcst      VisualCrossingWeatherIncludeParameter = "fcst"      // Forecast based on 16 day models
	IncludeStats     VisualCrossingWeatherIncludeParameter = "stats"     // Historical statistical normals and daily statistical forecast
	IncludeStatsFcst VisualCrossingWeatherIncludeParameter = "statsfcst" // Use the full statistical forecast information for dates in the future beyond the current model forecast. Permits hourly statistical forecast
)

type VisualCrossingWeatherElement string

const (
	ElementCloudCover     VisualCrossingWeatherElement = "cloudcover"     // How much of the sky is covered in cloud ranging from 0-100%
	ElementConditions     VisualCrossingWeatherElement = "conditions"     // Textual representation of the weather conditions
	ElementDescription    VisualCrossingWeatherElement = "description"    // Longer text descriptions suitable for displaying in weather displays
	ElementIcon           VisualCrossingWeatherElement = "icon"           // A fixed, machine readable summary that can be used to display an icon
	ElementDatetime       VisualCrossingWeatherElement = "datetime"       // ISO 8601 formatted date, time or datetime value in local time zone
	ElementDatetimeEpoch  VisualCrossingWeatherElement = "datetimeEpoch"  // Number of seconds since 1st January 1970 in UTC time
	ElementTZOffset       VisualCrossingWeatherElement = "tzoffset"       // The time zone offset in hours
	ElementDew            VisualCrossingWeatherElement = "dew"            // Dew point temperature
	ElementFeelsLike      VisualCrossingWeatherElement = "feelslike"      // What the temperature feels like accounting for heat index or wind chill
	ElementFeelsLikeMax   VisualCrossingWeatherElement = "feelslikemax"   // Maximum feels like temperature at the location (day only)
	ElementFeelsLikeMin   VisualCrossingWeatherElement = "feelslikemin"   // Minimum feels like temperature at the location (day only)
	ElementTemp           VisualCrossingWeatherElement = "temp"           // Temperature at the location, daily values are average values
	ElementTempMax        VisualCrossingWeatherElement = "tempmax"        // Maximum temperature at the location (day only)
	ElementTempMin        VisualCrossingWeatherElement = "tempmin"        // Minimum temperature at the location (day only)
	ElementHumidity       VisualCrossingWeatherElement = "humidity"       // Relative humidity in %
	ElementPrecip         VisualCrossingWeatherElement = "precip"         // Amount of liquid precipitation that fell or is predicted to fall
	ElementPrecipCover    VisualCrossingWeatherElement = "precipcover"    // Proportion of hours where there was non-zero precipitation (days only)
	ElementPrecipProb     VisualCrossingWeatherElement = "precipprob"     // Likelihood of measurable precipitation ranging from 0% to 100% (forecast only)
	ElementPrecipType     VisualCrossingWeatherElement = "preciptype"     // Array indicating the type(s) of precipitation expected or that occurred
	ElementSnow           VisualCrossingWeatherElement = "snow"           // Amount of snow that fell or is predicted to fall
	ElementSnowDepth      VisualCrossingWeatherElement = "snowdepth"      // Depth of snow on the ground
	ElementPressure       VisualCrossingWeatherElement = "pressure"       // Sea level atmospheric or barometric pressure in millibars (or hectopascals)
	ElementSource         VisualCrossingWeatherElement = "source"         // Type of weather data used for this weather object
	ElementStations       VisualCrossingWeatherElement = "stations"       // Weather stations used when collecting historical observation record
	ElementSunrise        VisualCrossingWeatherElement = "sunrise"        // Formatted time of the sunrise (day only)
	ElementSunriseEpoch   VisualCrossingWeatherElement = "sunriseEpoch"   // Sunrise time specified as seconds since 1st January 1970 in UTC time
	ElementSunset         VisualCrossingWeatherElement = "sunset"         // Formatted time of the sunset (day only)
	ElementSunsetEpoch    VisualCrossingWeatherElement = "sunsetEpoch"    // Sunset time specified as seconds since 1st January 1970 in UTC time
	ElementMoonPhase      VisualCrossingWeatherElement = "moonphase"      // Fractional portion through the current moon lunation cycle (0=new moon, 0.5=full moon)
	ElementMoonrise       VisualCrossingWeatherElement = "moonrise"       // Formatted time of the moonrise (day only, optional)
	ElementMoonriseEpoch  VisualCrossingWeatherElement = "moonriseEpoch"  // Moonrise time specified as seconds since 1st January 1970 in UTC time (day only, optional)
	ElementMoonset        VisualCrossingWeatherElement = "moonset"        // Formatted time of the moonset (day only, optional)
	ElementMoonsetEpoch   VisualCrossingWeatherElement = "moonsetEpoch"   // Moonset time specified as seconds since 1st January 1970 in UTC time (day only, optional)
	ElementUVIndex        VisualCrossingWeatherElement = "uvindex"        // Value between 0 and 10 indicating the level of UV exposure
	ElementUVIndex2       VisualCrossingWeatherElement = "uvindex2"       // Alternative UV index using US National Weather Service algorithms (optional, 5 day forecast only)
	ElementVisibility     VisualCrossingWeatherElement = "visibility"     // Distance at which distant objects are visible
	ElementWindDir        VisualCrossingWeatherElement = "winddir"        // Direction from which the wind is blowing
	ElementWindGust       VisualCrossingWeatherElement = "windgust"       // Instantaneous wind speed at a location
	ElementWindSpeed      VisualCrossingWeatherElement = "windspeed"      // Sustained wind speed measured as average over preceding 1-2 minutes
	ElementWindSpeedMax   VisualCrossingWeatherElement = "windspeedmax"   // Maximum wind speed over the day (day only, optional)
	ElementWindSpeedMean  VisualCrossingWeatherElement = "windspeedmean"  // Average (mean) wind speed over the day (day only, optional)
	ElementWindSpeedMin   VisualCrossingWeatherElement = "windspeedmin"   // Minimum wind speed over the day (day only, optional)
	ElementSolarRadiation VisualCrossingWeatherElement = "solarradiation" // Solar radiation power (W/m2) at the instantaneous moment
	ElementSolarEnergy    VisualCrossingWeatherElement = "solarenergy"    // Total energy from the sun (MJ/m2) that builds up over an hour or day
	ElementSevereRisk     VisualCrossingWeatherElement = "severerisk"     // Value between 0 and 100 representing risk of convective storms (forecast only)
	ElementCAPE           VisualCrossingWeatherElement = "cape"           // Convective available potential energy indicating energy for thunderstorms (forecast only)
	ElementCIN            VisualCrossingWeatherElement = "cin"            // Convective inhibition representing atmospheric tendency to prevent thunderstorms (forecast only)
	ElementDegreeDays     VisualCrossingWeatherElement = "degreedays"     // Number of degree days for this date (day only)
	ElementAccDegreeDays  VisualCrossingWeatherElement = "accdegreedays"  // Accumulated degree days
	ElementHours          VisualCrossingWeatherElement = "hours"          // Array of hourly weather data objects (child of daily weather object)
	ElementNormal         VisualCrossingWeatherElement = "normal"         // Array of normal weather data values representing min, mean, and max values
	ElementOffsetSeconds  VisualCrossingWeatherElement = "offsetseconds"  // Time zone offset for this weather data object in seconds
)

type VisualCrossingTimezone string

const (
	TimezoneUTC VisualCrossingTimezone = "Z"
)

type VisualCrossingWeatherRequestOpts struct {
	Location    string                                   `path:"location"`   // Location is the address, partial address or latitude,longitude location for which to retrieve weather data. You can also use US ZIP Codes.
	Date1       string                                   `path:"date1"`      // Date1 is the start date for which to retrieve weather data. If a date2 value is also given, then it represents the first date for which to retrieve weather data. If no date2 is specified then weather data for a single day is retrieved.
	Date2       string                                   `path:"date2"`      // Date2 is the end date for which to retrieve weather data. This value may only be used when a date1 value is given. When both date1 and date2 values are given, the query is inclusive of date2 and the weather data request period will end on midnight of the date2 value.
	Key         string                                   `url:"key"`         // Key is the API key to use for the request.
	UnitGroup   VisualCrossingUnitGroup                  `url:"unitGroup"`   // The system of units used for the output data. Supported values are us, uk, metric, and base.
	Lang        string                                   `url:"lang"`        // The language in which to return the weather data. Supported values are ar, bg, cs, da, de, el, en, es, fa, fi, fr, he, hu, it, ja, ko, nl, pl, pt, ru, sk, sr, sv, tr, uk, vi, zh.
	Include     *[]VisualCrossingWeatherIncludeParameter `url:"include"`     // Specifies the sections you would like to include in the result data. This allows you to reduce query cost and latency
	Elements    *string                                  `url:"elements"`    // Specifies the elements you would like to include in the result data. This allows you to reduce query cost and latency
	ContentType string                                   `url:"contentType"` // Specifies the format of the response data. Supported values are json and csv.
	Timezone    *VisualCrossingTimezone                  `url:"timezone"`    // Specifies the timezone of the input and result dates and times. When not specified, all date times are considered local times. If you would like to specify that all dates are entered as UTC dates and times, use timezone=Z parameter.
	MaxDistance *string                                  `url:"maxdistance"` // The maximum distance in meters used to search for local weather stations ( By default, 50 miles or approximately 80km (80,467m). This setting is combined with the maxStations parameter to find local weather stations.
	MaxStations *int                                     `url:"maxstations"` // The maximum number of weather stations to return. By default, 10 stations are returned.
	// @TODO: Support options, iconSet, elevationDifference, locationNames, forecastBasisDate, forecastBasisDay,
	// degreeDayTempFix, degreeDayStartDate, degreeDayTempMaxThreshold, degreeDayTempBase, degreeDayInverse, degreeDayMethod
}
