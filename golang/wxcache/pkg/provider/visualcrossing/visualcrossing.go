package visualcrossing

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"strings"
	"time"

	"golang.org/x/time/rate"
	"llif.org/wxcache/internal/config"
	"llif.org/wxcache/internal/httpclient"
	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/wxtypes"
)

type VisualCrossing struct {
	httpclient *httpclient.HttpClient
	apiKey     string
	radius     string
}

func Init(c *config.Config) *VisualCrossing {
	var (
		maxRequestsPerDay = c.RateLimit.VisualCrossing
		rateLimit         = rate.Every(time.Duration(24 * int(time.Hour) / maxRequestsPerDay))

		limiter = rate.NewLimiter(rateLimit, maxRequestsPerDay)
		client  = httpclient.NewHttpClient(limiter)
	)
	return &VisualCrossing{
		httpclient: client,
		apiKey:     c.APIKey.VisualCrossing,
		radius:     c.Radius,
	}
}

func (v *VisualCrossing) String() string {
	return "visualcrossing"
}

func (v *VisualCrossing) GetAirQuality(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.AirQualityV2, errBuckets []wxtypes.SpaceTime) {
	slog.ErrorContext(ctx, "visualcrossing.GetAirQuality not implemented")
	return nil, spacetime
}

// GetWeather resolves given space time and returns the available data as well as a slice of space time it could not resolve
func (v *VisualCrossing) GetWeather(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.WeatherV2, errBuckets []wxtypes.SpaceTime) {
	for _, st := range spacetime {
		res, err := v.getWeather(ctx, st)
		if err != nil {
			slog.ErrorContext(ctx, "error fetching weather", "st", st, "err", err, "provider", v.String())
		}
		if res == nil {
			errBuckets = append(errBuckets, st)
			continue
		}
		normalised, err := normaliseWeather(ctx, *res)
		if err != nil {
			slog.ErrorContext(ctx, "error normalising weather", "err", err, "provider", v.String())
			errBuckets = append(errBuckets, st)
			continue
		}
		result = append(result, normalised...)
	}
	return
}

func (v *VisualCrossing) GetPollen(ctx context.Context, spacetime []wxtypes.SpaceTime) (result []wxtypes.PollenV2, errBuckets []wxtypes.SpaceTime) {
	slog.ErrorContext(ctx, "visualcrossing.GetPollen not implemented")
	return nil, spacetime
}

func (v *VisualCrossing) getWeather(ctx context.Context, st wxtypes.SpaceTime) (res *VisualCrossingWeatherResponse, err error) {
	var (
		startDate = st.TimeFrom.UTC().Format("2006-01-02")
		endDate   = st.TimeTo.UTC().Format("2006-01-02")
	)

	visualCrossingElements := []VisualCrossingWeatherElement{
		ElementTemp,
		ElementWindSpeed,
		ElementWindGust,
		ElementWindDir,
		ElementPrecip,
		ElementCloudCover,
		ElementUVIndex,
		ElementPressure,
		ElementVisibility,
		ElementPrecipProb,
		ElementFeelsLike,
		ElementHumidity,
		ElementDatetime,
		ElementDatetimeEpoch,
		ElementTZOffset,
	}

	// We have to make the elements a single string separated by commas, because the API expects a "&elements=temp,wind" rather than "&elements=temp&elements=wind"
	elements := make([]string, len(visualCrossingElements))
	for i, e := range visualCrossingElements {
		elements[i] = string(e)
	}
	elementsStr := strings.Join(elements, ",")

	latlong := fmt.Sprintf("%f,%f", st.Lat, st.Long)
	opts := VisualCrossingWeatherRequestOpts{
		UnitGroup:   "metric",
		Elements:    &elementsStr,
		Key:         v.apiKey,
		ContentType: "json",
	}

	body, err := v.getFromVisualCrossing(wxtypes.Weather, latlong, startDate, endDate, opts)
	if err != nil {
		return nil, err
	}

	if err = json.Unmarshal(body, &res); err != nil {
		return nil, err
	}
	if err = wxtypes.ValidateStruct(res); err != nil {
		return nil, err
	}
	// If data returned are from sensor out of radius, skip it
	isInRadius := geo.IsWithinRadius(st.Lat, st.Long, res.Latitude, res.Longitude, v.radius)
	if !isInRadius {
		slog.InfoContext(ctx, "provider out of radius", "provider", v.String(), "lat", st.Lat, "long", st.Long, "radius", v.radius)
		return nil, nil
	}
	return res, nil
}

func (v *VisualCrossing) getFromVisualCrossing(t wxtypes.RequestType, location, date1, date2 string, opts any) (r []byte, err error) {
	err = wxtypes.ValidateStruct(opts)
	if err != nil {
		return
	}
	endpoint := fmt.Sprintf("%s%s/%s/%s", v.getEndpointFromRequestType(t), location, date1, date2)
	req, err := httpclient.BuildRequest(v.getHostFromRequestType(t), endpoint, opts)
	if err != nil {
		return
	}
	response, err := v.httpclient.Do(req)
	if err != nil {
		return
	}
	defer response.Body.Close()

	r, err = io.ReadAll(response.Body)
	if err != nil {
		return
	}
	if response.StatusCode/100 != 2 {
		err = fmt.Errorf("received non 2xx HTTP response code=%d, body=%s", response.StatusCode, string(r))
		return
	}
	return r, nil
}

func (v *VisualCrossing) getEndpointFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.Weather:
		return "/VisualCrossingWebServices/rest/services/timeline/"
	default:
		slog.Error("unsupported request type", "provider", v.String(), "request_type", t.String())
		return ""
	}
}

func (v *VisualCrossing) getHostFromRequestType(t wxtypes.RequestType) string {
	switch t {
	case wxtypes.Weather:
		return "weather.visualcrossing.com"
	default:
		slog.Error("unsupported host address for request type", "provider", v.String(), "request_type", t.String())
		return ""
	}
}
