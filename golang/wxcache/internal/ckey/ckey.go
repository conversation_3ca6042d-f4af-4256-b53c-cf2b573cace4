package ckey

type ContextKey int

const (
	RuntimeHTTP    string = "http"
	RuntimeMsgProc string = "msgproc"
)

func (k ContextKey) String() string {
	return [...]string{"", "request_id", "runtime"}[k]
}

const (
	// RequestID identifies a single request across the whole call-stack.
	// It should only be set at the uppermost level of call. Usually the API layer.
	RequestID ContextKey = iota + 1
	Runtime   ContextKey = iota + 1
)

func AllKeys() []ContextKey {
	return []ContextKey{RequestID, Runtime}
}
