package httpclient

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/google/go-querystring/query"
)

func BuildRequest(host, endpoint string, opts any) (*http.Request, error) {
	if host == "" || endpoint == "" {
		return nil, fmt.Errorf("invalid values for host=%s endpoint=%s", host, endpoint)
	}

	u := url.URL{
		Scheme: "https",
		Host:   host,
		Path:   endpoint,
	}
	r, err := http.NewRequest(http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, err
	}

	// Assume all communication is done via JSON
	r.Header.Add("Accept", "application/json")

	query, err := serialiseOptsToParams(opts)
	if err != nil {
		return nil, err
	}
	r.URL.RawQuery = query
	return r, nil
}

func serialiseOptsToParams(opts any) (string, error) {
	if opts == nil {
		return "", nil
	}

	values, err := query.Values(opts)
	if err != nil {
		return "", err
	}
	for k, v := range values {
		// Check if the slice only contains a single empty string
		// Which means the param is empty
		if len(v) == 1 && v[0] == "" {
			values.Del(k)
			continue
		}
	}
	return values.Encode(), nil
}
