package msgproc

import (
	"errors"

	llifaws "llif.org/wxcache/pkg/aws"
)

// A helper function which takes in a map of message attributes and returns the Value name of the first element.
//
// This is only an ad-hoc solution to the problem of the way we structure our messages, where we cannot know
// beforehand what the type of the message is without assuming the type from the message attributes.
func getMessageAttributeValue(attr map[string]llifaws.SQSMessageBodyAttribute) (string, error) {
	for _, v := range attr {
		return v.Value, nil
	}
	return "", errors.New("no values in message attributes")
}
