package handler_test

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/msgproc"
	"llif.org/wxcache/internal/msgproc/handler"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestHandlersFetchRealDataShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc               = service.NewEnvironmentService(c)
		airQualityHandler = handler.NewAirQualityMessageHandler(c.<PERSON>, svc)
		weatherHandler    = handler.NewWeatherMessageHandler(c.<PERSON>, svc)
		pollenHandler     = handler.NewPollenMessageHandler(c.<PERSON><PERSON>, svc)

		timeNow    = time.Now().UTC()
		timeDayAgo = time.Now().UTC().Add(-24 * time.Hour)

		ctx = context.Background()
	)
	t.Run("Air Quality", func(t *testing.T) {
		for _, loc := range location.GetAll() {
			m := handler.LocationLoadedMessage{
				UserUUID: "123",
				TimeFrom: timeDayAgo,
				TimeTo:   timeNow,
				Lat:      loc.Lat,
				Long:     loc.Long,
			}
			b, _ := json.Marshal(m)
			err := airQualityHandler.Handle(ctx, b)
			require.NoError(t, err)
		}
	})

	t.Run("Weather", func(t *testing.T) {
		for _, loc := range location.GetAll() {
			m := handler.LocationLoadedMessage{
				UserUUID: "123",
				TimeFrom: timeDayAgo,
				TimeTo:   timeNow,
				Lat:      loc.Lat,
				Long:     loc.Long,
			}
			b, _ := json.Marshal(m)
			err := weatherHandler.Handle(ctx, b)
			require.NoError(t, err)
		}
	})

	t.Run("Pollen", func(t *testing.T) {
		for _, loc := range location.GetAll() {
			m := handler.LocationLoadedMessage{
				UserUUID: "123",
				TimeFrom: timeDayAgo,
				TimeTo:   timeNow,
				Lat:      loc.Lat,
				Long:     loc.Long,
			}
			b, _ := json.Marshal(m)
			err := pollenHandler.Handle(ctx, b)
			require.NoError(t, err)
		}
	})
}

func TestHandlersShouldPass(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc               = &HandlerTestService{}
		ctx               = context.Background()
		airQualityHandler = handler.NewAirQualityMessageHandler(c.Logger, svc)
		weatherHandler    = handler.NewWeatherMessageHandler(c.Logger, svc)
		pollenHandler     = handler.NewPollenMessageHandler(c.Logger, svc)
		locationMessage   = helperPrepareLocationLoadedMessageWithAllFields()
	)

	tests := []struct {
		handler msgproc.MessageHandler
		message []byte
	}{
		{
			handler: airQualityHandler,
			message: locationMessage,
		},
		{
			handler: weatherHandler,
			message: locationMessage,
		},
		{
			handler: pollenHandler,
			message: locationMessage,
		},
	}

	for _, tt := range tests {
		err := tt.handler.Handle(ctx, tt.message)
		require.NoError(t, err)
	}
}

func TestHandlersHandleOptionalTimeFromInMessage(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc             = &HandlerTestService{}
		ctx             = context.Background()
		aqHandler       = handler.NewAirQualityMessageHandler(c.Logger, svc)
		weatherHandler  = handler.NewWeatherMessageHandler(c.Logger, svc)
		pollenHandler   = handler.NewPollenMessageHandler(c.Logger, svc)
		locationMessage = helperPrepareLocationLoadedMessageEmptyTimeToField()
	)

	tests := []struct {
		handler msgproc.MessageHandler
		message []byte
	}{
		{
			handler: aqHandler,
			message: locationMessage,
		},
		{
			handler: weatherHandler,
			message: locationMessage,
		},
		{
			handler: pollenHandler,
			message: locationMessage,
		},
	}

	for _, tt := range tests {
		err := tt.handler.Handle(ctx, tt.message)
		require.NoError(t, err)
	}
}

// These test will fail once we refactor the behaviour of msgproc to retry messages again.
// The tests should change accordingly afterwards

func TestHandlersShouldReturnNilOnError(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc             = &HandlerTestServiceReturnsError{}
		ctx             = context.Background()
		aqHandler       = handler.NewAirQualityMessageHandler(c.Logger, svc)
		weatherHandler  = handler.NewWeatherMessageHandler(c.Logger, svc)
		pollenHandler   = handler.NewPollenMessageHandler(c.Logger, svc)
		locationMessage = helperPrepareLocationLoadedMessageEmptyTimeToField()
	)

	tests := []struct {
		handler msgproc.MessageHandler
		message []byte
	}{
		{
			handler: aqHandler,
			message: locationMessage,
		},
		{
			handler: weatherHandler,
			message: locationMessage,
		},
		{
			handler: pollenHandler,
			message: locationMessage,
		},
	}

	for _, tt := range tests {
		err := tt.handler.Handle(ctx, tt.message)
		require.NoError(t, err)
	}
}

func TestHandlersFailValidationOfIncomingMessageShouldPass(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc            = &HandlerTestServiceReturnsError{}
		ctx            = context.Background()
		aqHandler      = handler.NewAirQualityMessageHandler(c.Logger, svc)
		weatherHandler = handler.NewWeatherMessageHandler(c.Logger, svc)
		pollenHandler  = handler.NewPollenMessageHandler(c.Logger, svc)
	)

	// Empty message should not pass validation on required fields
	locationLoadedMessage := handler.LocationLoadedMessage{}
	locationMessage, _ := json.Marshal(locationLoadedMessage)

	tests := []struct {
		handler msgproc.MessageHandler
		message []byte
	}{
		{
			handler: aqHandler,
			message: locationMessage,
		},
		{
			handler: weatherHandler,
			message: locationMessage,
		},
		{
			handler: pollenHandler,
			message: locationMessage,
		},
	}

	for _, tt := range tests {
		err := tt.handler.Handle(ctx, tt.message)
		require.Error(t, err)
	}
}

// Type used to test how the messaging handler reacts to the service returning nil, nil
type HandlerTestService struct{}

func (s *HandlerTestService) GetAirQuality(context.Context, []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	return nil, nil
}

func (s *HandlerTestService) GetAirQualityForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	return nil, nil
}

func (s *HandlerTestService) GetWeather(context.Context, []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	return nil, nil
}

func (s *HandlerTestService) GetWeatherForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	return nil, nil
}

func (s *HandlerTestService) GetPollen(context.Context, []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	return nil, nil
}

func (s *HandlerTestService) GetPollenForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	return nil, nil
}

// Type used to test how the messaging handler reacts to the service returning nil, error
type HandlerTestServiceReturnsError struct{}

func (s *HandlerTestServiceReturnsError) GetAirQuality(context.Context, []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	return nil, errors.New("aq raised error")
}

func (s *HandlerTestServiceReturnsError) GetAirQualityForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.AirQualityV2, error) {
	return nil, errors.New("forecast aq raised error")
}

func (s *HandlerTestServiceReturnsError) GetWeather(context.Context, []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	return nil, errors.New("weather raised error")
}

func (s *HandlerTestServiceReturnsError) GetWeatherForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.WeatherV2, error) {
	return nil, errors.New("forecast weather raised error")
}

func (s *HandlerTestServiceReturnsError) GetPollen(context.Context, []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	return nil, errors.New("pollen raised error")
}

func (s *HandlerTestServiceReturnsError) GetPollenForecast(context.Context, []wxtypes.SpaceTime) ([]wxtypes.PollenV2, error) {
	return nil, errors.New("forecast pollen raised error")
}

func helperPrepareLocationLoadedMessageWithAllFields() []byte {
	timeFrom, err := time.Parse("2006-01-02T15:04:05Z", "2023-01-02T00:00:00Z")
	if err != nil {
		panic(err)
	}
	timeTo, err := time.Parse("2006-01-02T15:04:05Z", "2023-01-03T00:00:00Z")
	if err != nil {
		panic(err)
	}

	lat, long := 32.716736, -117.161087

	v := handler.LocationLoadedMessage{
		UserUUID: "1",
		TimeFrom: timeFrom,
		TimeTo:   timeTo,
		Lat:      lat,
		Long:     long,
	}
	b, _ := json.Marshal(v)
	return b
}

func helperPrepareLocationLoadedMessageEmptyTimeToField() []byte {
	timeFrom, err := time.Parse("2006-01-02T15:04:05Z", "2023-01-02T00:00:00Z")
	if err != nil {
		panic(err)
	}

	lat, long := 32.716736, -117.161087

	v := handler.LocationLoadedMessage{
		UserUUID: "1",
		TimeFrom: timeFrom,
		Lat:      lat,
		Long:     long,
	}
	b, _ := json.Marshal(v)
	return b
}
