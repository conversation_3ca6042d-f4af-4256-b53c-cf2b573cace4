package config

import (
	"log/slog"

	"llif.org/wxcache/internal/config/database"
)

// BaseConfig holds values which are generic to the whole environment and are not dependant on any specific part of the service
type BaseConfig struct {
	// LogLevel sets the minimum required log level for a log to be written
	LogLevel string `env:"LOG_LEVEL" envDefault:"INFO"`

	// RunEnv holds the name of the environment the service is running in [local, dev, staging, prod]
	RunEnv string `env:"RUN_ENV" envDefault:"local" validate:"required"`

	// AppVersion represnts the current version of the service
	AppVersion string `env:"APP_VERSION"`

	// Radius represents the radius in which to search for data by default
	Radius string `env:"RADIUS" envDefault:"10km" validate:"required"`

	// Whether or not the service is running in a containerized environment
	IsContainerized bool `env:"IS_CONTAINERIZED" envDefault:"false"`
}

// SentryConfig holds values required to setup Sentry logging
type SentryConfig struct {
	// DSN is the URI necessary to connect to Sentry
	Dsn string `env:"SENTRY_DSN"`

	// Whether or not to print out Sentry internal logging to stdout
	Debug bool `env:"SENTRY_DEBUG"`

	// The sample rate for sampling traces in the range [0.0, 1.0]
	TracesSampleRate float64 `env:"TELEMETRY_TRACES_SAMPLE_RATE"`
}

// RateLimitConfig holds rate limit values for each supported provider in a Requests Per Day format (RPD)
type RateLimitConfig struct {
	// Ambee's requests per day limit
	Ambee int `env:"AMBEE_RATELIMIT" envDefault:"1000"`

	// WeatherAPI's requests per day limit
	WeatherAPI int `env:"WEATHERAPI_RATELIMIT" envDefault:"1000"`

	// OpenAQ's requests per day limit
	OpenAQ int `env:"OPENAQ_RATELIMIT" envDefault:"1000"`

	// OpenMeteo's requests per day limit
	OpenMeteo int `env:"OPENMETEO_RATELIMIT" envDefault:"1000"`

	// VisualCrossing's requests per day limit
	VisualCrossing int `env:"VISUALCROSSING_RATELIMIT" envDefault:"100000"`
}

// AWSConfig holds values to initialise and properly setup AWS services
type AWSConfig struct {
	// AWS region where the services are registered, for example us-east-1
	Region string `env:"AWS_REGION_NAME" envDefault:"us-east-1" validate:"required"`

	// The URL endpoint for connecting to AWS. Can be overriden to connect to a local cloud LocalStack
	Endpoint string `env:"AWS_URL"`

	// The URL endpoint for connecting to AWS when running locally
	EndpointLocal string `env:"AWS_URL_LOCAL" envDefault:"http://localhost:4566"`
}

// MessagingConfig holds values for setting up asynchronous messaging services like AWS SQS
type MessagingConfig struct {
	// MessagingPrefix specifies what prefix should resource names have to identify the environment
	MessagingPrefix string `env:"ENVIRONMENT_PREFIX" envDefault:"local_" validate:"required"`
}

// ServerConfig holds values required to setup endpoints and entrypoints to the service
type ServerConfig struct {
	// What port to run the HTTP server on
	HTTPPort string `env:"HTTP_PORT" envDefault:":8006" validate:"required"`

	// Whether or not should providers mock calls to the 3rd party client and instead rely on testing cassettes.
	// Slowly getting deprecated, do not rely on this in new code.
	MockHTTP bool
}

// ProviderAPIKey holds 3rd party API keys necessary to make requests to the providers
type ProviderAPIKey struct {
	// Ambee's API key
	Ambee string `env:"AMBEE_APIKEY"`

	// WeatherAPI's API key
	WeatherAPI string `env:"WAPI_KEY"`

	// VisualCrossing's API key
	VisualCrossing string `env:"VISUALCROSSING_APIKEY"`
}

// Config holds every subconfig and setup necessary to run the whole environment, including database connections
type Config struct {
	BaseConfig

	*slog.Logger     `validate:"required"`
	*database.Config `validate:"required"`

	AWS       AWSConfig
	Server    ServerConfig
	Sentry    SentryConfig
	Messaging MessagingConfig
	RateLimit RateLimitConfig
	APIKey    ProviderAPIKey
}
