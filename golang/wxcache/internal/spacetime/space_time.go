package spacetime

import (
	"fmt"
	"log/slog"
	"sort"
	"time"

	"llif.org/wxcache/pkg/geo"
	"llif.org/wxcache/pkg/wxtypes"
)

// IsWithinCurrentTime validates that the input SpaceTime is a "current" time given a time delta
func IsWithinCurrentTime(st wxtypes.SpaceTime, delta time.Duration) bool {
	currTime := time.Now().UTC()
	dt := currTime.Add(-delta)
	return st.TimeFrom.After(dt)
}

// ValidateSpaceTimeOrder validates that TimeFrom is not after TimeTo for each SpaceTime in the slice.
// Returns an error if any SpaceTime has TimeFrom older than TimeTo.
func ValidateSpaceTimeOrder(st []wxtypes.SpaceTime) error {
	for i, spacetime := range st {
		if spacetime.TimeFrom.After(spacetime.TimeTo) {
			return fmt.Errorf("invalid time range at index %d: TimeFrom (%s) cannot be after TimeTo (%s)",
				i, spacetime.TimeFrom.Format(time.RFC3339), spacetime.TimeTo.Format(time.RFC3339))
		}
	}
	return nil
}

// GetCurrentAndHistoricalSpaceTimeCoordinates takes in a slice of SpaceTime and returns two slices:
// 1. A slice of SpaceTime that is within the last hour
// 2. A slice of SpaceTime that is outside the last hour
func GetCurrentAndHistoricalSpaceTimeCoordinates(sts []wxtypes.SpaceTime) ([]wxtypes.SpaceTime, []wxtypes.SpaceTime) {
	curr := make([]wxtypes.SpaceTime, 0)
	historical := make([]wxtypes.SpaceTime, 0)

	for _, spaceTime := range sts {
		if IsWithinCurrentTime(spaceTime, time.Hour) {
			curr = append(curr, spaceTime)
			continue
		}
		historical = append(historical, spaceTime)
	}
	return curr, historical
}

// BucketSpaceTimeWithDelta takes in an input of wxtypes.SpaceTime objects, along with time.Duration, to
// transform the given input into an equally spaced out buckets of length delta, which is then returned as a slice.
//
// If the time duration between TimeFrom and TimeTo is less than or equal to the delta, the original wxtypes.SpaceTime is kept
func BucketSpaceTimeWithDelta(data []wxtypes.SpaceTime, delta time.Duration) (st []wxtypes.SpaceTime) {
	if len(data) == 0 {
		return []wxtypes.SpaceTime{}
	}

	for _, v := range data {
		if v.TimeTo.Sub(v.TimeFrom) <= delta {
			// Skip if the time bucket is within the given delta of time
			st = append(st, v)
			continue
		}

		// While t is before v.TimeTo, iterate over t and add delta time
		for t := v.TimeFrom.Truncate(time.Hour); t.Before(v.TimeTo); t = t.Add(delta) {
			nextT := t.Add(delta)
			// Skip creating a bucket if it would be empty (start time equals end time)
			if nextT.After(v.TimeTo) {
				nextT = v.TimeTo
			}
			if t.Equal(nextT) {
				continue
			}
			spaceTime := wxtypes.SpaceTime{
				Lat:      v.Lat,
				Long:     v.Long,
				TimeFrom: t,
				TimeTo:   nextT,
			}
			st = append(st, spaceTime)
		}
	}

	// Make sure the beginning timestamp
	// and ending timestamp are aligned with the input
	if len(st) > 0 {
		st[0].TimeFrom = data[0].TimeFrom
		st[len(st)-1].TimeTo = data[len(data)-1].TimeTo
	}
	return st
}

// bucketSpaceTimeToEurope takes in a single slice of wxtypes.SpaceTime and sorts them into two separate buckets.
// First bucket contains data with European coordinates, while the second bucket contains the rest of the world.
func BucketSpaceTimeToEurope(data []wxtypes.SpaceTime) (eu []wxtypes.SpaceTime, world []wxtypes.SpaceTime) {
	for _, st := range data {
		ok, err := geo.CoordinatesInContinent(geo.Europe, st.Lat, st.Long)
		if err != nil {
			slog.Warn("failed to geocode coordinates to Europe", "lat", st.Lat, "lon", st.Long, "err", err)
			continue
		}

		if ok {
			eu = append(eu, st)
		} else {
			world = append(world, st)
		}
	}
	return
}

// BucketSpaceTimeIntoDailyBuckets takes in a slice of wxtypes.SpaceTime and turns them into daily calendar buckets.
// It is not the same as bucketing into 24 hour buckets.
func BucketSpaceTimeIntoDailyBuckets(spacetime []wxtypes.SpaceTime) []wxtypes.SpaceTime {
	if len(spacetime) == 0 {
		return spacetime
	}

	var days = make(map[string]wxtypes.SpaceTime)
	for _, st := range spacetime {
		timeFrom := st.TimeFrom.Format("2006-01-02")
		day, ok := days[timeFrom]
		if !ok {
			days[timeFrom] = wxtypes.SpaceTime{
				Lat:      st.Lat,
				Long:     st.Long,
				TimeFrom: st.TimeFrom,
				TimeTo:   st.TimeTo,
			}
		} else {
			if st.TimeFrom.Before(day.TimeFrom) {
				day.TimeFrom = st.TimeFrom
			}
			if st.TimeTo.After(day.TimeTo) {
				day.TimeTo = st.TimeTo
			}
			days[timeFrom] = day
		}
	}

	var result = make([]wxtypes.SpaceTime, 0)
	for _, v := range days {
		result = append(result, v)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].TimeFrom.Before(result[j].TimeFrom)
	})
	return result
}

// SpreadToDailyTimeBuckets takes in a time range and returns a slice of each individual day within the range.
//
// Example: startTime = 2024-01-01, endTime = 2024-01-07, the function returns the following slice:
// 2024-01-01, 2024-01-02, 2024-01-03, ..., 2024-01-07
func SpreadToDailyTimeBuckets(startTime, endTime time.Time) []time.Time {
	var (
		timeDifference = endTime.Sub(startTime)
		dayCount       = timeDifference / time.Hour / 24

		result = make([]time.Time, 0, dayCount+1)
	)

	// Remove miliseconds and monotonic clocks [https://stackoverflow.com/a/54735184]
	startTime = startTime.Truncate(time.Second)
	endTime = endTime.Truncate(time.Second)

	for t := startTime; t.Before(endTime) || t.Equal(endTime); t = t.Add(time.Hour * 24) {
		result = append(result, t)
	}
	return result
}

// BucketSpaceTimeByLocationAndDate takes a slice of SpaceTime and:
//
// 1. Groups them by lat/long coordinates
//
// 2. For each location group, merges entries that fall within the same calendar date
//
// 3. Returns the merged buckets sorted by time
func BucketSpaceTimeByLocationAndDate(spacetime []wxtypes.SpaceTime) []wxtypes.SpaceTime {
	if len(spacetime) == 0 {
		return spacetime
	}

	locationGroups := make(map[string][]wxtypes.SpaceTime)
	for _, st := range spacetime {
		key := fmt.Sprintf("%.6f,%.6f", st.Lat, st.Long)
		locationGroups[key] = append(locationGroups[key], st)
	}

	var result []wxtypes.SpaceTime

	for _, group := range locationGroups {
		dailyBuckets := make(map[string]wxtypes.SpaceTime)

		for _, st := range group {
			dateKey := st.TimeFrom.Format("2006-01-02")
			existing, exists := dailyBuckets[dateKey]

			if !exists {
				dailyBuckets[dateKey] = wxtypes.SpaceTime{
					Lat:      st.Lat,
					Long:     st.Long,
					TimeFrom: st.TimeFrom,
					TimeTo:   st.TimeTo,
				}
			} else {
				if st.TimeFrom.Before(existing.TimeFrom) {
					existing.TimeFrom = st.TimeFrom
				}
				if st.TimeTo.After(existing.TimeTo) {
					existing.TimeTo = st.TimeTo
				}
				dailyBuckets[dateKey] = existing
			}
		}

		for _, bucket := range dailyBuckets {
			result = append(result, bucket)
		}
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].TimeFrom.Before(result[j].TimeFrom)
	})

	return result
}

// BucketSpaceTimeByLocationAndMergeDates takes a slice of SpaceTime and:
//
// 1. First calls BucketSpaceTimeByLocationAndDate to group by location and date
//
// 2. Then merges consecutive dates at the same location into single buckets
//
// 3. Returns the merged buckets sorted by time
func BucketSpaceTimeByLocationAndMergeDates(spacetime []wxtypes.SpaceTime) []wxtypes.SpaceTime {
	if len(spacetime) == 0 {
		return spacetime
	}

	dailyBuckets := BucketSpaceTimeByLocationAndDate(spacetime)

	locationGroups := make(map[string][]wxtypes.SpaceTime)
	for _, st := range dailyBuckets {
		key := fmt.Sprintf("%.6f,%.6f", st.Lat, st.Long)
		locationGroups[key] = append(locationGroups[key], st)
	}

	var result []wxtypes.SpaceTime

	for _, group := range locationGroups {
		sort.Slice(group, func(i, j int) bool {
			return group[i].TimeFrom.Before(group[j].TimeFrom)
		})

		if len(group) == 0 {
			continue
		}

		current := group[0]

		for i := 1; i < len(group); i++ {
			var (
				next            = group[i]
				currentDate     = current.TimeTo.Format("2006-01-02")
				nextDate        = next.TimeFrom.Format("2006-01-02")
				currentDay, _   = time.Parse("2006-01-02", currentDate)
				nextDay, _      = time.Parse("2006-01-02", nextDate)
				expectedNextDay = currentDay.Add(24 * time.Hour)
			)

			if nextDay.Equal(expectedNextDay) {
				current.TimeTo = next.TimeTo
			} else {
				// Not consecutive, add current to result and start new bucket
				result = append(result, current)
				current = next
			}
		}
		result = append(result, current)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].TimeFrom.Before(result[j].TimeFrom)
	})

	return result
}
