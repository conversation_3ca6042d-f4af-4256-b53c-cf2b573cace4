package concentration

import (
	"log/slog"

	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/gas"
)

// MicrogramsCubedToPpb takes in a value in µg/m3 and converts it to a correct ppb representation of the value
// for a specified gas. Each gas has a different molecular weight.
//
// The general equation is Concentration (ppb) = 24.45 x concentration (µg/m3) / molecular weight
func MicrogramsCubedToPpb(v float64, g gas.Gas) float64 {
	var (
		moleV = 24.45 // the volume (litres) of a mole (gram molecular weight) of a gas when the temperature is at 25°C and the pressure is at 1 atmosphere (1 atm = 1.01325 bar)
		moleW = getMolWFromGas(g)
	)
	return convertor.RoundFloat((moleV*v)/moleW, 3)
}

// MicrogramsCubedToPpm takes in a value in µg/m3 and converts it to a correct ppm representation of the value
// for a specified gas. Each gas has a different molecular weight.
//
// The general equation is Concentration (ppm) = (24.45 x concentration (µg/m3) / molecular weight) / 1_000
func MicrogramsCubedToPpm(v float64, g gas.Gas) float64 {
	return convertor.RoundFloat(MicrogramsCubedToPpb(v, g)/1000, 3)
}

// getMolWFromGas takes in a specific gas and looks up its molecular weight in a table. Returns `-1` if not found.
func getMolWFromGas(g gas.Gas) float64 {
	switch g {
	case gas.CarbonMonoxide:
		return 28.01
	case gas.NitricOxide:
		return 30.01
	case gas.NitrogenDioxide:
		return 46.01
	case gas.Ozone:
		return 48.0
	case gas.SulphurDioxide:
		return 64.07
	default:
		slog.Warn("could not find molecular weight", "gas", g)
		return -1
	}
}
