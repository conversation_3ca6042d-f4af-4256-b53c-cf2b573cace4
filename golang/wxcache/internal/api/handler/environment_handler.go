package handler

import (
	"context"
	"log/slog"
	"net/http"

	"github.com/labstack/echo/v4"
	"llif.org/wxcache/internal/api/request"
	"llif.org/wxcache/internal/api/response"
	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/internal/spacetime"
)

type environmentHandler struct {
	*slog.Logger

	svc service.EnvironmentService
}

func NewEnvironmentHandler(l *slog.Logger, svc service.EnvironmentService) *environmentHandler {
	return &environmentHandler{
		Logger: l,
		svc:    svc,
	}
}

func (h *environmentHandler) PostAirQuality(ctx echo.Context) error {
	input, err := bindValidate[request.PostEnvironment](ctx)
	if err != nil {
		return ctx.JSON(http.StatusBadRequest, response.APIError{Error: err.Error()})
	}

	if err := spacetime.ValidateSpaceTimeOrder(input.SpaceTime); err != nil {
		h.ErrorContext(ctx.Request().Context(), "invalid spacetime order", "err", err)
		return ctx.JSON(http.StatusUnprocessableEntity, response.APIError{Error: err.Error()})
	}

	data, err := h.svc.GetAirQuality(context.WithValue(ctx.Request().Context(), ckey.Runtime, ckey.RuntimeHTTP), input.SpaceTime)
	if err != nil {
		h.ErrorContext(ctx.Request().Context(), "could not fetch air quality", "err", err)
		return err
	}
	if len(data) == 0 {
		return ctx.JSON(http.StatusNoContent, nil)
	}
	return ctx.JSON(http.StatusOK, response.APIResponse{Data: data})
}

func (h *environmentHandler) PostWeather(ctx echo.Context) error {
	input, err := bindValidate[request.PostEnvironment](ctx)
	if err != nil {
		return ctx.JSON(http.StatusBadRequest, response.APIError{Error: err.Error()})
	}

	if err := spacetime.ValidateSpaceTimeOrder(input.SpaceTime); err != nil {
		h.ErrorContext(ctx.Request().Context(), "invalid spacetime order", "err", err)
		return ctx.JSON(http.StatusUnprocessableEntity, response.APIError{Error: err.Error()})
	}

	data, err := h.svc.GetWeather(context.WithValue(ctx.Request().Context(), ckey.Runtime, ckey.RuntimeHTTP), input.SpaceTime)
	if err != nil {
		h.ErrorContext(ctx.Request().Context(), "could not fetch weather", "err", err)
		return err
	}
	if len(data) == 0 {
		return ctx.JSON(http.StatusNoContent, nil)
	}
	return ctx.JSON(http.StatusOK, response.APIResponse{Data: data})
}

func (h *environmentHandler) PostPollen(ctx echo.Context) error {
	input, err := bindValidate[request.PostEnvironment](ctx)
	if err != nil {
		return ctx.JSON(http.StatusBadRequest, response.APIError{Error: err.Error()})
	}

	if err := spacetime.ValidateSpaceTimeOrder(input.SpaceTime); err != nil {
		h.ErrorContext(ctx.Request().Context(), "invalid spacetime order", "err", err)
		return ctx.JSON(http.StatusUnprocessableEntity, response.APIError{Error: err.Error()})
	}

	data, err := h.svc.GetPollen(context.WithValue(ctx.Request().Context(), ckey.Runtime, ckey.RuntimeHTTP), input.SpaceTime)
	if err != nil {
		h.ErrorContext(ctx.Request().Context(), "could not fetch pollen", "err", err)
		return err
	}
	if len(data) == 0 {
		return ctx.JSON(http.StatusNoContent, nil)
	}
	return ctx.JSON(http.StatusOK, response.APIResponse{Data: data})
}
