from datetime import datetime, timezone
from typing import Any, Async<PERSON>enerator

import pytest

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.value_limits import HeartRateValueLimit
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.heart_rate import HeartRate, HeartRateFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.base.tests.domain.builders.heart_rate_builder import HeartRateBuilder
from services.data_service.api.tests.common_calls import _delete_user_documents
from services.data_service.api.tests.utils import TestUtils
from services.data_service.conftest import UserFactoryCallable


class TestUpdateEndpoint:
    @pytest.fixture
    async def heart_rate_and_user(
        self, depr_event_repository: DeprEventRepository, user_factory: UserFactoryCallable
    ) -> AsyncGenerator[tuple[DeprEventModel | Any, MemberUser], Any]:
        user: MemberUser = await user_factory()
        heart_rate = HeartRateBuilder().with_user_uuid(user.user_uuid).build()
        inserted_heart_rate = (await depr_event_repository.insert(models=[heart_rate]))[0]
        yield inserted_heart_rate, user

        # Teardown
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=HeartRate, event_repo=depr_event_repository)

    async def test_update_heart_rate_by_id_should_pass(self, heart_rate_and_user):
        heart_rate, user = heart_rate_and_user
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        updated_heart_rate = heart_rate.model_copy(
            update={
                HeartRateFields.BPM_AVG: round(
                    PrimitiveTypesGenerator.generate_random_float(
                        min_value=HeartRateValueLimit.MINIMUM, max_value=HeartRateValueLimit.MAXIMUM, decimal_numbers=2
                    )
                ),
                DocumentLabels.TIMESTAMP: datetime.now(timezone.utc),
            }
        )

        # Act
        response = await TestUtils.call_update(
            data_type=DataType.HeartRate, updated_doc=updated_heart_rate, headers=headers
        )

        # Assert
        response_json = response.json()
        updated_at: str | None = response_json[DocumentLabels.SYSTEM_PROPERTIES].pop(DocumentLabels.UPDATED_AT)
        assert updated_at
        assert TestUtils.is_date_within_one_minute(datetime.fromisoformat(updated_at))
        assert TestUtils.to_assertable_dict(model=updated_heart_rate) == response_json
