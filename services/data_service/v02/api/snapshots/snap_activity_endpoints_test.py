# -*- coding: utf-8 -*-
# snapshottest: v1 - https://goo.gl/zC4yUc
from __future__ import unicode_literals

from services.data_service.v02.api.snapshot_impl import Snapshot

snapshots = Snapshot()
snapshots["test_sleep_endpoint_sample_data_fetch_should_pass_sample_data_fetch_should_pass[inputdata0] 1"] = {
    "Status": {},
    "Values": [
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2019-01-01T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 720.0,
            "asleep_seconds": 133800.0,
            "awake_seconds": 19980.0,
            "deep_seconds": 16800.0,
            "efficiency": 762.0,
            "events_count": 226.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 153840.0,
            "light_seconds": 92640.0,
            "rem_seconds": 21240.0,
            "restless_seconds": 420.0,
            "timestamp": "2020-01-01T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2021-01-01T00:00:00.000-04:00",
        },
    ],
}

snapshots["test_sleep_endpoint_sample_data_fetch_should_pass_sample_data_fetch_should_pass[inputdata1] 1"] = {
    "Status": {},
    "Values": [
        {
            "after_wakeup_seconds": 240.0,
            "asleep_seconds": 18780.0,
            "awake_seconds": 3420.0,
            "deep_seconds": 2100.0,
            "efficiency": 96.0,
            "events_count": 27.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 22200.0,
            "light_seconds": 12600.0,
            "rem_seconds": 4080.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-12T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 13560.0,
            "awake_seconds": 2100.0,
            "deep_seconds": 1680.0,
            "efficiency": 94.0,
            "events_count": 28.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 15660.0,
            "light_seconds": 9420.0,
            "rem_seconds": 2460.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-13T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-14T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-15T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-16T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-17T00:00:00.000-04:00",
        },
        {
            "after_wakeup_seconds": 0.0,
            "asleep_seconds": 0.0,
            "awake_seconds": 0.0,
            "deep_seconds": 0.0,
            "efficiency": 0.0,
            "events_count": 0.0,
            "fall_asleep_seconds": 0.0,
            "in_bed_seconds": 0.0,
            "light_seconds": 0.0,
            "rem_seconds": 0.0,
            "restless_seconds": 0.0,
            "timestamp": "2020-08-18T00:00:00.000-04:00",
        },
    ],
}

snapshots["test_steps_endpoint_sample_data_fetch_should_pass 1"] = {
    "Status": {},
    "Values": [
        {"steps": 20098, "timestamp": "2019-01-01T00:00:00.000-04:00"},
        {"steps": 0, "timestamp": "2020-01-01T00:00:00.000-04:00"},
        {"steps": 0, "timestamp": "2021-01-01T00:00:00.000-04:00"},
    ],
}
