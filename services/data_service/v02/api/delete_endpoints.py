from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.application.enums.deletable_data_type import DeletableDataType
from services.data_service.application.use_cases.by_id.delete_by_id_use_case import (
    DeleteByIdUseCase,
)
from services.data_service.v02.api.models.response.delete_document_by_id_api_response import (
    DeleteDocumentByIdAPIResponse,
)
from services.data_service.v02.constants import DataServicePrefixes, DeleteEndpointRoutes

delete_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.DELETE_PREFIX}",
    tags=["delete"],
    responses={404: {"description": "Not found"}},
)


@delete_router.delete(DeleteEndpointRoutes.BY_ID, response_model=DeleteDocumentByIdAPIResponse)
async def delete_by_id_endpoint(
    data_type: DeletableDataType,
    doc_id: UUID = Query(...),
    current_uuid: UUID = Depends(get_current_uuid),
    target_use_case: DeleteByIdUseCase = Injected(DeleteByIdUseCase),
):
    """Deletes an entry that matches given doc_id"""
    result = await target_use_case.execute_async(
        user_uuid=current_uuid,
        doc_ids=[doc_id],
        data_schema=data_type.to_domain_model(),
    )
    return DeleteDocumentByIdAPIResponse.map(model=result[0])
