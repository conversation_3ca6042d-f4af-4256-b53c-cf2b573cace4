import pytest

from services.data_service.constants import (
    API_FILTER_INTERVAL,
    API_FILTER_TIME_GTE,
    API_FILTER_TIME_LTE,
    RE_FETCH_MOST_RECENT,
)
from services.data_service.v02.api.snapshot_impl import SnapshotTest


@pytest.fixture
def snapshot(request) -> SnapshotTest:
    return SnapshotTest(request)


@pytest.fixture
def data_service_common_query_params():
    return {
        API_FILTER_TIME_GTE: "2019-08-17T06:43:30-04:00",
        API_FILTER_TIME_LTE: "2021-08-20T10:43:30-04:00",
        API_FILTER_INTERVAL: "1y",
        RE_FETCH_MOST_RECENT: "true",
    }


@pytest.fixture
def data_service_common_query_params_detail():
    return {
        API_FILTER_TIME_GTE: "2019-08-17T06:43:30-04:00",
        API_FILTER_TIME_LTE: "2021-08-20T10:43:30-04:00",
        RE_FETCH_MOST_RECENT: "true",
    }
