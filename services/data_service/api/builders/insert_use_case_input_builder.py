from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInput,
)


class InsertUseCaseInputBuilder:
    def build(self):
        return InsertUseCaseInput(
            name=PrimitiveTypesGenerator.generate_random_string(), tags=PrimitiveTypesGenerator.generate_random_tags()
        )
