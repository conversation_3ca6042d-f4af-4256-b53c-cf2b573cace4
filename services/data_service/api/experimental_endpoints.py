from uuid import UUID

from fastapi import APIRouter, Depends
from fastapi_injector import Injected
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.data_service.api.constants import DataServicePrefixes, ExperimentalEndpointRoutes
from services.data_service.api.models.request.experimental.suggest_descendant_nodes_api_request_input import (
    SuggestDescendantNodesAPIRequestInput,
)
from services.data_service.api.models.request.experimental.suggest_descendant_nodes_api_request_output import (
    SuggestDescendantNodesAPIRequestOutput,
)
from services.data_service.application.use_cases.type_tree.suggest_descendant_nodes_use_case import (
    SuggestDescendantNodesUseCase,
    SuggestDescendantNodesUseCaseInputBoundary,
)

experimental_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.EXPERIMENTAL_PREFIX}",
    tags=["experimental"],
    responses={404: {"description": "Not found"}},
)


@experimental_router.get(
    ExperimentalEndpointRoutes.SUGGEST_DESCENDANT_NODES,
    status_code=status.HTTP_200_OK,
    description="returns possible type descendant of given node",
)
def suggest_descendant_nodes_endpoint(
    _: UUID = Depends(get_current_uuid),
    use_case: SuggestDescendantNodesUseCase = Injected(SuggestDescendantNodesUseCase),
    input_boundary: SuggestDescendantNodesUseCaseInputBoundary = Depends(
        SuggestDescendantNodesAPIRequestInput.to_input_boundary
    ),
) -> SuggestDescendantNodesAPIRequestOutput:
    output = use_case.execute(input_boundary=input_boundary)
    if not output.nodes:
        raise NoContentException(message="no matching nodes found")
    return SuggestDescendantNodesAPIRequestOutput.map(model=output)
