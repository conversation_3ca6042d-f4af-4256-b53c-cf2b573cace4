from typing import Sequence

from pydantic import Field

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.query_api_mapper import QueryAPIMapper
from services.base.domain.annotated_types import NodeStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric
from services.base.domain.schemas.events.exercise.cardio import Cardio
from services.base.domain.schemas.events.exercise.exercise import Exercise
from services.base.domain.schemas.events.exercise.strength import Strength
from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.events.feeling.stress import Stress
from services.base.domain.schemas.events.nutrition.drink import Drink
from services.base.domain.schemas.events.nutrition.food import Food
from services.base.domain.schemas.events.nutrition.supplement import Supplement
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TypeTree


class NodeTypedQueryAPI(BaseDataModel):
    nodes: Sequence[NodeStr] = Field(min_length=1)
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    def to_type_query(self, type_tree: TypeTree) -> TypeQuery:
        query = QueryAPIMapper.map(query=self.query)
        cats = []
        tps = []
        for n in self.nodes:
            node = type_tree.get_node(n)
            cat = node.category
            if cat:
                cats.append(node.category)
            match node:
                case "doc":
                    tps.extend([et.to_domain_model() for et in EventType])
                case "doc.event":
                    tps.extend([et.to_domain_model() for et in EventType])
                case "doc.event.nutrition":
                    tps.extend([Food, Drink, Supplement])
                case "doc.event.feeling":
                    tps.extend([Emotion, Stress])
                case "doc.event.exercise":
                    tps.extend([Exercise, Cardio, Strength])
                case "doc.event.body_metric":
                    tps.extend([BloodGlucose, BloodPressure, BodyMetric])
                case _:
                    assert node.data_type
                    tps.append(node.data_type.to_domain_model())
        if cats:
            v_q = ValuesQuery(field_name=DocumentLabels.CATEGORY, values=cats)
            query = BooleanQueryBuilder().add_queries(queries=[v_q, query]).build_and_query()
        return TypeQuery(
            query=query,
            domain_types=tps,
        )


class NodeQueryAPI(BaseDataModel):
    queries: Sequence[NodeTypedQueryAPI] = Field(default_factory=list)

    def to_query(self, type_tree: TypeTree) -> Query:
        if self.queries:
            return Query(type_queries=[q.to_type_query(type_tree=type_tree) for q in self.queries])
        else:
            typed_query = TypeQuery(domain_types=[et.to_domain_model() for et in EventType], query=None)
            return Query(type_queries=[typed_query])
