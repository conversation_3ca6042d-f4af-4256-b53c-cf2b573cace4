from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends
from pydantic import Field, model_validator

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.schemas.query.aggregations import CalendarAggregationType, SimpleAggregationMethod
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.data_service.api.queries.event_query_api import EventQueryAPI
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregations_input_boundary import (
    CalendarHistogramAggregationInputBoundary,
)


class CalendarHistogramAggregationAPIRequestInput(EventQueryAPI):
    calendar_aggregation_type: CalendarAggregationType = Field()
    fill_null_values: bool = Field(default=True)
    field_name: str | None = Field()
    aggregation_method: SimpleAggregationMethod = Field()

    @staticmethod
    def to_input_boundary(
        request_input: CalendarHistogramAggregationAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> CalendarHistogramAggregationInputBoundary:
        return CalendarHistogramAggregationInputBoundary(
            calendar_aggregation_type=request_input.calendar_aggregation_type,
            fill_null_values=request_input.fill_null_values,
            field_name=request_input.field_name,
            aggregation_method=request_input.aggregation_method,
            query=request_input.to_query(),
            owner_id=owner_id,
        )

    @model_validator(mode="after")
    def validate_field_name(self):
        aggregation_method = self.aggregation_method
        if aggregation_method != SimpleAggregationMethod.COUNT and self.field_name is None:
            raise ValueError("Field name must be provided for aggregation methods other than COUNT")
        return self

    @model_validator(mode="after")
    def validate_field_type(self):
        if self.field_name is None:
            return self
        query = self.to_query()
        for q in query.type_queries:
            for dt in q.domain_types:
                FieldValidator.validate_field(field_name=self.field_name, must_be_numeric=True, parent_type=dt)
        return self
