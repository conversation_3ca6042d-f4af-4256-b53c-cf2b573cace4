from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider
from services.data_service.api.tests.common_rpc_calls import _call_get_endpoint
from services.data_service.v1.api.models.output.extensions.extension_provider_api_output import (
    ExtensionProviderAPIOutput,
)
from services.data_service.v1.api.models.response.extensions.list_extension_providers_response import (
    ListExtensionProvidersResponse,
)
from services.data_service.v1.api.urls import ExtensionProviderEndpointUrls
from settings.extension_constants import LLIF_EXTENSION_PROVIDER_UUID


class TestExtensionProviderEndpoints:
    @pytest.fixture(scope="class")
    async def seed_providers(self, extension_provider_repository: ExtensionProviderRepository):
        # Setup
        provider_id = uuid4()
        providers = await extension_provider_repository.upsert(
            extension_providers=[
                ExtensionProvider(provider_id=provider_id, name="test", domain="test.org", description="test")
            ]
        )
        yield providers
        # Teardown
        await extension_provider_repository.delete(extension_providers=providers)

    async def test_get_extension_provider_should_include_seeded_llif_should_pass(self):
        # Arrange
        request_url = join_as_url(
            base_url=ExtensionProviderEndpointUrls.BASE,
            query_params={"provider_id": LLIF_EXTENSION_PROVIDER_UUID},
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = ExtensionProviderAPIOutput(**response.json())
        assert response_model.name == "llif"
        assert response_model.domain == "org.llif"
        assert (
            response_model.description
            == "The Live Learn Innovate Foundation (LLIF) is a non-profit organization that is committed to helping"
            " individuals recover and derive value from their life data. If questions or feedback, Please contact"
            " <NAME_EMAIL>"
        )

    async def test_get_extension_provider_should_include_added_should_pass(self, seed_providers):
        provider = seed_providers[0]
        # Arrange
        request_url = join_as_url(
            base_url=ExtensionProviderEndpointUrls.BASE, query_params={"provider_id": provider.provider_id}
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = ExtensionProviderAPIOutput(**response.json())
        assert response_model == ExtensionProviderAPIOutput.map(model=provider)

    async def test_list_extensions_providers_should_include_added_should_pass(self, seed_providers):
        # Arrange
        request_url = join_as_url(base_url=ExtensionProviderEndpointUrls.LIST, query_params=None)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = ListExtensionProvidersResponse(**response.json())
        for provider in seed_providers:
            assert ExtensionProviderAPIOutput.map(model=provider) in response_model.items
