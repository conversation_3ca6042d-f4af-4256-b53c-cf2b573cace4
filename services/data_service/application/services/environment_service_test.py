import pytest

from services.data_service.application.services.environment_service import EnvironmentService


class TestEnvironmentService:

    @pytest.mark.parametrize(
        "backfill_threshold, data_len, data_to_backfill_len, expected",
        [
            (0.5, 100, 20, False),
            (0.5, 100, 60, True),
            (0.7, 365, 3, False),
            (0.7, 365, 120, True),
            (0.3, 100, 0, False),
            (0.3, 100, 100, True),
        ],
    )
    def test_should_wait_for_response_passes(
        self, backfill_threshold: float, data_len: int, data_to_backfill_len: int, expected: bool
    ):
        assert (
            EnvironmentService._should_wait_for_response(
                backfill_threshold=backfill_threshold, data_len=data_len, data_to_backfill_len=data_to_backfill_len
            )
            is expected
        )

    @pytest.mark.parametrize(
        "backfill_threshold, data_len, data_to_backfill_len",
        [
            (-0.1, 100, 20),
            (1.1, 100, 20),
        ],
    )
    def test_should_backfill_raises(self, backfill_threshold: float, data_len: int, data_to_backfill_len: int):
        with pytest.raises(ValueError):
            assert EnvironmentService._should_wait_for_response(
                backfill_threshold=backfill_threshold, data_len=data_len, data_to_backfill_len=data_to_backfill_len
            )
