from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content import ContentCategory, ContentIdentifier
from services.data_service.application.builders.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import (
    InsertContentInput,
)


class InsertContentInputBuilder(InsertEventBuilderBase, ContentIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertContentInput:
        return InsertContentInput(
            type=DataType.Content,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ContentCategory),
            template_id=self._template_id,
        )
