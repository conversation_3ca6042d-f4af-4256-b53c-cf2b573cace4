from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityIdentifier
from services.base.domain.schemas.events.event import EventPlanExtension
from services.data_service.application.builders.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.application.use_cases.events.models.insert_activity_input import InsertActivityInput


class InsertActivityInputBuilder(InsertEventBuilderBase, ActivityIdentifier):
    def build(self) -> InsertActivityInput:
        return InsertActivityInput(
            type=DataType.Activity,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            assets=self._assets,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )

    def build_n(self, n: int | None = None) -> list[InsertActivityInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
