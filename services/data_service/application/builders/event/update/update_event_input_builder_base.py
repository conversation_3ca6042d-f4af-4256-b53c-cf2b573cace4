from abc import abstractmethod
from datetime import datetime
from typing import Self
from uuid import UUID

from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateEventInputBuilderBase(BuilderBase, TypeIdentifier):

    def __init__(self):
        super().__init__()
        self._id: UUID | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._group_id: UUID | None = None

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_timestamp(self, timestamp: datetime | None) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_group_id(self, group_id):
        self._group_id = group_id
        return self

    @abstractmethod
    def build(self) -> UpdateEventInput:
        pass
