from datetime import datetime, timedelta
from typing import Self

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.value_limits import StepsValueLimit
from services.data_service.application.use_cases.loading.steps.models.load_steps_input import LoadStepsInput


class LoadStepsInputBuilder:

    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> LoadStepsInput:
        time_interval = CustomModelsGenerator.generate_random_time_interval(
            timestamp=self._timestamp, end_time=self._end_time
        )
        return LoadStepsInput(
            timestamp=self._timestamp or time_interval.timestamp,
            steps=PrimitiveTypesGenerator.generate_random_int(min_value=StepsValueLimit.MINIMUM),
            end_time=self._end_time or time_interval.end_time,
        )

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> list[LoadStepsInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
