from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.data_service.application.use_cases.loading.metadata_input import MetadataInputModel


class MetadataInputBuilder:
    def build(self) -> MetadataInputModel:
        return MetadataInputModel(
            organization=PrimitiveTypesGenerator.generate_random_enum(Organization),
            data_integrity=PrimitiveTypesGenerator.generate_random_enum(DataIntegrity),
        )
