from typing import Self
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.records.sleep_record import SleepRecordCategory, SleepRecordIdentifier
from services.data_service.application.builders.record.update_record_builder_base import (
    UpdateRecordBuilderBase,
)
from services.data_service.application.use_cases.records.models.update_sleep_record_input import UpdateSleepRecordInput


class UpdateSleepRecordInputBuilder(UpdateRecordBuilderBase, SleepRecordIdentifier):
    def __init__(self):
        super().__init__()
        self._stage: SleepStage | None = None
        self._category: SleepRecordCategory | None = None

    def build(self) -> UpdateSleepRecordInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        end_time = self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp)

        return UpdateSleepRecordInput(
            id=self._id or uuid4(),
            type=DataType.SleepRecord,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepRecordCategory),
            timestamp=timestamp,
            end_time=end_time,
            stage=self._stage or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepStage),
        )

    def with_stage(self, stage: SleepStage) -> Self:
        self._stage = stage
        return self
