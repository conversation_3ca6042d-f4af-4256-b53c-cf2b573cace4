from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.records.steps_record import StepsRecordCategory, StepsRecordIdentifier
from services.data_service.application.builders.record.insert_record_builder_base import InsertRecordBuilderBase
from services.data_service.application.use_cases.records.models.insert_steps_record_input import InsertStepsRecordInput


class InsertStepsRecordBuilder(InsertRecordBuilderBase, StepsRecordIdentifier):
    def __init__(self):
        super().__init__()
        self._value: int | None = None
        self._category: StepsRecordCategory | None = None

    def build(self) -> InsertStepsRecordInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return InsertStepsRecordInput(
            type=DataType.StepsRecord,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=StepsRecordCategory),
            timestamp=timestamp,
            end_time=self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp),
            value=self._value or PrimitiveTypesGenerator.generate_random_int(10, 100),
        )

    def with_value(self, value: int) -> Self:
        self._value = value
        return self

    def with_category(self, category: StepsRecordCategory) -> Self:
        self._category = category
        return self

    def build_n(self, n: int | None = None) -> Sequence[InsertStepsRecordInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]
