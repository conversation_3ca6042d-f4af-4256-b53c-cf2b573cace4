from abc import ABC
from datetime import datetime
from typing import Self
from uuid import UUID

from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.tests.domain.builders.builder_base import BuilderBase


class UpdateRecordBuilderBase(BuilderBase, TypeIdentifier, ABC):

    def __init__(self):
        super().__init__()
        self._id: UUID | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_timestamp(self, timestamp: datetime | None) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self
