from __future__ import annotations

from datetime import datetime
from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.data_service.application.use_cases.records.insert_record_inputs import InsertRecordInputs
from services.data_service.type_resolver import TypeResolver


class InsertRecordInputBuilder(BuilderBase):
    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._type_id: str | None = None

    def build(self) -> InsertRecordInputs:
        type_id = self._type_id or PrimitiveTypesGenerator.get_random_type_id()
        builder = TypeResolver.get_insert_record_input_builder(type_id=type_id)
        return (
            builder()
            .with_timestamp(timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime())
            .with_end_time(self._end_time)
            .build()
        )

    def build_all(self, n: int | None = None) -> Sequence[InsertRecordInputs]:
        out = []
        for builder_type in TypeResolver.INSERT_RECORD_INPUT_BUILDERS_UNION.__args__:
            out.extend(
                [
                    builder_type().build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=2))
                ]
            )
        return out

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_type_id(self, type_id: str) -> Self:
        self._type_id = type_id
        return self
