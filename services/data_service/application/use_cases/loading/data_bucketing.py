from typing import List

from pydantic import AwareDatetime

from services.base.application.input_validators.shared import InputTimestampModel
from services.base.domain.schemas.shared import BaseDataModel


class TimeBucket(BaseDataModel):
    timestamp: AwareDatetime
    end_time: AwareDatetime


class DataAndTimeBucket[T: InputTimestampModel](BaseDataModel):
    time_bucket: TimeBucket
    data: List[T]
