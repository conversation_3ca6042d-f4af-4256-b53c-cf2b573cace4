from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.steps import Steps, StepsDetail
from services.data_service.application.use_cases.loading.data_bucketing import DataAndTimeBucket
from services.data_service.application.use_cases.loading.steps.models.load_steps_input import LoadStepsInput


class StepsMapper:
    @staticmethod
    def map_value(input_data: DataAndTimeBucket[LoadStepsInput], metadata: Metadata) -> Steps:
        values = [d.steps for d in input_data.data]

        return Steps(
            timestamp=input_data.time_bucket.timestamp,
            end_time=input_data.time_bucket.end_time,
            step_details=[StepsDetail(steps=d.steps, timestamp=d.timestamp) for d in input_data.data],
            steps=sum(values),
            metadata=metadata,
            duration=int((input_data.time_bucket.end_time - input_data.time_bucket.timestamp).total_seconds()),
        )
