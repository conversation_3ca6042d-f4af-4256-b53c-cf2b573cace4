from datetime import datetime, time, timedelta
from typing import Iterable, Sequence

from services.base.application.input_validators.shared import InputTimestampModel
from services.data_service.application.use_cases.loading.data_bucketing import (
    DataAndTimeBucket,
    TimeBucket,
)


class DaySplitter:
    @staticmethod
    def split[T: InputTimestampModel](data: Sequence[T], time_limit: time) -> Iterable[DataAndTimeBucket[T]]:
        current_bulk: list[T] = []
        current_time_bucket: TimeBucket = DaySplitter.create_day_time_bucket(
            entry_datetime=data[0].timestamp, time_limit=time_limit
        )

        for entry in data:
            if DaySplitter.is_in_bucket_range(bucket=current_time_bucket, entry_datetime=entry.timestamp):
                current_bulk.append(entry)
            else:
                yield DataAndTimeBucket(data=current_bulk, time_bucket=current_time_bucket)
                current_time_bucket = DaySplitter.create_day_time_bucket(
                    entry_datetime=entry.timestamp, time_limit=time_limit
                )
                current_bulk = [entry]

        yield DataAndTimeBucket(data=current_bulk, time_bucket=current_time_bucket)

    @staticmethod
    def create_day_time_bucket(entry_datetime: datetime, time_limit: time) -> TimeBucket:
        """Returns single day time bucket based on the specified time limit, e.g. from 18:00 to 18:00"""
        if entry_datetime.time() <= time_limit:
            return TimeBucket(
                timestamp=datetime.combine(
                    date=entry_datetime.date() - timedelta(days=1), time=time_limit, tzinfo=entry_datetime.tzinfo
                ),
                end_time=datetime.combine(date=entry_datetime.date(), time=time_limit, tzinfo=entry_datetime.tzinfo),
            )
        return TimeBucket(
            timestamp=datetime.combine(date=entry_datetime.date(), time=time_limit, tzinfo=entry_datetime.tzinfo),
            end_time=datetime.combine(
                date=entry_datetime.date() + timedelta(days=1), time=time_limit, tzinfo=entry_datetime.tzinfo
            ),
        )

    @staticmethod
    def is_in_bucket_range(bucket: TimeBucket, entry_datetime: datetime) -> bool:
        return bucket.timestamp <= entry_datetime < bucket.end_time
