from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.sleep import Sleep, SleepEvent, SleepSummary
from services.data_service.application.use_cases.loading.data_bucketing import DataAndTimeBucket
from services.data_service.application.use_cases.loading.sleep.models.load_sleep_input import LoadSleepInput
from services.data_service.application.use_cases.loading.sleep.sleep_parser import SleepParser


class SleepMapper:
    @staticmethod
    def map_value(input_data: DataAndTimeBucket[LoadSleepInput], metadata: Metadata) -> Sleep:
        """Parses data to output sleep entry"""
        sleep_data = input_data.data
        time_bucket = input_data.time_bucket
        output_sleep_detail = SleepParser.parse_sleep_detail(sleep_data)
        output_sleep_summary: SleepSummary = SleepParser.parse_sleep_summary(
            entry_list=sleep_data, events_count=len(output_sleep_detail)
        )

        output_start_time = time_bucket.timestamp
        output_end_time = time_bucket.end_time
        output_duration = int((output_end_time - output_start_time).total_seconds()) if output_end_time else None

        # Needs to be refactored if we want to differentiate between multiple sleep events
        output_sleep_events = [
            SleepEvent(
                timestamp=output_start_time,
                end_time=output_end_time,
                duration=output_duration,
                sleep_detail=output_sleep_detail,
                sleep_summary=output_sleep_summary,
            )
        ]

        output_sleep = Sleep(
            timestamp=output_start_time,
            end_time=output_end_time,
            duration=output_duration,
            sleep_events=output_sleep_events,
            metadata=metadata,
        )

        return output_sleep
