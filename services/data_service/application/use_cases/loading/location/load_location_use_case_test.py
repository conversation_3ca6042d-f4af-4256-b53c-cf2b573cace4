import random
from datetime import timedelta
from math import asin, atan2, cos, degrees, radians, sin

import pytest

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.location import Location, LocationFields
from services.base.domain.schemas.shared import CoordinatesModel
from services.base.tests.domain.builders.location_builder import LocationBuilder
from services.data_service.application.use_cases.loading.location.load_location_use_case import LoadLocationUseCase


class TestLoadLocationUseCase:

    @staticmethod
    def tweak_location(
        location: Location, delta: timedelta | None = None, distance_km: float | None = None
    ) -> Location:
        coordinates, timestamp = location.start_coordinates, location.timestamp

        # Shift the timestamp
        if delta:
            timestamp += delta

        # Shift the coordinates
        if distance_km:
            bearing = random.uniform(0, 360)
            coordinates = TestLoadLocationUseCase.shift_coordinates(
                coordinates=coordinates, bearing=bearing, distance_km=distance_km
            )

        return Location.map(
            model=location, fields={LocationFields.TIMESTAMP: timestamp, LocationFields.START_COORDINATES: coordinates}
        )

    @staticmethod
    def shift_coordinates(coordinates: CoordinatesModel, bearing: float, distance_km: float) -> CoordinatesModel:
        R = 6371.0  # Earth radius in kilometers
        lat, lon = radians(coordinates.latitude), radians(coordinates.longitude)
        bearing = radians(bearing)

        # Shift the coordinates by distance with respect to bearings
        new_lat = asin(sin(lat) * cos(distance_km / R) + cos(lat) * sin(distance_km / R) * cos(bearing))
        new_lon = lon + atan2(
            sin(bearing) * sin(distance_km / R) * cos(lat), cos(distance_km / R) - sin(lat) * sin(new_lat)
        )

        return CoordinatesModel(lat=degrees(new_lat), lon=degrees(new_lon))

    @pytest.fixture
    async def existing_location(self, depr_event_repository: DeprEventRepository):
        locations = LocationBuilder().build_n()
        locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
        yield locations
        results = await depr_event_repository.search_by_id(
            data_schema=Location, doc_ids=[loc.doc_id for loc in locations], user_uuid=None
        )
        [await depr_event_repository.delete_by_id(doc_id=r.id, entry=r.document) for r in results.results]

    async def test_does_location_exist_in_radius_non_overlapping_location_should_return_false(
        self,
        existing_location,
        load_location_use_case: LoadLocationUseCase,
    ):
        # Arrange
        locations = [
            self.tweak_location(
                location=loc,
                delta=PrimitiveTypesGenerator.generate_random_timedelta(min_timedelta=timedelta(minutes=6)),
                distance_km=1.1,
            )
            for loc in existing_location
        ]

        # Assert
        for location in locations:
            assert not await load_location_use_case._is_location_in_spacetime_radius(
                location=location, radius="1km", delta=timedelta(minutes=5)
            )

    async def test_does_location_exist_in_radius_existing_location_overlapping_radius_should_return_false(
        self,
        existing_location,
        load_location_use_case: LoadLocationUseCase,
    ):
        # Arrange
        locations = [
            self.tweak_location(
                location=loc,
                delta=PrimitiveTypesGenerator.generate_random_timedelta(min_timedelta=timedelta(minutes=6)),
                distance_km=0.5,
            )
            for loc in existing_location
        ]

        # Assert
        for location in locations:
            assert not await load_location_use_case._is_location_in_spacetime_radius(
                location=location, radius="1km", delta=timedelta(minutes=5)
            )

    async def test_does_location_exist_in_radius_existing_location_overlapping_time_should_return_false(
        self,
        existing_location,
        load_location_use_case: LoadLocationUseCase,
    ):
        # Arrange
        locations = [
            self.tweak_location(
                location=loc,
                delta=PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=timedelta(minutes=4)),
                distance_km=1.1,
            )
            for loc in existing_location
        ]

        # Assert
        for location in locations:
            assert not await load_location_use_case._is_location_in_spacetime_radius(
                location=location, radius="1km", delta=timedelta(minutes=5)
            )

    async def test_does_location_exist_in_radius_existing_overlapping_location_should_return_true(
        self,
        existing_location,
        load_location_use_case: LoadLocationUseCase,
    ):
        # Arrange
        locations = [
            self.tweak_location(
                location=loc,
                delta=PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=timedelta(minutes=4)),
                distance_km=0.5,
            )
            for loc in existing_location
        ]

        # Assert
        for location in locations:
            assert await load_location_use_case._is_location_in_spacetime_radius(
                location=location, radius="1km", delta=timedelta(minutes=5)
            )
