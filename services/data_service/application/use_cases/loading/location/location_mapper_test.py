from services.base.tests.domain.builders.metadata_builder import MetadataBuilder
from services.data_service.application.builders.load_location_input_builder import LoadLocationInputBuilder
from services.data_service.application.use_cases.loading.location.location_mapper import LocationMapper


def test_location_mapper_location_ordered_and_in_bucket_should_pass():
    # Arrange
    input_data = LoadLocationInputBuilder().build()

    metadata = MetadataBuilder().build()
    # Act
    output = LocationMapper.map_value(input_data=input_data, metadata=metadata)
    assert output.metadata == metadata

    # Assert
    # Time bucket assertions
    assert output.timestamp == input_data.timestamp
    assert output.end_time == input_data.end_time

    # Coordinates assertions
    assert output.start_coordinates
    assert output.start_coordinates.latitude == input_data.data.latitude
    assert output.start_coordinates.longitude == input_data.data.longitude

    assert output.end_coordinates
    assert output.end_coordinates.latitude == input_data.data.latitude
    assert output.end_coordinates.longitude == input_data.data.longitude

    # Duration assertion
    assert input_data.end_time
    expected_duration = int((input_data.end_time - input_data.timestamp).total_seconds())
    assert output.duration == expected_duration

    assert output.activity_type is None
    assert output.activity_type_probability is None


def test_location_mapper_parsed_places_should_pass():
    # Arrange
    input_data = LoadLocationInputBuilder().build()

    metadata = MetadataBuilder().build()
    # Act
    output = LocationMapper.map_value(input_data=input_data, metadata=metadata)

    assert output.place_visit_details
    assert input_data.place
    # Assert
    assert output.place_visit_details.name == input_data.place.name
    assert output.place_visit_details.address == input_data.place.address
    assert output.place_visit_details.confidence == input_data.place.confidence
