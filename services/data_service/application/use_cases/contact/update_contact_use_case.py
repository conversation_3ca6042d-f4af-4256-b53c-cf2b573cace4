from typing import Sequence
from uuid import UUID

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import IncorrectOperationException, InvalidPrivilegesException
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact
from services.data_service.application.use_cases.contact.models.update_contact_input_boundary import (
    UpdateContactInput,
    UpdateContactInputBoundary,
)


class UpdateContactUseCase:
    def __init__(
        self,
        contact_repository: ContactRepository,
        duplicate_check_service: DuplicateCheckService,
    ):
        self._contact_repo = contact_repository
        self._duplicate_check_service = duplicate_check_service

    async def execute_async(self, input_boundary: UpdateContactInputBoundary) -> Sequence[Contact]:
        existing_contacts = await self._fetch_contacts_from_db(
            input_contacts=input_boundary.documents, owner_id=input_boundary.owner_id
        )
        if not existing_contacts:
            return []

        updated_contacts = []
        contacts_needing_dup_check = []

        for contact_input in input_boundary.documents:
            existing_contact = next(c for c in existing_contacts if c.id == contact_input.id)

            updated_contact = Contact(
                id=existing_contact.id,
                type=existing_contact.type,
                last_name=contact_input.last_name,
                first_name=contact_input.first_name,
                company=contact_input.company,
                address=contact_input.address,
                note=contact_input.note,
                birthday=contact_input.birthday,
                relationship=contact_input.relationship,
                rbac=existing_contact.rbac,
                tags=contact_input.tags,
                system_properties=existing_contact.system_properties,
                archived_at=existing_contact.archived_at,
            )
            updated_contacts.append(updated_contact)
            if any(
                (
                    contact_input.first_name != existing_contact.first_name,
                    contact_input.last_name != existing_contact.last_name,
                )
            ):
                contacts_needing_dup_check.append(updated_contact)
        if contacts_needing_dup_check:
            await self._duplicate_check_service.validate_no_document_duplicates(documents=contacts_needing_dup_check)

        return await self._contact_repo.update(contacts=updated_contacts)

    async def _fetch_contacts_from_db(
        self, input_contacts: Sequence[UpdateContactInput], owner_id: UUID
    ) -> Sequence[Contact]:
        existing_contacts: Sequence[Contact] = await self._contact_repo.search_by_id(ids=[d.id for d in input_contacts])
        if len(input_contacts) != len(existing_contacts):
            ep_ids = [ep.id for ep in existing_contacts]
            not_found_ids = [str(p.id) for p in input_contacts if p.id not in ep_ids]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for contact in existing_contacts:
            if contact.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")
            if contact.archived_at:
                raise IncorrectOperationException(message="updating archived contact")
        return existing_contacts
