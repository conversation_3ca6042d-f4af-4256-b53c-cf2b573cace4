import re
from datetime import datetime, timezone
from http import HTT<PERSON>ethod
from typing import List, <PERSON>ple
from urllib.parse import quote

from pydantic import Field

from services.base.application.io.httpclient import HttpClient
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory
from services.base.domain.schemas.events.nutrition.food import FoodCategory
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_provider import (
    LookupProviderEnum,
    NutritionProvider,
    NutritionProviderInsertDrinkInput,
    NutritionProviderInsertFoodInput,
    NutritionProviderInsertSupplementInput,
    NutritionProviderOutput,
)


class OpenFoodFactsNutrients(BaseDataModel):
    energy_kcal_100g: float | None = Field(alias="energy-kcal_100g", default=None)
    energy_kj_100g: float | None = Field(alias="energy-kj_100g", default=None)
    fat_100g: float | None = Field(alias="fat_100g", default=None)
    saturated_fat_100g: float | None = Field(alias="saturated-fat_100g", default=None)
    trans_fat_100g: float | None = Field(alias="trans-fat_100g", default=None)
    cholesterol_100g: float | None = Field(alias="cholesterol_100g", default=None)
    carbohydrates_100g: float | None = Field(alias="carbohydrates_100g", default=None)
    sugars_100g: float | None = Field(alias="sugars_100g", default=None)
    fiber_100g: float | None = Field(alias="fiber_100g", default=None)
    proteins_100g: float | None = Field(alias="proteins_100g", default=None)
    salt_100g: float | None = Field(alias="salt_100g", default=None)
    sodium_100g: float | None = Field(alias="sodium_100g", default=None)
    vitamin_a_100g: float | None = Field(alias="vitamin-a_100g", default=None)
    vitamin_c_100g: float | None = Field(alias="vitamin-c_100g", default=None)
    calcium_100g: float | None = Field(alias="calcium_100g", default=None)
    iron_100g: float | None = Field(alias="iron_100g", default=None)
    potassium_100g: float | None = Field(alias="potassium_100g", default=None)
    caffeine_100g: float | None = Field(alias="caffeine_100g", default=None)


class OpenFoodFactsProduct(BaseDataModel):
    id: str | None = Field(alias="_id", default=None)
    code: str | None = Field(alias="code", default=None)
    product_name: str | None = Field(alias="product_name", default=None)
    generic_name: str | None = Field(alias="generic_name", default=None)
    brands: str | None = Field(alias="brands", default=None)
    categories: str | None = Field(alias="categories", default=None)
    serving_size: str | None = Field(alias="serving_size", default=None)
    serving_quantity: float | None = Field(alias="serving_quantity", default=None)
    nutriments: OpenFoodFactsNutrients | None = Field(default=None)
    quantity: str | None = Field(alias="quantity", default=None)
    packaging: str | None = Field(alias="packaging", default=None)


class OpenFoodFactsSearchResponse(BaseDataModel):
    count: int = Field(default=0)
    page: int = Field(default=1)
    page_count: int = Field(alias="page_count")
    page_size: int = Field(alias="page_size")
    products: List[OpenFoodFactsProduct] = Field(default_factory=list)


class OpenFoodFactsSingleProductResponse(BaseDataModel):
    code: str | None = Field(default=None)
    product: OpenFoodFactsProduct | None = Field(default=None)
    status: int = Field(default=0)
    status_verbose: str | None = Field(alias="status_verbose", default=None)


class OpenFoodFactsProvider(NutritionProvider):
    def __init__(self, base_url: str, http_client: HttpClient):
        self._base_url = base_url
        self._http_client = http_client

    async def lookup_upc(self, upc: str) -> NutritionProviderOutput:
        url = f"{self._base_url}/api/v2/product/{upc}.json"

        response = await self._http_client.do_request(
            url=url,
            response_model=OpenFoodFactsSingleProductResponse,
            method=HTTPMethod.GET,
            headers={
                "User-Agent": "LLIF/v3 (<EMAIL>)"
            },  # Has to be set in this format to avoid being blocked by the API
        )

        if not response.product or response.status == 0:
            raise ValueError(f"No product found for UPC '{upc}'")

        return self._convert_products_to_nutrition_output([response.product])

    async def lookup_image(self, image: bytes, name: str | None = None) -> NutritionProviderOutput:
        raise NotImplementedError("Image lookup is not supported for OpenFoodFacts provider")

    async def lookup(self, name: str, size: int = 5) -> NutritionProviderOutput:
        encoded_name = quote(name)
        url = f"{self._base_url}/cgi/search.pl"

        params = {
            "search_terms": encoded_name,
            "search_simple": "1",
            "action": "process",
            "json": "1",
            "page_size": size,
        }

        query_params = "&".join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{url}?{query_params}"

        response = await self._http_client.do_request(
            url=full_url,
            response_model=OpenFoodFactsSearchResponse,
            method=HTTPMethod.GET,
            headers={"User-Agent": "LLIF/v3 (<EMAIL>)"},
        )

        if not response.products:
            raise ValueError(f"No products found matching '{name}'")

        return self._convert_products_to_nutrition_output(response.products)

    def _convert_products_to_nutrition_output(self, products: List[OpenFoodFactsProduct]) -> NutritionProviderOutput:
        results = []
        for product in products:
            if not product.nutriments:
                continue

            name = self._get_product_name(product)
            brand = product.brands.split(",")[0].strip() if product.brands else None
            nutrients = self._extract_nutrients(product.nutriments)
            calories = product.nutriments.energy_kcal_100g

            consumed_amount, consumed_type = self._extract_serving_size(product.quantity or "100 g")

            category_info = self._classify_product(product)
            if category_info["type"] == "drink":
                consumed_type = (
                    self._convert_weight_unit_to_volume_unit(consumed_type)
                    if isinstance(consumed_type, WeightUnit)
                    else consumed_type
                )
                results.append(
                    NutritionProviderInsertDrinkInput(
                        type=DataType.Drink,
                        category=category_info["category"],
                        timestamp=datetime.now(timezone.utc),
                        name=name.lower(),
                        consumed_type=consumed_type,
                        consumed_amount=consumed_amount,
                        calories=calories,
                        nutrients=nutrients,
                        brand=brand,
                        flavor=None,
                        rating=None,
                        note=None,
                        provider=LookupProviderEnum.OPEN_FOOD_FACTS,
                    )
                )
            elif category_info["type"] == "supplement":
                # Supplements are typically measured in servings
                consumed_amount, consumed_type = 1.0, "serving"
                results.append(
                    NutritionProviderInsertSupplementInput(
                        type=DataType.Supplement,
                        category=category_info["category"],
                        timestamp=datetime.now(timezone.utc),
                        name=name.lower(),
                        consumed_type=consumed_type,
                        consumed_amount=consumed_amount,
                        calories=calories,
                        nutrients=nutrients,
                        brand=brand,
                        flavor=None,
                        rating=None,
                        note=None,
                        provider=LookupProviderEnum.OPEN_FOOD_FACTS,
                    )
                )
            else:
                results.append(
                    NutritionProviderInsertFoodInput(
                        type=DataType.Food,
                        category=category_info["category"],
                        timestamp=datetime.now(timezone.utc),
                        name=name.lower(),
                        consumed_type=consumed_type,
                        consumed_amount=consumed_amount,
                        calories=calories,
                        nutrients=nutrients,
                        brand=brand,
                        flavor=None,
                        rating=None,
                        note=None,
                        provider=LookupProviderEnum.OPEN_FOOD_FACTS,
                    )
                )

        return NutritionProviderOutput(documents=results)

    def _get_product_name(self, product: OpenFoodFactsProduct) -> str:
        if product.product_name:
            return product.product_name
        elif product.generic_name:
            return product.generic_name
        elif product.code:
            return f"Product {product.code}"
        else:
            return "Unknown Product"

    def _classify_product(self, product: OpenFoodFactsProduct) -> dict:
        categories = product.categories.lower() if product.categories else ""
        name = self._get_product_name(product).lower()
        packaging = product.packaging.lower() if product.packaging else ""

        drink_keywords = [
            "beverage",
            "drink",
            "juice",
            "water",
            "soda",
            "coffee",
            "tea",
            "milk",
            "smoothie",
            "beer",
            "wine",
            "alcohol",
            "soft-drink",
        ]
        if (
            any(keyword in categories for keyword in drink_keywords)
            or any(keyword in name for keyword in drink_keywords)
            or any(keyword in packaging for keyword in ["bottle", "can"])
        ):
            return {"type": "drink", "category": DrinkCategory.OTHER}

        supplement_keywords = [
            "supplement",
            "vitamin",
            "mineral",
            "protein powder",
            "powder",
            "capsule",
            "tablet",
            "dietary-supplement",
            "nutrition-supplement",
        ]
        if any(keyword in categories for keyword in supplement_keywords) or any(
            keyword in name for keyword in supplement_keywords
        ):
            return {"type": "supplement", "category": SupplementCategory.OTHER}

        return {"type": "food", "category": FoodCategory.OTHER}

    def _extract_serving_size(self, quantity: str) -> Tuple[float, VolumeUnit | WeightUnit]:
        # Example: "100 g" -> (100, WeightUnit.G)
        quantity_match = re.match(r"[+-]?(?:\d+(?:[.,]\d+)?|[.,]\d+)", quantity)
        if not quantity_match:
            raise ShouldNotReachHereException("No quantity match found")

        quantity_value = float(quantity_match.group(0).replace(",", "."))
        unit_value = quantity.replace(quantity_match.group(0), "").lower().strip()

        # There is no volume unitfor cl, so we convert it to ml
        if unit_value == "cl":
            return (quantity_value * 10, VolumeUnit.ML)

        unit_result = None
        try:
            unit_result = VolumeUnit(unit_value)
        except ValueError:
            try:
                unit_result = WeightUnit(unit_value)
            except ValueError:
                unit_result = None

        # default to grams if we can't find a valid unit
        return (quantity_value, unit_result) if unit_result else (quantity_value, WeightUnit.G)

    def _convert_weight_unit_to_volume_unit(self, weight_unit: WeightUnit) -> VolumeUnit:
        if weight_unit == WeightUnit.G:
            return VolumeUnit.ML
        elif weight_unit == WeightUnit.OZ:
            return VolumeUnit.FL_OZ
        elif weight_unit == WeightUnit.LB:
            return VolumeUnit.CUP
        elif weight_unit == WeightUnit.KG:
            return VolumeUnit.L
        else:
            return VolumeUnit.ML

    def _extract_nutrients(self, nutriments: OpenFoodFactsNutrients) -> Nutrients:
        sodium = nutriments.sodium_100g
        if sodium is None and nutriments.salt_100g is not None:
            sodium = nutriments.salt_100g * 0.4

        if sodium is not None and sodium > 10:
            sodium = sodium / 1000

        return Nutrients(
            fat=nutriments.fat_100g,
            saturated_fat=nutriments.saturated_fat_100g,
            trans_fat=nutriments.trans_fat_100g,
            cholesterol=nutriments.cholesterol_100g,
            carbohydrates=nutriments.carbohydrates_100g,
            fiber=nutriments.fiber_100g,
            sugar=nutriments.sugars_100g,
            protein=nutriments.proteins_100g,
            sodium=sodium,
            potassium=nutriments.potassium_100g,
            vitamin_a=nutriments.vitamin_a_100g,
            vitamin_c=nutriments.vitamin_c_100g,
            iron=nutriments.iron_100g,
            calcium=nutriments.calcium_100g,
            caffeine=nutriments.caffeine_100g,
            # Unsupported fields
            polyunsaturated_fat=None,
            monounsaturated_fat=None,
            chromium=None,
            copper=None,
            folate=None,
            iodine=None,
            niacin=None,
            phosphorus=None,
            riboflavin=None,
            selenium=None,
            thiamin=None,
            vitamin_b6=None,
            vitamin_b12=None,
            vitamin_d=None,
            vitamin_e=None,
            vitamin_k=None,
            zinc=None,
            biotin=None,
            pantothenic_acid=None,
            magnesium=None,
            manganese=None,
            molybdenum=None,
            chloride=None,
        )
