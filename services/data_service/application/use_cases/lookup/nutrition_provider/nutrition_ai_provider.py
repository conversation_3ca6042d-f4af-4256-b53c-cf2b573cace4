import json
from datetime import datetime, timezone

from pydantic_ai import Agent, BinaryContent
from pydantic_ai.models import Model

from services.base.domain.enums.data_types import DataType
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.classify_nutrition_type_use_case import (
    ClassifyNutritionTypeInputBoundary,
    ClassifyNutritionTypeUseCase,
)
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_provider import (
    DrinkAIOutput,
    FoodAIOutput,
    LookupProviderEnum,
    NutritionProvider,
    NutritionProviderInsertDrinkInput,
    NutritionProviderInsertFoodInput,
    NutritionProviderInsertSupplementInput,
    NutritionProviderOutput,
    SupplementAIOutput,
)
from services.data_service.application.use_cases.lookup.prompt.nutrition_ai_prompt import get_nutrition_ai_prompt
from services.data_service.application.use_cases.lookup.prompt.nutrition_image_ai_prompt import (
    get_nutrition_image_ai_prompt,
)


class NutritionAIProvider(NutritionProvider):
    def __init__(
        self,
        model: Model,
        classify_event_type_uc: ClassifyEventTypeUseCase,
        classify_nutrition_type_uc: ClassifyNutritionTypeUseCase,
    ):
        self._model = model
        self._classify_event_type_uc = classify_event_type_uc
        self._classify_nutrition_type_uc = classify_nutrition_type_uc

    async def lookup(self, name: str, size: int = 1) -> NutritionProviderOutput:
        return await self._lookup_from_name(name=name)

    async def lookup_image(self, image: bytes, name: str | None = None) -> NutritionProviderOutput:
        nutrition_type_output = await self._classify_nutrition_type_uc.execute(
            input_boundary=ClassifyNutritionTypeInputBoundary(image=image)
        )

        return await self._lookup_from_image(image=image, name=name, data_type=nutrition_type_output.type)

    async def lookup_upc(self, upc: str) -> NutritionProviderOutput:
        raise NotImplementedError("UPC lookup is not supported for AI provider")

    async def _lookup_from_name(self, name: str) -> NutritionProviderOutput:
        prompt_schema, return_type, output_type = await self._classify_event_type(
            name=name,
            classify_event_type_uc=self._classify_event_type_uc,
        )

        prompt = get_nutrition_ai_prompt(
            event_schema=output_type.model_json_schema(),
            category_enum=prompt_schema.category_enum,
        )
        agent = Agent(model=self._model, instructions=prompt)
        response = await agent.run(user_prompt=name)

        # Sometimes the model returns a markdown code block, so we need to remove it
        response_output = response.output.replace("```json", "").replace("```", "")
        result_dict = json.loads(response_output)

        result = None
        # Determine the correct DataType based on return_type
        if return_type == InsertFoodInput:
            result = NutritionProviderInsertFoodInput(
                type=DataType.Food,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_TEXT,
                **result_dict,
            )
        elif return_type == InsertDrinkInput:
            result = NutritionProviderInsertDrinkInput(
                type=DataType.Drink,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_TEXT,
                **result_dict,
            )
        elif return_type == InsertSupplementInput:
            result = NutritionProviderInsertSupplementInput(
                type=DataType.Supplement,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_TEXT,
                **result_dict,
            )
        else:
            raise ValueError(f"Unsupported return type: {return_type}")

        return NutritionProviderOutput(documents=[result])

    async def _lookup_from_image(self, name: str | None, image: bytes, data_type: DataType) -> NutritionProviderOutput:
        # @TODO: Should there be a better abstraction for this?
        # _classify_event_type is used for name lookup, but not for image lookup
        if data_type == DataType.Food:
            return_type = InsertFoodInput
            output_type = FoodAIOutput
        elif data_type == DataType.Drink:
            return_type = InsertDrinkInput
            output_type = DrinkAIOutput
        elif data_type == DataType.Supplement:
            return_type = InsertSupplementInput
            output_type = SupplementAIOutput
        else:
            raise ValueError(f"Data type {data_type} is not supported")

        prompt_schema = PromptSchema.from_event_type(event_type=return_type)

        prompt = get_nutrition_image_ai_prompt(
            event_schema=output_type.model_json_schema(),
            category_enum=prompt_schema.category_enum,
        )

        user_prompt = [prompt, BinaryContent(data=image, media_type="image/jpeg")]

        agent = Agent(model=self._model)
        response = await agent.run(user_prompt=user_prompt)

        # Sometimes the model returns a markdown code block, so we need to remove it
        response_output = response.output.replace("```json", "").replace("```", "")
        result_dict = json.loads(response_output)

        result = None
        # Determine the correct DataType based on return_type
        if return_type == InsertFoodInput:
            result = NutritionProviderInsertFoodInput(
                type=DataType.Food,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_PHOTO,
                **result_dict,
            )
        elif return_type == InsertDrinkInput:
            result = NutritionProviderInsertDrinkInput(
                type=DataType.Drink,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_PHOTO,
                **result_dict,
            )
        elif return_type == InsertSupplementInput:
            result = NutritionProviderInsertSupplementInput(
                type=DataType.Supplement,
                timestamp=datetime.now(timezone.utc),
                provider=LookupProviderEnum.AI_PHOTO,
                **result_dict,
            )
        else:
            raise ValueError(f"Unsupported return type: {return_type}")

        return NutritionProviderOutput(documents=[result])
