from abc import ABC, abstractmethod
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.metadata_v3 import SourceService
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory
from services.base.domain.schemas.events.nutrition.food import FoodCategory
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)


class ThirdPartyProvider(StrEnum):
    USDA_FOOD_DB = SourceService.USDA_FOOD_DB
    OPEN_FOOD_FACTS = SourceService.OPEN_FOOD_FACTS


class LookupProviderEnum(StrEnum):
    USDA_FOOD_DB = SourceService.USDA_FOOD_DB
    OPEN_FOOD_FACTS = SourceService.OPEN_FOOD_FACTS
    AI_PHOTO = SourceService.AI_PHOTO
    AI_TEXT = SourceService.AI_TEXT


class NutritionProviderInsertFoodInput(InsertFoodInput):
    provider: LookupProviderEnum


class NutritionProviderInsertDrinkInput(InsertDrinkInput):
    provider: LookupProviderEnum


class NutritionProviderInsertSupplementInput(InsertSupplementInput):
    provider: LookupProviderEnum


class NutritionProviderOutput(BaseDataModel):
    documents: list[
        NutritionProviderInsertFoodInput | NutritionProviderInsertDrinkInput | NutritionProviderInsertSupplementInput
    ]


class FoodAIOutput(NutritionCollection):
    category: FoodCategory
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    name: str = Field(alias=NutritionFields.NAME)


class DrinkAIOutput(NutritionCollection):
    category: DrinkCategory
    consumed_type: VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    name: str = Field(alias=NutritionFields.NAME)


class SupplementAIOutput(NutritionCollection):
    category: SupplementCategory
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    name: str = Field(alias=NutritionFields.NAME)


class NutritionProvider(ABC):
    @abstractmethod
    async def lookup(
        self,
        name: str,
        size: int = 5,
    ) -> NutritionProviderOutput:
        raise NotImplementedError

    @abstractmethod
    async def lookup_image(self, image: bytes, name: str | None = None) -> NutritionProviderOutput:
        raise NotImplementedError

    @abstractmethod
    async def lookup_upc(self, upc: str) -> NutritionProviderOutput:
        raise NotImplementedError

    async def _classify_event_type(
        self,
        name: str,
        classify_event_type_uc: ClassifyEventTypeUseCase,
    ) -> tuple[
        PromptSchema,
        type[InsertFoodInput] | type[InsertDrinkInput] | type[InsertSupplementInput],
        type[FoodAIOutput] | type[DrinkAIOutput] | type[SupplementAIOutput],
    ]:
        prompt_schema = await classify_event_type_uc.execute(query=name)
        if prompt_schema.event_type == InsertFoodInput:
            return_type = InsertFoodInput
            output_type = FoodAIOutput
        elif prompt_schema.event_type == InsertDrinkInput:
            return_type = InsertDrinkInput
            output_type = DrinkAIOutput
        elif prompt_schema.event_type == InsertSupplementInput:
            return_type = InsertSupplementInput
            output_type = SupplementAIOutput
        else:
            raise ValueError(f"Event type {prompt_schema.event_type} is not supported")
        return prompt_schema, return_type, output_type
