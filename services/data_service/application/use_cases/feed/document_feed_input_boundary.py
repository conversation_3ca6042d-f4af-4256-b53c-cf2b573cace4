from services.base.application.database.models.sorts import Sort
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.models.feed_continuation_token import FeedContinuationToken


class DocumentFeedInputBoundary(BaseDataModel):
    limit: int
    sort: Sort
    continuation_token: FeedContinuationToken | None = None
    query: Query
