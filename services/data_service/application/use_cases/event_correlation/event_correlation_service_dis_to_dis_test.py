import pandas as pd
import pytest

from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    AnalysisRelationshipLabel,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_service import CorrelationService


def test_calculate_dis_to_dis_no_relationship():
    data = pd.DataFrame(
        {
            "dependent": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2],
            "independent": [0, 0, 1, 1, 2, 0, 0, 1, 1, 2, 0, 0, 1, 1, 2],
        }
    )
    result = CorrelationService.calculate_dis_to_dis(df=data)
    assert result.p_value == 1.0
    assert result.correlation_coefficient < 0.10
    assert result.relationship == AnalysisRelationshipLabel.NO_RELATIONSHIP


def test_calculate_dis_to_dis_potential_relationship():
    data = pd.DataFrame(
        {
            "dependent": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 0, 1, 2],
            "independent": [0, 0, 1, 1, 2, 0, 1, 1, 2, 2, 1, 1, 2, 2, 2, 0, 0, 0],
        }
    )
    result = CorrelationService.calculate_dis_to_dis(df=data)

    assert pytest.approx(result.correlation_coefficient, abs=0.01) == 0.23
    assert result.relationship == AnalysisRelationshipLabel.POTENTIAL_RELATIONSHIP


def test_calculate_dis_to_dis_strong_relationship():
    data = pd.DataFrame(
        {
            "dependent": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],
            "independent": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],
        }
    )
    result = CorrelationService.calculate_dis_to_dis(df=data)

    assert result.relationship == AnalysisRelationshipLabel.STRONG_RELATIONSHIP
