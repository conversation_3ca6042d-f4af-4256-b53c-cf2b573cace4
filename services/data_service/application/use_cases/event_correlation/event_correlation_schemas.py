from enum import Enum
from typing import Optional, Sequence

from pydantic import Field

from services.base.domain.schemas.events.document_base import TimestampDocument
from services.base.domain.schemas.shared import BaseDataModel


class AnalysisCertainty(Enum):
    STRONG_EVIDENCE = "strong evidence"
    MODERATE_EVIDENCE = "moderate evidence"
    WEAK_EVIDENCE = "weak evidence"
    INSUFFICIENT_EVIDENCE = "insufficient evidence"


class AnalysisRelationshipLabel(Enum):
    NO_RELATIONSHIP = "no relationship"
    POTENTIAL_RELATIONSHIP = "potential relationship"
    VERY_WEAK_RELATIONSHIP = "very weak relationship"
    WEAK_RELATIONSHIP = "weak relationship"
    MODERATE_RELATIONSHIP = "moderate relationship"
    STRONG_RELATIONSHIP = "strong relationship"


class EventCorrelationAggregate(BaseDataModel):
    correlation_coefficient: float = Field(ge=-1, le=1)
    p_value: float = Field(ge=0, le=1)
    degree_of_freedom: Optional[int] = Field(default=None, ge=0)
    covariance: Optional[float] = Field(default=None)
    certainty: AnalysisCertainty
    relationship: AnalysisRelationshipLabel


class SingleCorrelationVariableDocument(TimestampDocument):
    value: float


class CorrelationVariableDocuments(BaseDataModel):
    field_name: str | None
    values: Sequence[SingleCorrelationVariableDocument]
