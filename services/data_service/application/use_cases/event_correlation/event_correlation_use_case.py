import logging
from datetime import datetime, timedelta
from typing import Literal, Sequence, cast
from uuid import UUID

from pydantic import Field, model_validator

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.application.boundaries.time_input import TimeInput, TimeRangeInput
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.application.exceptions import IncorrectOperationException, NoContentException
from services.base.application.utils.hashmap import HashMapUtils
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import TimestampDocument
from services.base.domain.schemas.query.aggregations import (
    AggregationMethod,
    DateHistogramAggregation,
    FieldAggregation,
    SimpleAggregationMethod,
)
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.type_resolver import TypeResolver
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI
from services.data_service.application.services.environment_service import EnvironmentService
from services.data_service.application.use_cases.aggregate_location_use_case import AggregateLocationUseCase
from services.data_service.application.use_cases.event_correlation.document_matcher import DocumentMatcher
from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    CorrelationVariableDocuments,
    EventCorrelationAggregate,
    SingleCorrelationVariableDocument,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_service import (
    CorrelationService,
    CorrelationServiceInput,
)
from settings.app_routes import AppRoutes


class CorrelationTemporalOptions(TimeInput):
    type: Literal["before", "after", "closest"]

    delta_from: timedelta
    delta_to: timedelta


class EnvironmentTypedQuery(BaseDataModel):
    domain_type: Literal[DataType.AirQuality, DataType.Weather, DataType.Pollen]


class CorrelationVariableInput(BaseDataModel):
    field_name: NonEmptyStr | None
    query: EventTypedQueryAPI | EnvironmentTypedQuery
    aggregation_method: SimpleAggregationMethod | None

    @model_validator(mode="after")
    def validate_query(self):
        if self.aggregation_method and not self.field_name:
            raise ValueError("Field name must be specified for aggregation")
        if isinstance(self.query, EnvironmentTypedQuery):
            if self.aggregation_method is None:
                raise ValueError("Aggregation method must be specified for environment typed query")
        return self

    @model_validator(mode="after")
    def validate_field_type(self):
        if self.field_name:
            if isinstance(self.query, EnvironmentTypedQuery):
                tps = [TypeResolver.get_environment(self.query.domain_type)]
            else:
                tps = [t.to_domain_model() for t in self.query.types]
            for t in tps:
                FieldValidator.validate_field(field_name=self.field_name, must_be_numeric=True, parent_type=t)
        return self


class EventCorrelationUseCaseInputBoundary(BaseDataModel):
    dependent: CorrelationVariableInput
    independent: CorrelationVariableInput

    temporal_options: CorrelationTemporalOptions
    owner_id: UUID
    doc_limit: int = 100000


class CorrelationVariableAggregate(BaseDataModel):
    count: int = Field(ge=0)


class EventCorrelationUseCaseOutputBoundary(BaseDataModel):
    data: Sequence[tuple[float, float]]
    independent: CorrelationVariableAggregate
    dependent: CorrelationVariableAggregate
    correlation: EventCorrelationAggregate  # TODO: make union based on used correlation algorithm
    suggested_visualisation: Literal["box_plot", "scatter_plot", "heat_map"]


class EventCorrelationUseCase:
    def __init__(
        self,
        agg_service: AggregationService,
        search_service: DocumentSearchService,
        environment_service: EnvironmentService,
        location_use_case: AggregateLocationUseCase,
    ):
        self._agg_service = agg_service
        self._search_service = search_service
        self._environment_service = environment_service
        self._agg_location_use_case = location_use_case

    async def execute_async(
        self,
        input_boundary: EventCorrelationUseCaseInputBoundary,
    ) -> EventCorrelationUseCaseOutputBoundary:
        dependent_docs, dep_count = await self._get_docs(
            variable=input_boundary.dependent,
            owner_id=input_boundary.owner_id,
            time_input=input_boundary.temporal_options,
            doc_limit=input_boundary.doc_limit,
        )
        independent_docs, indep_count = await self._get_docs(
            input_boundary.independent,
            owner_id=input_boundary.owner_id,
            time_input=input_boundary.temporal_options,
            doc_limit=input_boundary.doc_limit,
        )

        pairs = self._get_pairs(
            dependent_docs=dependent_docs,
            independent_docs=independent_docs,
            temporal_options=input_boundary.temporal_options,
        )
        logging.info(f"Found proto pairs: {pairs}")
        if not input_boundary.dependent.field_name:
            pairs = [
                *pairs,
                *(
                    await self._handle_selection_bias(
                        independent=input_boundary.independent,
                        user_id=input_boundary.owner_id,
                        dependent_docs=dependent_docs.values,
                        independent_docs=independent_docs.values,
                        temporal_options=input_boundary.temporal_options,
                    )
                ),
            ]
            logging.info(f"Pairs after selection bias: {pairs}")

        correlation_output = CorrelationService.analyze_correlation(
            spearman_override=True,
            input_boundary=CorrelationServiceInput(
                data=pairs,
                independent_field_name=bool(input_boundary.independent.field_name),
                dependent_field_name=bool(input_boundary.dependent.field_name),
            ),
        )

        suggested_visualisation = self._determine_visualization_type(
            dependent_type=correlation_output.dependent_type, independent_type=correlation_output.independent_type
        )

        return EventCorrelationUseCaseOutputBoundary(
            data=correlation_output.data,
            suggested_visualisation=suggested_visualisation,
            dependent=CorrelationVariableAggregate(
                count=dep_count,
            ),
            independent=CorrelationVariableAggregate(
                count=indep_count,
            ),
            correlation=correlation_output.correlation,
        )

    async def _get_docs(
        self, variable: CorrelationVariableInput, owner_id: UUID, time_input: TimeInput, doc_limit: int
    ) -> tuple[CorrelationVariableDocuments, int]:
        if isinstance(variable.query, EnvironmentTypedQuery):
            docs = await self._fetch_environment_docs(variable=variable, owner_id=owner_id, time_input=time_input)
            if len(docs.values) == 0:
                raise NoContentException(f"No data for environment typed query, variable: {variable}")
        else:
            docs = await self._fetch_event_docs(
                variable=variable, owner_id=owner_id, time_input=time_input, doc_limit=doc_limit
            )
            if len(docs.values) == 0:
                raise NoContentException(f"No data for event typed query, variable: {variable}")
        return docs, len(docs.values)

    async def _fetch_event_docs(
        self, variable: CorrelationVariableInput, owner_id: UUID, time_input: TimeInput, doc_limit: int
    ) -> CorrelationVariableDocuments:
        docs = []
        variable_query = self._build_variable_query(
            variable=variable,
            owner_id=owner_id,
            time_range=time_input,
        )
        await self._validate_query_counts(query=variable_query, doc_limit=doc_limit)

        if variable.aggregation_method:
            if not variable.field_name:
                raise IncorrectOperationException("Field name must be specified for aggregation")
            logging.info(f"Aggregating variable: {variable}")
            response = await self._agg_service.date_histogram_by_query(
                query=variable_query,
                aggregation=DateHistogramAggregation(
                    default_aggregation_method=AggregationMethod(variable.aggregation_method.value),
                    histogram_field_aggregations=[
                        FieldAggregation(
                            field_name=variable.field_name,
                        )
                    ],
                    interval=time_input.interval,
                ),
            )
            for doc in response:
                docs.append(
                    SingleCorrelationVariableDocument(
                        timestamp=doc.timestamp,
                        value=doc.aggregates[0].value or 0,
                    )
                )

        else:
            response = await self._fetch_all_documents(query=variable_query)
            logging.info(f"Fetched {len(response)} docs for variable: {variable}")
            for doc in response:
                value = 1
                if variable.field_name:
                    value = HashMapUtils.get_nested_field_value(doc, variable.field_name)
                docs.append(
                    SingleCorrelationVariableDocument(
                        timestamp=doc.timestamp,
                        value=value,  # type: ignore
                    )
                )
        return CorrelationVariableDocuments(field_name=variable.field_name, values=docs)

    async def _fetch_environment_docs(
        self, variable: CorrelationVariableInput, owner_id: UUID, time_input: TimeInput
    ) -> CorrelationVariableDocuments:
        docs = []
        if not isinstance(variable.query, EnvironmentTypedQuery):
            raise ShouldNotReachHereException("Expected EnvironmentTypedQuery")

        user_space_time = await self._fetch_location(user_uuid=owner_id, time_input=time_input)
        if not variable.field_name or not variable.aggregation_method:
            raise IncorrectOperationException(
                "Field name and aggregation method must be specified for environment typed query"
            )
        backfill_url = self._environment_routes_map[variable.query.domain_type]
        logging.info(f"Fetching environment data for variable: {variable}")
        response, _ = await self._environment_service.fetch_environment(
            space_time_data=user_space_time,
            time_input=time_input,
            domain_type=TypeResolver.get_environment(variable.query.domain_type),
            requested_fields=[
                (variable.field_name, variable.aggregation_method),
            ],
            backfill_url=backfill_url,
            backfill_threshold=1,
        )
        for timestamp, doc in response.items():
            value = doc.get(variable.field_name)
            if value is None:
                # Currently throwing away empty environment buckets.
                continue
            docs.append(
                SingleCorrelationVariableDocument(
                    timestamp=timestamp,
                    value=value,
                )
            )
        return CorrelationVariableDocuments(field_name=variable.field_name, values=docs)

    def _build_variable_query(
        self, variable: CorrelationVariableInput, owner_id: UUID, time_range: TimeRangeInput
    ) -> Query:
        if isinstance(variable.query, EventTypedQueryAPI):
            type_q = variable.query.to_type_query()
        else:
            raise ShouldNotReachHereException("Environment typed query does not have a to_type_query method.")

        bool_builder = BooleanQueryBuilder()
        bool_builder.add_query(
            CommonLeafQueries.timestamp_range_query(gte=time_range.time_gte, lte=time_range.time_lte)
        )
        if variable.field_name:
            bool_builder.add_query(type_q.query)
            bool_builder.add_query(ExistsQuery(field_name=variable.field_name))

        type_q = TypeQuery(domain_types=type_q.domain_types, query=bool_builder.build_and_query())
        query = Query(type_queries=[type_q])
        query = CommonQueryAdjustments.add_user_uuid_to_query(user_uuid=owner_id, query=query)
        return query

    async def _validate_query_counts(self, query: Query, doc_limit: int):
        count = await self._search_service.count_by_query(query=query)
        if count == 0:
            raise NoContentException("No data for dependent or independent variable")
        # TODO: If continuous, aggregate instead
        if count > doc_limit:
            raise IncorrectOperationException(
                "Too many data points for dependent or independent variable. Adjust the query."
            )

    @classmethod
    def _get_pairs(
        cls,
        dependent_docs: CorrelationVariableDocuments,
        independent_docs: CorrelationVariableDocuments,
        temporal_options: CorrelationTemporalOptions,
    ) -> Sequence[tuple[float, Sequence[float]]]:
        results = []

        for dep_doc in dependent_docs.values:
            # TODO: Only consider documents that were not used before?
            matched_docs = DocumentMatcher.find_matching_docs(
                ts=dep_doc.timestamp,
                docs=independent_docs.values,
                temporal_type=temporal_options.type,
                delta_to=temporal_options.delta_to,
                delta_from=temporal_options.delta_from,
            )

            results.append((dep_doc.value, [doc.value for doc in matched_docs]))

        if not any(p2 for _, p2 in results):
            raise NoContentException("No pairs found for dependent and independent variables in the given windows.")
        return results

    async def _fetch_all_documents(self, query: Query) -> Sequence[TimestampDocument]:
        """Fetch all documents matching the query using pagination with continuation tokens"""
        documents = []
        continuation_token = None

        while True:
            results = await self._search_service.search_documents_by_query(
                query=query,
                size=1000,
                sorts=[CommonSorts.timestamp(order=SortOrder.ASCENDING)],
                continuation_token=continuation_token,
            )
            documents.extend(results.documents)
            continuation_token = results.continuation_token
            if not continuation_token:
                break
        documents = cast(Sequence[TimestampDocument], documents)
        return documents

    @staticmethod
    def _determine_visualization_type(
        dependent_type: str, independent_type: str
    ) -> Literal["scatter_plot", "box_plot", "heat_map"]:
        """Determine the appropriate visualization type based on variable types"""
        if dependent_type == "continuous" and independent_type == "continuous":
            return "scatter_plot"
        elif dependent_type == "continuous" or independent_type == "continuous":
            return "box_plot"
        else:
            return "heat_map"

    async def _find_active_buckets(
        self,
        start_time: datetime,
        end_time: datetime,
        user_id: UUID,
        interval: str,
    ):
        """Find active time buckets within the given time range"""
        query = Query(
            type_queries=[
                TypeQuery(
                    domain_types=[et.to_event_model() for et in EventV3Type],
                    query=RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=start_time, lte=end_time),
                )
            ]
        )
        query = CommonQueryAdjustments.add_user_uuid_to_query(user_uuid=user_id, query=query)
        aggs = await self._agg_service.date_histogram_by_query(
            query=query,
            aggregation=DateHistogramAggregation(
                default_aggregation_method=AggregationMethod.SUM,
                histogram_field_aggregations=[],
                interval=interval,
            ),
        )
        return [agg.timestamp for agg in aggs if agg.doc_count > 0]

    async def _handle_selection_bias(
        self,
        independent: CorrelationVariableInput,
        dependent_docs: Sequence[SingleCorrelationVariableDocument],
        independent_docs: Sequence[SingleCorrelationVariableDocument],
        user_id: UUID,
        temporal_options: CorrelationTemporalOptions,
    ):
        # First find active buckets
        st = dependent_docs[0].timestamp
        et = dependent_docs[-1].timestamp
        active_buckets = await self._find_active_buckets(
            start_time=st,
            end_time=et,
            user_id=user_id,
            interval=temporal_options.interval,
        )

        # now get active buckets without dependent
        non_overlapping_buckets = []

        # For each active bucket, check if it overlaps with any dependent document based on temporal relationship
        for bucket_timestamp in active_buckets:
            has_overlap = DocumentMatcher.find_matching_docs(
                ts=bucket_timestamp,
                docs=dependent_docs,
                temporal_type=temporal_options.type,
                delta_to=temporal_options.delta_to,
                delta_from=temporal_options.delta_from,
            )

            if not has_overlap:
                non_overlapping_buckets.append(bucket_timestamp)

        # Go through the buckets and assign either 0 -> 0 if independent did not happen or 0 -> 1 if it did
        pairs = []
        for bucket in non_overlapping_buckets:
            matching_independent_docs = DocumentMatcher.find_matching_docs(
                ts=bucket,
                docs=independent_docs,
                temporal_type=temporal_options.type,
                delta_to=temporal_options.delta_to,
                delta_from=temporal_options.delta_from,
            )
            if independent.field_name:
                pairs.append([0.0, [d.value for d in matching_independent_docs]])
            else:
                pairs.append([0.0, [1.0 for _ in matching_independent_docs]])

        return pairs

    async def _fetch_location(self, user_uuid: UUID, time_input: TimeInput) -> Sequence[SpaceTimeCoordinates]:
        output = await self._agg_location_use_case.execute_async(
            user_uuid=user_uuid,
            time_input=time_input,
            metadata=MetadataParametersInputBoundary(),
        )
        return [SpaceTimeCoordinates.map(r) for r in output.results if r.latitude and r.longitude and r.end_time]

    _environment_routes_map = {
        DataType.Weather: AppRoutes.WEATHER_V2_ROUTE,
        DataType.AirQuality: AppRoutes.AIR_QUALITY_V2_ROUTE,
        DataType.Pollen: AppRoutes.POLLEN_V2_ROUTE,
    }
