from enum import Str<PERSON>num
from typing import List, Optional, Sequence

import numpy as np

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.schemas.shared import BaseDataModel


class AnomalyType(StrEnum):
    SPIKE = "spike"
    DIP = "dip"


class Anomaly(BaseDataModel):
    index: int
    label: Optional[str]
    value: RoundedFloat
    anomaly_type: AnomalyType
    description: str


class AnomalyDetection:
    @staticmethod
    def iqr_detect(data_series: Sequence[float], x_labels: Sequence[str] | None = None, k: float = 1) -> List[Anomaly]:
        """
        Detects outliers using the Interquartile Range (IQR) method and returns Anomaly objects.
        """
        anomalies = []
        data_series_np = np.array(data_series)
        if data_series_np.size < 2 or np.all(data_series_np == data_series_np[0]):
            return []

        quartile1 = np.percentile(data_series_np, 25)
        quartile3 = np.percentile(data_series_np, 75)
        interquartile_range = quartile3 - quartile1

        if interquartile_range == 0:
            unique_values = np.unique(data_series_np)
            if len(unique_values) <= 1:
                return []
            lower_bound = quartile1
            upper_bound = quartile3
        else:
            lower_bound = quartile1 - k * interquartile_range
            upper_bound = quartile3 + k * interquartile_range

        for i, x in enumerate(data_series_np):
            anomaly_type = None
            description = ""
            is_anomaly = False
            if x < lower_bound:
                deviation = lower_bound - x
                description = f"Low outlier: {x:.1f} (below lower bound {lower_bound:.1f} by {deviation:.1f})"
                anomaly_type = AnomalyType.DIP
                is_anomaly = True
            elif x > upper_bound:
                deviation = x - upper_bound
                description = f"High outlier: {x:.1f} (above upper bound {upper_bound:.1f} by {deviation:.1f})"
                anomaly_type = AnomalyType.SPIKE
                is_anomaly = True

            if is_anomaly and anomaly_type is not None:
                x_label = None
                if x_labels:
                    x_label = x_labels[i]
                    description += f" on {x_label}"
                description += "."
                anomalies.append(
                    Anomaly(
                        index=i,
                        label=x_label,
                        value=x,
                        anomaly_type=anomaly_type,
                        description=description,
                    )
                )
        return anomalies
