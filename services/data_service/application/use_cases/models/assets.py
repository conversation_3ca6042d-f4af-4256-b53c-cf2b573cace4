from typing import List, Optional

from services.base.application.input_validators.shared import InputAssetModel
from services.base.domain.annotated_types import AssetId
from services.base.domain.enums.assets_enums import AssetType
from services.base.domain.schemas.shared import AssetReferenceModel, BaseDataModel


class DocumentWithAssetReference[T: InputAssetModel](BaseDataModel):
    document: T
    assets: Optional[List[AssetReferenceModel]] = None


class OutputAssetReferenceModel(BaseDataModel):
    asset_type: AssetType
    asset_id: AssetId
