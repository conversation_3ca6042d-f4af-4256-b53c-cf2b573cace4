import re
from urllib.parse import ParseR<PERSON>ult, parse_qs, unquote, urlparse

from googleapiclient.discovery import build

from services.base.domain.schemas.events.content.content import ContentCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.handlers.content_handler_base import ContentHandlerBase
from services.data_service.application.use_cases.content.models.youtube import Video
from settings.app_secrets import secrets


class YouTubeHandler(ContentHandlerBase):
    """Handler for YouTube content that includes service functionality."""

    def __init__(self):
        self._yt = build(serviceName="youtube", version="v3", developerKey=secrets.YOUTUBE_API_KEY)

    async def can_handle(self, url: str) -> bool:
        parsed_url = self._parse_url(url=url)
        return parsed_url.netloc.replace("www.", "") in ["youtube.com", "youtu.be"]

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        parsed_url = self._parse_url(url)
        video = self._get_video_details(url=parsed_url.geturl())
        return self._normalize_output(
            title=video.title.strip(),
            category=ContentCategory.CONTENT,
            description=video.description.strip() if video.description else None,
        )

    @staticmethod
    def _parse_url(url: str) -> ParseResult:
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        if "continue" in query_params:
            continue_url = unquote(query_params["continue"][0])
            return urlparse(continue_url)
        return parsed_url

    def _get_video_details(self, url: str) -> Video:
        """Get video details from YouTube API."""
        search = re.search(r"(?:v=|\/)([0-9A-Za-z_-]{11}).*", url)
        if not search:
            raise ValueError(f"did not find video id in url: {url}")

        video_id: str = search.group(1)
        response = (
            self._yt.videos()
            .list(
                id=video_id,
                part="snippet",
            )
            .execute()
        )
        if not response.get("items"):
            raise ValueError(f"no video found with id: {video_id}")

        snippet = response["items"][0]["snippet"]
        return Video(**snippet)
