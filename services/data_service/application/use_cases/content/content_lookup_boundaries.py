from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory
from services.base.domain.schemas.events.content.content import ContentCategory
from services.base.domain.schemas.shared import BaseDataModel


class ContentLookupOutputBoundary(BaseDataModel):
    type: Literal[DataType.Content, DataType.Activity]
    title: NonEmptyStr = Field(description="title of the webpage as provided by the url")
    category: ContentCategory | ActivityCategory = Field(
        description="type of the content, e.g. article, video, website"
    )
    description: NonEmptyStr | None = Field(
        description="description provided by the website about the url",
    )
