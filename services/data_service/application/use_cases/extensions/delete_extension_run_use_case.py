from typing import List
from uuid import UUID

from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository


class DeleteExtensionRunUseCase:
    def __init__(self, extension_run_repo: ExtensionRunRepository, extension_result_repo: ExtensionResultRepository):
        self._extension_run_repo = extension_run_repo
        self._extension_result_repo = extension_result_repo

    async def execute_async(self, run_ids: List[UUID], delete_child_documents: bool = True) -> List[UUID]:
        """
        Deletes extension run documents and their child documents

        Returns: list of deleted UUIDs
        """
        deleted_uuid_list = []
        if delete_child_documents:
            runs = await self._extension_run_repo.search_by_id(ids=run_ids)
            for run in runs:
                child_documents = await self._extension_result_repo.get_all_children(parent_id=run.id)
                if child_documents:
                    deleted_child_ids = await self._extension_result_repo.delete_by_id(
                        ids=[child_document.id for child_document in child_documents]
                    )
                    deleted_uuid_list.extend(deleted_child_ids)

        deleted_run_ids = await self._extension_run_repo.delete_by_id(ids=run_ids)
        deleted_uuid_list.extend(deleted_run_ids)

        return deleted_uuid_list
