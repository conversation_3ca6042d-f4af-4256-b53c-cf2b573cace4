from typing import List, Optional
from uuid import UUID

from pydantic import AwareDatetime

from services.base.application.boundaries.documents import SearchDocumentsOutputBoundary
from services.base.application.database.models.filter_types import (
    RangeFilter,
)
from services.base.application.database.models.sorts import Sort
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import ExtensionRun
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import RangeQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.models.list_extension_runs_continuation_token import (
    ListExtensionRunsContinuationToken,
)


class ListExtensionRunsOutputBoundary(BaseDataModel):
    extension_runs: List[ExtensionRun]
    continuation_token: ListExtensionRunsContinuationToken


class ListExtensionRunsUseCase:
    def __init__(self, extension_run_repo: ExtensionRunRepository):
        self._extension_run_repo = extension_run_repo

    async def execute_async(
        self,
        user_uuid: UUID,
        limit: int,
        sort: Sort,
        continuation_token: Optional[ListExtensionRunsContinuationToken] = None,
        range_filter: Optional[RangeFilter] = None,
        extension_ids: Optional[List[UUID]] = None,
    ) -> ListExtensionRunsOutputBoundary:
        query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    ValuesQuery(
                        field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}", values=[str(user_uuid)]
                    ),
                    (
                        ValuesQuery(
                            field_name=f"{DocumentLabels.METADATA}.{ExtensionLabels.EXTENSION_ID}",
                            values=[str(extension_id) for extension_id in extension_ids],
                        )
                        if extension_ids
                        else None
                    ),
                    (
                        RangeQuery[AwareDatetime](
                            field_name=DocumentLabels.TIMESTAMP, gte=range_filter.gte, lte=range_filter.lte
                        )
                        if range_filter
                        else None
                    ),
                ]
            )
            .build_and_query()
        )
        query = Query(type_queries=[TypeQuery(domain_types=[ExtensionRun], query=query)])

        output: SearchDocumentsOutputBoundary[ExtensionRun] = await self._extension_run_repo.search_by_query(
            query=query,
            continuation_token=continuation_token.token if continuation_token else None,
            size=limit,
            sorts=[sort, Sort(name=DocumentLabels.UNDERSCORE_ID, order=sort.order)],
        )

        if not output.results:
            raise NoContentException(
                f"No extension runs found for given range_filter:{range_filter} and extension_ids{extension_ids}."
            )

        return ListExtensionRunsOutputBoundary(
            extension_runs=[result.document for result in output.results],
            continuation_token=ListExtensionRunsContinuationToken(
                token=output.continuation_token,
                extension_ids=extension_ids,
            ),
        )
