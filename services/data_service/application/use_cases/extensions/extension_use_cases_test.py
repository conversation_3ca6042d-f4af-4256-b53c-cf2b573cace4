import datetime
from typing import Awaitable, Callable
from uuid import uuid4

from services.base.application.database.models.sorts import Sort, SortOrder
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.extension_output_builder import (
    <PERSON><PERSON><PERSON>ult<PERSON>uilder,
    ExtensionRunBuilder,
)
from services.data_service.application.use_cases.extensions.delete_extension_run_use_case import (
    DeleteExtensionRunUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_results_use_case import (
    ListExtensionResultsUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_runs_use_case import (
    ListExtensionRunsOutputBoundary,
    ListExtensionRunsUseCase,
)
from services.data_service.application.use_cases.extensions.update_extension_run_status_use_case import (
    UpdateExtensionRunStatusUseCase,
)
from services.data_service.dependency_bootstrapper import DependencyBootstrapper


class TestExtensionUseCases:
    async def test_list_extension_runs_use_case(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], Awaitable[MemberUser]]
    ):
        # Arrange
        user = await user_factory()
        user_uuid = user.user_uuid
        timestamp = datetime.datetime(year=2023, month=1, day=1, tzinfo=datetime.timezone.utc)
        extension_id = uuid4()

        use_case = dependency_bootstrapper.get(interface=ListExtensionRunsUseCase)
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        run_document1 = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        run_document2 = ExtensionRunBuilder().with_user_uuid(user_uuid).with_extension_id(extension_id).build()
        run_document3 = ExtensionRunBuilder().with_user_uuid(user_uuid).build()
        runs = await extension_run_repo.insert(extension_runs=[run_document1, run_document2, run_document3])
        inserted_ids = [run.id for run in runs]

        # Act
        documents: ListExtensionRunsOutputBoundary = await use_case.execute_async(
            user_uuid=user_uuid,
            limit=100,
            sort=Sort(name=DocumentLabels.TIMESTAMP, order=SortOrder.DESCENDING),
            extension_ids=[extension_id],
        )
        # Asserts
        for document in documents.extension_runs:
            document_id = document.id
            assert any(
                doc.id == document_id and doc.model_dump() == document.model_dump()
                for doc in [run_document1, run_document2, run_document3]
            )

        # Teardown
        deleted_ids = await extension_run_repo.delete_by_id(ids=[run.id for run in runs])
        for deleted_id in deleted_ids:
            assert deleted_id in inserted_ids

    async def test_list_extension_results_use_case(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], Awaitable[MemberUser]]
    ):
        # Arrange
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        extension_output_repo = dependency_bootstrapper.get(interface=ExtensionResultRepository)
        use_case = dependency_bootstrapper.get(interface=ListExtensionResultsUseCase)

        user = await user_factory()
        user_uuid = user.user_uuid
        timestamp = datetime.datetime(year=2023, month=1, day=1, tzinfo=datetime.timezone.utc)
        extension_id = uuid4()

        run_document1 = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        run_document2 = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        child1 = (
            ExtensionResultBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        child2 = (
            ExtensionResultBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        child3 = (
            ExtensionResultBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )

        runs = await extension_run_repo.insert(extension_runs=[run_document1, run_document2])
        children = await extension_output_repo.insert(extension_results=[child1, child2], parent_id=run_document1.id)
        children_different_parent = await extension_output_repo.insert(
            extension_results=[child3], parent_id=run_document2.id
        )
        inserted_ids = [run.id for run in runs] + [child.id for child in children]

        # Asserts
        documents = await use_case.execute_async(run_id=run_document1.id)
        for document in documents:
            document_id = document.id
            translation_table = str.maketrans("'\"", "\"'")
            document.output = (
                str(document.output).translate(translation_table).replace(" ", "")  # pyright: ignore
            )  # Added to compensate for conversion to Dict in the usecase
            assert any(doc.id == document_id and doc.model_dump() == document.model_dump() for doc in [child1, child2])

        for child_with_different_parent in children_different_parent:
            assert child_with_different_parent.model_dump() not in [doc.model_dump() for doc in documents]

        # Teardown
        deleted_ids = await extension_run_repo.delete_by_id(ids=[run.id for run in runs])
        deleted_children_id = await extension_output_repo.delete_by_id(ids=[child.id for child in children])
        _ = await extension_output_repo.delete_by_id(ids=[child.id for child in children_different_parent])
        for deleted_id in [*deleted_ids, *deleted_children_id]:
            assert deleted_id in inserted_ids

    async def test_update_use_case(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], Awaitable[MemberUser]]
    ):
        # Arrange
        run_document1 = ExtensionRunBuilder().with_extension_status(ExtensionStatus.RUNNING).build()
        run_document2 = ExtensionRunBuilder().with_extension_status(ExtensionStatus.RUNNING).build()
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        use_case = dependency_bootstrapper.get(interface=UpdateExtensionRunStatusUseCase)

        runs = await extension_run_repo.insert(extension_runs=[run_document1, run_document2])
        inserted_ids = [run.id for run in runs]

        updated_runs = await use_case.execute_async(run_ids=[run_document1.id], status=ExtensionStatus.SUCCEEDED)
        # Asserts
        for run in updated_runs:
            assert run.extension_status == ExtensionStatus.SUCCEEDED

        # Teardown
        deleted_ids = await extension_run_repo.delete_by_id(ids=[run.id for run in runs])
        for deleted_id in deleted_ids:
            assert deleted_id in inserted_ids

    async def test_delete_use_case(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], Awaitable[MemberUser]]
    ):
        # Arrange
        user = await user_factory()
        user_uuid = user.user_uuid
        timestamp = datetime.datetime(year=2023, month=1, day=1, tzinfo=datetime.timezone.utc)
        extension_id = uuid4()

        run_document1 = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        run_document2 = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        child1 = (
            ExtensionResultBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        child2 = (
            ExtensionResultBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(timestamp)
            .with_extension_id(extension_id)
            .build()
        )
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        extension_output_repo = dependency_bootstrapper.get(interface=ExtensionResultRepository)
        use_case = dependency_bootstrapper.get(interface=DeleteExtensionRunUseCase)

        runs = await extension_run_repo.insert(extension_runs=[run_document1, run_document2])
        children = await extension_output_repo.insert(extension_results=[child1, child2], parent_id=run_document1.id)
        inserted_ids = [run.id for run in runs] + [child.id for child in children]

        deleted_ids = await use_case.execute_async(run_ids=[run_document1.id, run_document2.id])
        for deleted_id in deleted_ids:
            assert deleted_id in inserted_ids
