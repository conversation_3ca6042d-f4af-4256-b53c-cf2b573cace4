from typing import Sequence
from uuid import UUID

from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import ExtensionRun


class UpdateExtensionRunStatusUseCase:
    def __init__(self, extension_run_repo: ExtensionRunRepository):
        self._extension_run_repo = extension_run_repo

    async def execute_async(self, run_ids: Sequence[UUID], status: ExtensionStatus) -> Sequence[ExtensionRun]:
        """
        Updates the run status of provided run_ids to the provided status

        Returns: Updated runs
        """
        runs = await self._extension_run_repo.search_by_id(ids=run_ids)
        for run in runs:
            run.extension_status = status

        updated_runs = await self._extension_run_repo.update(extension_runs=runs)
        return updated_runs
