import logging
from enum import StrEnum
from typing import Sequence

import numpy as np
from pydantic import Field
from sklearn.linear_model import LinearRegression

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.schemas.shared import BaseDataModel


class TrendDetectionUseCaseInputBoundary(BaseDataModel):
    data_series: Sequence[float] = Field(min_length=2)
    relative_slope_threshold: float = Field(
        default=0.01, description="Min absolute relative slope for a trend (abs(slope / intercept))."
    )
    r2_threshold: float = Field(default=0.3, description="Min R-squared for significant trend (model fit).")


class TrendResult(StrEnum):
    UPWARD_TREND = "UPWARD_TREND"
    DOWNWARD_TREND = "DOWNWARD_TREND"
    NO_TREND = "NO_TREND"


class TrendDetectionUseCaseOutputBoundary(BaseDataModel):
    trend_result: TrendResult = TrendResult.NO_TREND
    coeficient: Rounded6Float | None = None
    intercept: Rounded6Float | None = None
    relative_slope: Rounded6Float | None = None


class TrendDetectionUseCase:
    async def execute_async(
        self, input_boundary: TrendDetectionUseCaseInputBoundary
    ) -> TrendDetectionUseCaseOutputBoundary:
        values = np.array(input_boundary.data_series)
        mean_value = float(np.mean(values))

        if np.all(values == values[0]):
            return TrendDetectionUseCaseOutputBoundary(trend_result=TrendResult.NO_TREND)

        # Create the independent variable (index)
        n = len(input_boundary.data_series)
        indices = np.arange(n).reshape(-1, 1)

        # Normalize the independent variable (indices) to [0, 1]
        min_index = 0
        max_index = n - 1
        scaled_indices = (indices - min_index) / (max_index - min_index)

        # Perform linear regression on original values vs scaled indices
        model = LinearRegression()
        model.fit(scaled_indices, values)

        if not model.coef_:
            logging.warning(
                "Linear regression did not produce coefficients.", extra={"data_series": input_boundary.data_series}
            )
            return TrendDetectionUseCaseOutputBoundary(trend_result=TrendResult.NO_TREND)

        coeficient = model.coef_[0]
        intercept = model.intercept_

        # Calculate R-squared value
        r_squared = model.score(scaled_indices, values)

        # 1. Check if the linear fit is good enough
        if r_squared < input_boundary.r2_threshold:
            return TrendDetectionUseCaseOutputBoundary(trend_result=TrendResult.NO_TREND)

        if coeficient is None:
            logging.warning(
                "Coefficient became None unexpectedly after regression.",
                extra={"data_series": input_boundary.data_series},
            )
            return TrendDetectionUseCaseOutputBoundary(trend_result=TrendResult.NO_TREND)

        # 2. Calculate relative slope (handle near-zero mean)
        relative_slope = None

        if np.isclose(mean_value, 0):
            if coeficient > input_boundary.relative_slope_threshold:
                trend_result = TrendResult.UPWARD_TREND
            elif coeficient < -input_boundary.relative_slope_threshold:
                trend_result = TrendResult.DOWNWARD_TREND
            else:
                trend_result = TrendResult.NO_TREND
        else:
            relative_slope = coeficient / mean_value

            # 3. Check if the relative slope is significant enough
            if relative_slope > input_boundary.relative_slope_threshold:
                trend_result = TrendResult.UPWARD_TREND
            elif relative_slope < -input_boundary.relative_slope_threshold:
                trend_result = TrendResult.DOWNWARD_TREND
            else:  # Fit is good (R^2 high), but the relative change is small
                trend_result = TrendResult.NO_TREND

        return TrendDetectionUseCaseOutputBoundary(
            trend_result=trend_result, coeficient=coeficient, intercept=intercept, relative_slope=relative_slope or None
        )
