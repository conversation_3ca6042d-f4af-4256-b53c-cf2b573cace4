from typing import Sequence

from services.base.domain.repository.place_repository import PlaceRepository
from services.base.domain.schemas.place import Place
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.data_service.application.use_cases.place.models.search_place_input_boundary import (
    SearchPlaceInputBoundary,
)


class SearchPlaceUseCase:
    def __init__(self, place_repository: PlaceRepository):
        self._place_repository = place_repository

    async def execute_async(self, input_boundary: SearchPlaceInputBoundary) -> Sequence[Place]:
        query = CommonQueryAdjustments.add_user_uuid_to_query(
            user_uuid=input_boundary.owner_id, query=input_boundary.query.to_query()
        )
        response = await self._place_repository.search_by_query(query=query, size=10_000)
        return response.documents
