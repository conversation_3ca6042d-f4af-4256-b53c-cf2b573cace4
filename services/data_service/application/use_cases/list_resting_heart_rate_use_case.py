from typing import List, Optional
from uuid import UUID

from pydantic import Field, field_validator

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.output_models import EventOutputModel
from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateFields
from services.base.domain.schemas.shared import BaseDataModel, TimeIntervalModel, TimestampModel
from services.data_service.application.use_cases.helpers.do_list_query_helper import DoListQueryHelper


class ListRestingHeartRateOutputDetail(TimestampModel):
    value: float
    confidence: Optional[float] = None


class ListRestingHeartRateOutputItem(TimeIntervalModel, EventOutputModel):
    bpm_avg: float | None = Field(alias=RestingHeartRateFields.BPM_AVG, default=None)
    bpm_max: float | None = Field(alias=RestingHeartRateFields.BPM_MAX, default=None)
    bpm_min: float | None = Field(alias=RestingHeartRateFields.BPM_MIN, default=None)
    rhr_detail: List[ListRestingHeartRateOutputDetail] = Field(alias=RestingHeartRateFields.RHR_DETAIL)

    @field_validator("bpm_avg", "bpm_max", "bpm_min")
    @classmethod
    def round_bpm(cls, value: float):
        return round(value, 2) if value else value


class ListRestingHeartRateOutputBoundary(BaseDataModel):
    results: List[ListRestingHeartRateOutputItem]
    re_fetch_time_input: Optional[TimeRangeInput] = None


class ListRestingHeartRateUseCase:
    def __init__(self, search_service: DocumentSearchService, do_list_query: DoListQueryHelper):
        self._search_service = search_service
        self._do_list_query = do_list_query

    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeRangeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> ListRestingHeartRateOutputBoundary:
        and_query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    metadata.to_and_query(),
                    CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid),
                ]
            )
            .build_and_query()
        )

        results, used_time_input = await self._do_list_query.execute_async(
            query=and_query,
            time_input=time_input,
            re_fetch=re_fetch,
            data_type=DataType.RestingHeartRate,
            return_type=ListRestingHeartRateOutputItem,
        )
        return ListRestingHeartRateOutputBoundary(
            results=results, re_fetch_time_input=used_time_input if used_time_input != time_input else None
        )
