from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.data_service.application.use_cases.plans.models.search_plans_input_boundary import (
    SearchPlansInputBoundary,
)


class SearchPlansUseCase:

    def __init__(self, plan_repo: PlanRepository):
        self._plan_repo = plan_repo

    async def execute_async(self, input_boundary: SearchPlansInputBoundary) -> Sequence[Plan]:
        query = self._create_single_query(owner_id=input_boundary.owner_id, single_document_query=input_boundary.query)

        # TODO continuation token is not exposed right now
        search_result = await self._plan_repo.search_by_query(
            query=query,
            size=input_boundary.limit,
            continuation_token=input_boundary.continuation_token.token if input_boundary.continuation_token else None,
            sorts=input_boundary.sorts,
        )

        return search_result.documents

    def _create_single_query(
        self, owner_id: UUID, single_document_query: Optional[SingleDocumentTypeQuery[Plan]]
    ) -> SingleDocumentTypeQuery[Plan]:
        bool_query_builder = BooleanQueryBuilder()

        bool_query_builder.add_query(
            ValuesQuery(field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}", values=[str(owner_id)])
        )

        if single_document_query:
            bool_query_builder.add_query(single_document_query.query)

        return SingleDocumentTypeQuery[Plan](query=bool_query_builder.build_and_query(), domain_type=Plan)
