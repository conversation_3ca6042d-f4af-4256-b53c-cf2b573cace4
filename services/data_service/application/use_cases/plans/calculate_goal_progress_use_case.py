import json
import logging
from typing import Sequence

from services.base.application.database.aggregation_service import AggregationService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.goal import Goal, GoalFields
from services.base.domain.schemas.query.aggregations import AggregationMethod, FieldAggregation
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery
from services.base.domain.schemas.query.query import Query
from services.data_service.api.serializers.query_marshaller import QueryMarshaller


class CalculateGoalProgressUseCase:
    def __init__(self, plan_repo: PlanRepository, agg_service: AggregationService):
        self._plan_repo = plan_repo
        self._agg_service = agg_service

    async def execute(self, goals: Sequence[Goal]) -> Sequence[Goal]:
        goals_to_update = []
        modified_goals_map = {}

        for goal in goals:
            lte = goal.next_scheduled_at
            gte = goal.last_scheduled_at

            q = QueryMarshaller.deserialize_type_query(json.loads(goal.condition.query))
            q.query = (
                BooleanQueryBuilder()
                .add_queries(
                    queries=[
                        q.query,
                        ExistsQuery(field_name=goal.condition.field_name),
                        RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=gte, lte=lte),
                    ]
                )
                .build_and_query()
            )

            aggregated_results = await self._agg_service.aggregate_by_query(
                query=Query(type_queries=[q]),
                aggregations=[
                    FieldAggregation(
                        field_name=goal.condition.field_name,
                        aggregation_method=AggregationMethod(goal.condition.agg_method),
                    )
                ],
            )
            aggregated = aggregated_results[0]

            if aggregated.value is None:
                logging.error(f"failed to aggregate data for goal {goal.id}")
                aggregated.value = 0

            if aggregated.value != goal.current_value:
                updated_goal = Goal.map(model=goal, fields={GoalFields.CURRENT_VALUE: aggregated.value})
                goals_to_update.append(updated_goal)
                modified_goals_map[goal.id] = updated_goal

        if goals_to_update:
            persisted_goals = await self._plan_repo.update(plans=goals_to_update)
            persisted_map = {p_goal.id: p_goal for p_goal in persisted_goals}
            modified_goals_map.update(persisted_map)

        return [modified_goals_map.get(goal.id, goal) for goal in goals]
