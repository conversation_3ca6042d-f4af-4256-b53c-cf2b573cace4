import pytest
from dateutil.rrule import DAILY

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.plan.goal import Goal
from services.base.domain.schemas.plan.plan_base import Streak
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.tests.domain.builders.goal_builder import GoalBuilder, GoalConditionBuilder
from services.data_service.application.use_cases.plans.complete_goal_use_case import CompleteGoalUseCase


class TestCalculateGoalProgressUseCase:
    def test_complete_goal_without_recurrence_passes(self):
        # Arrange
        condition = (
            GoalConditionBuilder()
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .build()
        )
        goal = (
            GoalBuilder()
            .with_archived_at(False)
            .with_condition(condition)
            .with_current_completed(0)
            .with_max_completed(1)
            .build()
        )
        goal.recurrence = None
        # Act
        completed_goal = CompleteGoalUseCase.complete_goal(goal)

        # Assert
        expected_completed = 1 if goal.is_achieved else 0
        assert completed_goal.current_completed == expected_completed
        assert completed_goal.current_value == 0
        if goal.is_achieved:
            assert completed_goal.archived_at
        assert completed_goal.next_scheduled_at == goal.next_scheduled_at

    def test_complete_goal_with_recurrence_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = CustomRRule(dtstart=started_at, freq=DAILY)
        next_scheduled = recurrence.after(dt=started_at)

        gte = PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100)
        lte = PrimitiveTypesGenerator.generate_random_float(min_value=gte, max_value=200)

        condition = (
            GoalConditionBuilder()
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .with_gte(gte)
            .with_lte(lte)
            .build()
        )

        goal = (
            GoalBuilder()
            .with_archived_at(False)
            .with_condition(condition)
            .with_recurrence(recurrence)
            .with_next_scheduled_at(next_scheduled)
            .with_current_completed(3)
            .with_max_completed(5)
            .with_current_value(PrimitiveTypesGenerator.generate_random_float(min_value=gte, max_value=lte))
            .build()
        )

        # Act
        completed_goal = CompleteGoalUseCase.complete_goal(goal)

        # Assert
        assert completed_goal.current_completed == 4
        assert completed_goal.current_value == 0
        assert completed_goal.archived_at is None
        assert completed_goal.next_scheduled_at == recurrence.after(dt=next_scheduled)

    def test_complete_goal_streak_on_time_when_achieved(self):
        # Arrange
        condition = (
            GoalConditionBuilder()
            .with_gte(1)
            .with_lte(3)
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .build()
        )
        streak = Streak(streak=3, longest_streak=5)
        goal = (
            GoalBuilder()
            .with_archived_at(False)
            .with_condition(condition)
            .with_streak(streak)
            .with_current_value(2)
            .build()
        )

        # Act
        completed_goal = CompleteGoalUseCase.complete_goal(goal)

        # Assert
        assert completed_goal.streak.streak == 4
        assert completed_goal.streak.longest_streak == 5

    def test_complete_goal_streak_too_late_when_not_achieved(self):
        # Arrange
        condition = (
            GoalConditionBuilder()
            .with_gte(1)
            .with_lte(1)
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .build()
        )
        streak = Streak(streak=3, longest_streak=5)
        goal = (
            GoalBuilder()
            .with_archived_at(False)
            .with_condition(condition)
            .with_streak(streak)
            .with_current_value(2)
            .build()
        )

        # Act
        completed_goal = CompleteGoalUseCase.complete_goal(goal)

        # Assert
        assert completed_goal.streak.streak == 1
        assert completed_goal.streak.longest_streak == 5

    def test_complete_goal_preserves_all_properties(self):
        # Arrange
        condition = (
            GoalConditionBuilder()
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .build()
        )
        goal = (
            GoalBuilder()
            .with_archived_at(False)
            .with_condition(condition)
            .with_name("Test Goal")
            .with_note("Test note")
            .with_tags(["tag1", "tag2"])
            .build()
        )

        # Act
        completed_goal = CompleteGoalUseCase.complete_goal(goal)

        # Assert
        assert completed_goal.type == Goal.type_id()
        assert completed_goal.id == goal.id
        assert completed_goal.rbac == goal.rbac
        assert completed_goal.metadata == goal.metadata
        assert completed_goal.name == goal.name
        assert completed_goal.note == goal.note
        assert completed_goal.condition == goal.condition
        assert completed_goal.tags == goal.tags
        assert completed_goal.max_completed == goal.max_completed

    def test_complete_goal_with_existing_archived_raises(self):
        # Arrange
        archived_time = PrimitiveTypesGenerator.generate_random_aware_datetime()
        condition = (
            GoalConditionBuilder()
            .with_field_name("test")
            .with_query(TypeQuery(domain_types=[Goal], query=None))
            .build()
        )
        goal = GoalBuilder().with_condition(condition).with_archived_at(archived_time).build()

        # Act & assert
        with pytest.raises(ShouldNotReachHereException):
            _ = CompleteGoalUseCase.complete_goal(goal)
