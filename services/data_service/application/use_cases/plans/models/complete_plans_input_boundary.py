from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from pydantic import AwareDatetime, Field, field_validator

from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.event_template import TypedTemplatePayloads


class CompletePlanInput(BaseDataModel):
    id: UUID
    completed_at: AwareDatetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    payload: TypedTemplatePayloads | None = Field(default=None)


class CompletePlansInputBoundary(BaseDataModel):
    documents: Sequence[CompletePlanInput] = Field(min_length=1)
    owner_id: UUID

    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[CompletePlanInput]) -> Sequence[CompletePlanInput]:
        ids = {d.id for d in documents}
        if len(ids) != len(documents):
            raise ValueError("duplicate plans found")
        return documents
