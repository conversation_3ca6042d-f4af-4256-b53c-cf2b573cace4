from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.plan import Plan, PlanFields


class ArchivePlansUseCase:
    def __init__(self, plan_repo: PlanRepository):
        self._plan_repo = plan_repo

    async def execute_async(self, owner_id: UUID, plan_ids: Sequence[UUID]) -> Sequence[Plan]:
        existing_ucs = await self._fetch_plans_from_db(plan_ids=plan_ids, owner_id=owner_id)
        archive_timestamp = datetime.now(timezone.utc)

        return await self._plan_repo.update(
            plans=[Plan.map(model=uc, fields={PlanFields.ARCHIVED_AT: archive_timestamp}) for uc in existing_ucs]
        )

    async def _fetch_plans_from_db(self, plan_ids: Sequence[UUID], owner_id: UUID) -> Sequence[Plan]:
        existing_plans: Sequence[Plan] = await self._plan_repo.search_by_id(ids=plan_ids)
        if len(plan_ids) != len(existing_plans):
            e_plan_ids = [ep.id for ep in existing_plans]
            not_found_ids = [str(id) for id in plan_ids if id not in e_plan_ids]
            raise IncorrectOperationException(message=f"Plans {not_found_ids} were not found")

        for plan in existing_plans:
            if plan.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")
            if plan.archived_at:
                raise IncorrectOperationException(message="updating archived plans")
        return existing_plans
