from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import IncorrectOperationException
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template
from services.data_service.application.use_cases.events.event_validators import DuplicatesValidator
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateEventTemplateInputBoundaryItem,
    UpdateGroupTemplateInputBoundaryItem,
    UpdateTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator


class UpdateTemplatesUseCase:
    def __init__(
        self,
        template_repo: TemplateRepository,
        duplicate_validator: DuplicatesValidator,
        template_validator: TemplateValidator,
    ):
        self._template_repo = template_repo
        self._duplicate_validator = duplicate_validator
        self._template_validator = template_validator

    async def execute_async(self, owner_id: UUID, input_boundary: UpdateTemplateInputBoundary) -> Sequence[Template]:
        input_event_templates = [
            t for t in input_boundary.documents if isinstance(t, UpdateEventTemplateInputBoundaryItem)
        ]
        input_group_templates = [
            t for t in input_boundary.documents if isinstance(t, UpdateGroupTemplateInputBoundaryItem)
        ]
        to_update_event_templates = []
        if input_event_templates:
            to_update_event_templates = await self._collect_update_event_template_inputs(
                input_templates=input_event_templates, owner_id=owner_id
            )
        to_update_group_templates = []
        if input_group_templates:
            to_update_group_templates = await self._collect_update_group_template_inputs(
                input_templates=input_group_templates, owner_id=owner_id
            )

        to_update: Sequence[Template] = list(to_update_event_templates) + list(to_update_group_templates)
        return await self._template_repo.update(templates=to_update)

    async def _collect_update_event_template_inputs(
        self, input_templates: Sequence[UpdateEventTemplateInputBoundaryItem], owner_id: UUID
    ) -> Sequence[EventTemplate]:
        to_update: list[EventTemplate] = []
        to_duplicate_check: list[EventTemplate] = []

        existing_event_templates = await self._template_validator.fetch_and_validate_templates(
            template_ids=[t.id for t in input_templates], owner_id=owner_id
        )

        for input_template, existing_template in zip(
            sorted(input_templates, key=lambda p: p.id), sorted(existing_event_templates, key=lambda p: p.id)
        ):
            if not isinstance(existing_template, EventTemplate):
                raise IncorrectOperationException(message=f"Template {existing_template.id} is not an EventTemplate")
            if input_template.document.type != existing_template.document_type.value:
                raise IncorrectOperationException(
                    message=f"Trying to change document type for document_id:{input_template.id}"
                )
            updated_template = EventTemplate(
                type=DataType.EventTemplate,
                id=existing_template.id,
                system_properties=existing_template.system_properties,
                rbac=existing_template.rbac,
                document_name=input_template.document.name,
                document=input_template.document,
                document_type=EventType(input_template.document.type.value),
                name=input_template.name,
                tags=input_template.tags,
                archived_at=existing_template.archived_at,
            )
            to_update.append(updated_template)
            if (
                input_template.name != existing_template.name
                or input_template.document.name != existing_template.document.name
            ):
                to_duplicate_check.append(updated_template)

        await self._duplicate_validator.validate(documents=to_duplicate_check)
        return to_update

    async def _collect_update_group_template_inputs(
        self, input_templates: Sequence[UpdateGroupTemplateInputBoundaryItem], owner_id: UUID
    ) -> Sequence[GroupTemplate]:
        to_update: list[GroupTemplate] = []
        to_duplicate_check: list[GroupTemplate] = []

        existing_group_templates = await self._template_validator.fetch_and_validate_templates(
            template_ids=[t.id for t in input_templates], owner_id=owner_id
        )

        for input_template, existing_template in zip(
            sorted(input_templates, key=lambda p: p.id), sorted(existing_group_templates, key=lambda p: p.id)
        ):
            if not isinstance(existing_template, GroupTemplate):
                raise IncorrectOperationException(message=f"Template {existing_template.id} is not an GroupTemplate")
            await self._template_validator.fetch_and_validate_group_template_ids(
                template_ids=input_template.template_ids, owner_id=owner_id
            )
            updated_template = GroupTemplate(
                type=DataType.GroupTemplate,
                id=existing_template.id,
                system_properties=existing_template.system_properties,
                rbac=existing_template.rbac,
                template_ids=input_template.template_ids,
                name=input_template.name,
                tags=input_template.tags,
                archived_at=existing_template.archived_at,
            )
            to_update.append(updated_template)
            if input_template.name != existing_template.name:
                to_duplicate_check.append(updated_template)

        await self._duplicate_validator.validate(documents=to_duplicate_check)
        return to_update
