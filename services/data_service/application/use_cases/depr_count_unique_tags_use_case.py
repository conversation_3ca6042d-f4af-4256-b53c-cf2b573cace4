import asyncio
from collections import defaultdict
from typing import List, Sequence
from uuid import UUID

from pydantic import Field

from services.base.application.boundaries.aggregates import FrequencyDistributionAggregate
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.enums.taggable_data_type import TaggableDataType


class DeprCountUniqueTagsUseCaseOutputItem(BaseDataModel):
    tag_name: str = Field(alias="tag_name", title="Tag name", description="Tag name")
    count: int = Field(alias="count", title="Count", description="Quantity of tags with this name")


class DeprCountUniqueTagsUseCaseOutputBoundary(BaseDataModel):
    tags: List[DeprCountUniqueTagsUseCaseOutputItem] = Field(alias="tags", default=[])


class DeprCountUniqueTagsUseCase:
    def __init__(self, agg_service: AggregationService):
        self._agg_service = agg_service

    async def execute_async(
        self,
        user_uuid: UUID,
        data_types: Sequence[TaggableDataType],
    ) -> DeprCountUniqueTagsUseCaseOutputBoundary:
        q = CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid)
        field_name = f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword"

        tag_dict = defaultdict(int)
        async with asyncio.TaskGroup() as group:
            tasks = [
                group.create_task(
                    self._do_count(
                        field_name=field_name,
                        query=SingleDocumentTypeQuery(domain_type=dt.to_domain_model(), query=q).to_query(),
                    )
                )
                for dt in data_types
            ]

        results = [task.result() for task in tasks]
        for result in results:
            for output in result:
                tag_dict[output.aggregation_key] += output.document_count

        if not tag_dict:
            raise NoContentException("no documents with tags found for given data types")

        tag_count_out = DeprCountUniqueTagsUseCaseOutputBoundary()
        for name, count in tag_dict.items():
            tag_count_out.tags.append(DeprCountUniqueTagsUseCaseOutputItem(tag_name=name, count=count))

        # Sort tag counts from highest to lowest
        tag_count_out.tags.sort(key=lambda t: t.count, reverse=True)

        return tag_count_out

    async def _do_count(self, field_name: str, query: Query) -> Sequence[FrequencyDistributionAggregate]:
        return await self._agg_service.frequency_distribution_by_query(
            size=10_000,
            field_name=field_name,
            query=query,
        )
