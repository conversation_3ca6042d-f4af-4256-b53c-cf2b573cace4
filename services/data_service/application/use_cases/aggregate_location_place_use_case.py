import logging
from typing import List, Optional
from uuid import UUID

from pydantic import Field, ValidationError

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.location import Location, LocationFields, PlaceVisitDetailsFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimestampModel
from services.base.domain.schemas.user import User
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import are_results_empty
from services.data_service.application.utils.find_latest_entries import find_latest_entries
from services.data_service.application.utils.metadata_filters import prepare_metadata_query


class AggregateLocationPlaceUseCaseOutputDetail(BaseDataModel):
    name: str
    address: Optional[str] = Field(default=None)
    duration: Optional[float] = Field(ge=0, default=None, le=SECONDS_IN_365_DAYS)
    count: Optional[int] = Field(ge=0, default=None)
    coordinates: Optional[CoordinatesModel] = None


class AggregateLocationPlaceUseCaseOutputItem(TimestampModel):
    places: List[AggregateLocationPlaceUseCaseOutputDetail]


class AggregateLocationPlaceUseCaseOutputBoundary(BaseDataModel):
    results: List[AggregateLocationPlaceUseCaseOutputItem]
    re_fetch_time_input: Optional[TimeInput] = None


class AggregateLocationPlaceUseCase:
    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> AggregateLocationPlaceUseCaseOutputBoundary:
        custom_sub_aggs = {
            "place": {
                "terms": {
                    "field": f"{LocationFields.PLACE_VISIT_DETAILS}.{PlaceVisitDetailsFields.NAME}",
                },
                "aggs": {
                    DocumentLabels.DURATION: {"sum": {"field": DocumentLabels.DURATION}},
                    "hits": {
                        "top_hits": {
                            "size": 100,
                            "_source": [LocationFields.START_COORDINATES, LocationFields.PLACE_VISIT_DETAILS],
                        },
                    },
                },
            }
        }

        and_query = (
            BooleanQueryBuilder()
            .add_queries(queries=prepare_metadata_query(metadata_input=metadata))
            .add_query(query=CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Location], query=and_query)])

        results = await get_time_aggregated_fields_async(
            query=query,
            custom_sub_aggs=custom_sub_aggs,
            time_gte=time_input.time_gte,
            time_lte=time_input.time_lte,
            interval=time_input.interval,
        )

        if are_results_empty(results):
            if not re_fetch:
                raise NoContentException("No data available for the given time range.")
            new_time_input = TimeInput.map(
                model=find_latest_entries(
                    data_type=DataType.Location,
                    current_user=User(user_uuid=user_uuid),
                    old_parameters=time_input,
                ),
                fields={"interval": time_input.interval},
            )
            output = await self.execute_async(user_uuid=user_uuid, time_input=new_time_input, metadata=metadata)
            return AggregateLocationPlaceUseCaseOutputBoundary(
                results=output.results, re_fetch_time_input=new_time_input
            )

        buckets = results["aggregations"]["requested_histogram"]["buckets"]
        output_results = []
        for bucket in buckets:
            try:
                places = []
                for place in bucket["place"]["buckets"]:
                    places.append(
                        AggregateLocationPlaceUseCaseOutputDetail(
                            name=place["key"],
                            duration=place[DocumentLabels.DURATION]["value"],
                            count=place["doc_count"],
                            coordinates=place["hits"]["hits"]["hits"][0]["_source"][LocationFields.START_COORDINATES],
                            address=place["hits"]["hits"]["hits"][0]["_source"][LocationFields.PLACE_VISIT_DETAILS][
                                PlaceVisitDetailsFields.ADDRESS
                            ],
                        )
                    )
                output_results.append(
                    AggregateLocationPlaceUseCaseOutputItem(timestamp=bucket["key_as_string"], places=places)
                )

            except (KeyError, ValueError, ValidationError):
                logging.exception(f"Can't process location entry {bucket}")
        return AggregateLocationPlaceUseCaseOutputBoundary(results=output_results, re_fetch_time_input=time_input)
