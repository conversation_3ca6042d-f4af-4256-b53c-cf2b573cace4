from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event_group import EventGroup
from services.data_service.application.use_cases.events.models.reorder_event_group_input_boundary import (
    ReorderEventGroupInputBoundary,
)


class ReorderEventGroupUseCase:
    def __init__(self, event_repo: EventRepository):
        self._event_repo = event_repo

    async def execute_async(self, boundary: ReorderEventGroupInputBoundary, owner_id: UUID) -> EventGroup:
        groups = await self._event_repo.search_by_id(ids=[boundary.id])
        if not groups:
            raise IncorrectOperationException(message=f"Event group {boundary.id} not found")

        group = groups[0]
        if not isinstance(group, EventGroup):
            raise IncorrectOperationException(
                message=f"Document {boundary.id} is not an event group, type: {group.type_id()}"
            )

        if group.rbac.owner_id != owner_id:
            raise InvalidPrivilegesException(message="You don't have permission to reorder this event group")

        # Validate that all provided child IDs exist in the group
        current_child_ids = set(group.child_ids)
        requested_child_ids = set(boundary.child_ids)

        if current_child_ids != requested_child_ids:
            raise IncorrectOperationException(
                message=f"Child IDs mismatch. Existing child IDs: {current_child_ids}, requested child IDs: {requested_child_ids}"
            )

        group.child_ids = list(boundary.child_ids)

        updated_groups = await self._event_repo.update(events=[group])
        updated_group = updated_groups[0]
        assert isinstance(updated_group, EventGroup)
        return updated_group
