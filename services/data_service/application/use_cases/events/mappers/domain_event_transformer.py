from typing import Sequence
from uuid import UUID, uuid4

from services.base.application.exceptions import IncorrectOperationException
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import AssetReference, EventMetadata, RBACSchema
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.models.insert_event_input import EventInsertionContext
from services.data_service.application.use_cases.events.models.shared import EventInputAsset
from services.data_service.type_resolver import TypeResolver


class DomainEventTransformer:
    def __init__(self, assets_service: AssetService):
        self._assets_service = assets_service

    async def transform(
        self,
        event_and_submission_id: tuple[InsertEventInputs, UUID],
        owner_id: UUID,
        metadata: EventMetadata,
        group_id: UUID | None = None,
    ) -> Sequence[Event]:
        insertable_event = event_and_submission_id[0]
        submission_id = event_and_submission_id[1]

        # store assets
        # TODO: handle failing to store assets
        asset_references = await self.store_assets(input_assets=insertable_event.assets, owner_id=owner_id)
        ## We need to check here whether the insert event is NOT a group because there is no other way how to do it
        ## without introducing new type union
        if not isinstance(insertable_event, TypeResolver.INSERT_GROUP_INPUTS_UNION):
            insertion_ctx = EventInsertionContext(
                owner_id=owner_id,
                submission_id=submission_id,
                asset_references=asset_references or [],
                metadata=metadata,
                group_id=group_id,
            )
            event = insertable_event.to_domain(insertion_ctx)
            return [event]

        elif isinstance(insertable_event, TypeResolver.INSERT_GROUP_INPUTS_UNION):
            events = []
            # will be replaced with Group when we implement it properly on domain layer
            event_group = EventGroup(
                type=DataType.EventGroup,
                rbac=RBACSchema(owner_id=owner_id),
                group_id=group_id,
                submission_id=submission_id,
                template_id=insertable_event.template_id,
                id=uuid4(),
                plan_extension=insertable_event.plan_extension,
                timestamp=insertable_event.timestamp,
                end_time=insertable_event.end_time,
                metadata=metadata,
                tags=insertable_event.tags,
                asset_references=asset_references or [],
                name=insertable_event.name,
                note=insertable_event.note,
                child_ids=[],
                category=insertable_event.category,
            )

            for input_event in insertable_event.events:
                events.extend(
                    await self.transform(
                        event_and_submission_id=(input_event, submission_id),
                        owner_id=owner_id,
                        metadata=metadata,
                        group_id=event_group.id,
                    )
                )
            event_group.child_ids = [e.id for e in events]
            return [event_group, *events]
        else:
            raise IncorrectOperationException(message=f"unexpected event type provided: {type(insertable_event)}")

    async def store_assets(self, input_assets: Sequence[EventInputAsset], owner_id: UUID) -> Sequence[AssetReference]:
        if not input_assets:
            return []
        return await self._assets_service.store_input_assets(assets=input_assets, owner_id=owner_id)
