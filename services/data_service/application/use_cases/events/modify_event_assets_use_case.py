from itertools import chain
from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundary,
    ModifyEventAssetsInputBoundaryItem,
)


class ModifyEventAssetsUseCase:
    def __init__(
        self,
        event_repo: EventRepository,
        assets_service: AssetService,
    ):
        self._assets_service = assets_service
        self._event_repo = event_repo

    async def execute_async(self, boundary: ModifyEventAssetsInputBoundary, owner_id: UUID) -> Sequence[Event]:
        input_items = sorted(boundary.items, key=lambda e: e.id)
        existing_events = sorted(
            await self._fetch_events_from_db(input_event_ids=[i.id for i in input_items], owner_id=owner_id),
            key=lambda e: e.id,
        )

        await self._validate_assets_to_remove_exist(
            items=input_items,
            existing_events=existing_events,
        )

        existing_event: Event
        input_item: ModifyEventAssetsInputBoundaryItem
        updated_events: list[Event] = []
        for existing_event, input_item in zip(existing_events, input_items):
            new_asset_references = await self._get_asset_references(
                existing_event=existing_event, input_item=input_item, owner_id=owner_id
            )

            existing_event.asset_references = new_asset_references or []
            type(existing_event).model_validate(existing_event)
            updated_events.append(existing_event)
        return await self._event_repo.update(events=updated_events)

    async def _fetch_events_from_db(self, input_event_ids: Sequence[UUID], owner_id: UUID) -> Sequence[Event]:
        existing_events: Sequence[Event] = await self._event_repo.search_by_id(ids=[id for id in input_event_ids])

        if len(input_event_ids) != len(existing_events):
            ee = [e.id for e in existing_events]
            not_found_ids = [str(id) for id in input_event_ids if id not in ee]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for event in existing_events:
            if event.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")

        return existing_events

    async def _get_asset_references(
        self, existing_event: Event, input_item: ModifyEventAssetsInputBoundaryItem, owner_id: UUID
    ):
        new_assets_references = []
        if existing_event.asset_references:
            new_assets_references.extend(existing_event.asset_references)

        if input_item.asset_ids_to_remove:
            if not new_assets_references:
                raise IncorrectOperationException(message=f"event {existing_event.id} has no assets to remove")
            await self._assets_service.delete_assets(asset_ids=input_item.asset_ids_to_remove, owner_id=owner_id)

            new_assets_references = [
                a for a in new_assets_references if a.asset_id not in input_item.asset_ids_to_remove
            ]

        if input_item.assets_to_add:
            added_asset_references = await self._assets_service.store_input_assets(
                assets=input_item.assets_to_add, owner_id=owner_id
            )
            new_assets_references += added_asset_references

        return new_assets_references

    @staticmethod
    async def _validate_assets_to_remove_exist(
        items: Sequence[ModifyEventAssetsInputBoundaryItem], existing_events: Sequence[Event]
    ):
        asset_ids_to_remove = list(chain.from_iterable([i.asset_ids_to_remove for i in items if i.asset_ids_to_remove]))
        existing_events_asset_ids = list(
            chain.from_iterable(
                [[a.asset_id for a in e.asset_references] for e in existing_events if e.asset_references]
            )
        )
        not_found_ids = [asset_id for asset_id in asset_ids_to_remove if asset_id not in existing_events_asset_ids]

        if not_found_ids:
            raise IncorrectOperationException(f"asset references {not_found_ids} not found in events to delete")
