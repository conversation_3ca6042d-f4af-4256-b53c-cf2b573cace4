from typing import Sequence

from pydantic import field_validator
from pydantic.fields import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.events.updatable_event_inputs import UpdatableEventInputs


class UpdateEventInputBoundary(BaseDataModel):
    documents: Sequence[UpdatableEventInputs] = Field(min_length=1)

    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[UpdatableEventInputs]) -> Sequence[UpdatableEventInputs]:
        ids = {d.id for d in documents}
        if len(ids) != len(documents):
            raise ValueError("multiple update inputs for single document provided")
        content_hashes = {d.content_hash for d in documents}
        if len(content_hashes) != len(documents):
            raise ValueError("request contains duplicate entries")
        return documents
