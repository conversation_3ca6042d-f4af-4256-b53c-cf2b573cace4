from typing import Literal
from uuid import UUID, uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.place_visit import (
    PlaceVisit,
    PlaceVisitFields,
    PlaceVisitIdentifier,
)
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertPlaceVisitInput(InsertEventInput, PlaceVisitIdentifier):
    type: Literal[DataType.PlaceVisit] = Field(alias=EventFields.TYPE)
    category: PlaceCategory = Field(alias=PlaceVisitFields.CATEGORY)
    place_id: UUID | None = Field(alias=PlaceVisitFields.PLACE_ID)
    rating: int | None = Field(
        alias=PlaceVisitFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> PlaceVisit:
        return PlaceVisit(
            # technical
            type=DataType.PlaceVisit,
            category=self.category,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            note=self.note,
            rating=self.rating,
            place_id=self.place_id,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
