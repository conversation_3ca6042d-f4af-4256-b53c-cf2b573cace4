from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import Cardio, CardioCategory, CardioFields, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import (
    Exercise,
    ExerciseCategory,
    ExerciseFields,
    ExerciseIdentifier,
)
from services.base.domain.schemas.events.exercise.strength import (
    Strength,
    StrengthCategory,
    StrengthFields,
    StrengthIdentifier,
)
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertExerciseInput(InsertEventInput, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(alias=ExerciseFields.CATEGORY)
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(
        alias=ExerciseFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Exercise:
        return Exercise(
            # technical
            type=DataType.Exercise,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            note=self.note,
            rating=self.rating,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


class InsertCardioInput(InsertEventInput, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=CardioFields.CATEGORY)
    name: NonEmptyStr = Field(alias=CardioFields.NAME)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=250_000, default=None)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848, default=None)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(
        alias=CardioFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Cardio:
        return Cardio(
            type=DataType.Cardio,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            distance=self.distance,
            elevation=self.elevation,
            rating=self.rating,
            note=self.note,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.distance,
            ]
        )


class InsertStrengthInput(InsertEventInput, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=StrengthFields.CATEGORY)
    name: NonEmptyStr = Field(alias=StrengthFields.NAME)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500, default=None)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(
        alias=StrengthFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Strength:
        return Strength(
            type=DataType.Strength,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            count=self.count,
            weight=self.weight,
            rating=self.rating,
            note=self.note,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.count,
            ]
        )
