from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    Medication,
    MedicationCategory,
    MedicationFields,
    MedicationIdentifier,
    MedicationValueLimits,
    SingleDoseInformation,
    VolumeUnit,
    WeightUnit,
)
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertMedicationInput(InsertEventInput, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)

    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)
    note: NonEmptyStr | None = Field(
        alias=MedicationFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> Medication:
        return Medication(
            type=self.type,
            category=self.category,
            timestamp=self.timestamp,
            name=self.name,
            consumed_amount=self.consumed_amount,
            consume_unit=self.consume_unit,
            single_dose_information=self.single_dose_information,
            tags=self.tags,
            note=self.note,
            # System
            id=uuid4(),
            rbac=RBACSchema(owner_id=ctx.owner_id),
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            plan_extension=self.plan_extension,
            group_id=ctx.group_id,
            metadata=ctx.metadata,
            asset_references=ctx.asset_references,
            end_time=self.end_time,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
            ]
        )
