from typing import Sequence
from uuid import UUID

from services.base.application.boundaries.aggregates import DateHistogramAggregate
from services.base.application.database.aggregation_service import AggregationService
from services.base.domain.schemas.query.aggregations import DateHistogramAggregation
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel


class DateHistogramUseCaseInputBoundary(BaseDataModel):
    query: Query
    aggregation: DateHistogramAggregation
    owner_id: UUID


class DateHistogramUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[DateHistogramAggregate]


class DateHistogramUseCase:
    def __init__(self, agg_service: AggregationService):
        self._agg_service = agg_service

    async def execute_async(
        self,
        input_boundary: DateHistogramUseCaseInputBoundary,
    ) -> DateHistogramUseCaseOutputBoundary:
        query = CommonQueryAdjustments.add_user_uuid_to_query(
            query=input_boundary.query, user_uuid=input_boundary.owner_id
        )

        results = await self._agg_service.date_histogram_by_query(query=query, aggregation=input_boundary.aggregation)

        return DateHistogramUseCaseOutputBoundary(results=results)
