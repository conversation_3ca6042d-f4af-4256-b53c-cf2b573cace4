import pytest

from services.base.domain.enums.event_type import EventType
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase


@pytest.mark.integration
@pytest.mark.parametrize(
    "query,expected_event_type",
    [
        pytest.param("I had coffee this morning", EventType.Drink, id="drink_coffee"),
        pytest.param("I ran 5km yesterday", EventType.Cardio, id="cardio_running"),
        pytest.param("I lifted weights at the gym", EventType.Strength, id="strength_weights"),
        pytest.param("I watched a movie on Netflix", EventType.Content, id="video_movie"),
        pytest.param("I read a book", EventType.Content, id="text_book"),
        pytest.param("I listened to a podcast", EventType.Content, id="audio_podcast"),
        pytest.param("I had a headache", EventType.Symptom, id="symptom_headache"),
        pytest.param("I took some aspirin", EventType.Medication, id="medication_aspirin"),
        pytest.param("I ate a sandwich for lunch", EventType.Food, id="food_sandwich"),
        pytest.param("I did some yoga", EventType.Exercise, id="exercise_yoga"),
        pytest.param("I was feeling happy today", EventType.Emotion, id="emotion_happy"),
        pytest.param("I was stressed about work", EventType.Stress, id="stress_work"),
        pytest.param("I took a vitamin supplement", EventType.Supplement, id="supplement_vitamin"),
    ],
)
async def test_classify_event_type(
    classify_event_type_use_case: ClassifyEventTypeUseCase, query: str, expected_event_type: EventType
):
    # Act
    result = await classify_event_type_use_case.execute(query=query)

    # Assert
    assert result.event_type.type_id() == expected_event_type
