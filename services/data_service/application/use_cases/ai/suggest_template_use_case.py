from typing import Sequence
from uuid import UUID

from pydantic import BaseModel
from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.schemas.query.leaf_query import MatchType, PatternQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.data_service.application.use_cases.ai.prompts.suggest_template_prompt import (
    TemplateOption,
    generate_suggest_template_prompt,
)
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase


class SuggestTemplateUseCaseInputBoundary(BaseModel):
    query: NonEmptyStr


class SuggestTemplateUseCase(UseCaseBase):
    def __init__(
        self,
        model: Model,
        search_templates_use_case: SearchTemplatesUseCase,
    ):
        self._model = model
        self._search_templates_use_case = search_templates_use_case

    async def execute(
        self, user_uuid: UUID, input_boundary: SuggestTemplateUseCaseInputBoundary
    ) -> EventTemplate | None:
        templates = await self._get_user_templates(query=input_boundary.query, user_uuid=user_uuid)
        if not templates:
            return None

        serialized_templates = self._serialize_templates(templates)
        selected_template_id = await self._select_best_template(
            query=input_boundary.query,
            templates=serialized_templates,
        )
        if selected_template_id and selected_template_id != "none":
            for t in templates:
                if str(t.id) == selected_template_id:
                    return t
        return None

    def _serialize_templates(self, templates: Sequence[EventTemplate]) -> list[dict]:
        return [
            TemplateOption(
                template_id=t.id,
                template_name=t.document_name,
                document=t.document.model_dump(by_alias=True),
            ).model_dump(by_alias=True)
            for t in templates
        ]

    async def _select_best_template(
        self,
        query: str,
        templates: list[dict],
    ) -> str | None:
        instructions = generate_suggest_template_prompt(templates=templates)
        agent = Agent(model=self._model, instructions=instructions)
        response = await agent.run(user_prompt=query)
        result = response.output.strip()
        return result if result != "none" else None

    async def _get_user_templates(self, query: str, user_uuid: UUID) -> Sequence[EventTemplate]:
        result = await self._search_templates_use_case.execute_async(
            input_boundary=SearchTemplatesInputBoundary(
                owner_id=user_uuid,
                query=Query(
                    type_queries=[
                        TypeQuery(
                            domain_types=[EventTemplate],
                            query=PatternQuery(
                                field_names=["document_name", "document", "tags"],
                                pattern=query,
                                match_type=MatchType.FUZZY,
                            ),
                        )
                    ]
                ),
            )
        )
        # We are explicitly ignoring group templates for now
        return [template for template in result if isinstance(template, EventTemplate)]
