from enum import StrEnum

from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.data_service.application.use_cases.ai.prompts.classify_action_prompt import classify_action_prompt


class Actions(StrEnum):
    INSERT_PLAN = "insert_plan"
    COMPLETE_PLAN = "complete_plan"
    INSERT_EVENT = "insert_event"
    UPDATE_PLAN = "update_plan"
    ARCHIVE_PLAN = "archive_plan"


class ClassifyUserActionUseCase:
    def __init__(self, model: Model):
        self._agent = Agent(model=model, instructions=classify_action_prompt)

    async def execute(self, query: str) -> str:
        response = await self._agent.run(user_prompt=query, output_type=Actions)
        return response.output
