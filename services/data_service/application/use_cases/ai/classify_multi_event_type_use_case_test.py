import pytest

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.data_service.application.use_cases.ai.classify_multi_event_type_use_case import (
    ClassifyMultiEventTypeUseCase,
)


@pytest.mark.integration
@pytest.mark.parametrize(
    "query,expected_types",
    [
        ("I did some exercise", {DataType.Exercise, DataType.Cardio, DataType.Strength}),
        ("I went for a run", {DataType.Cardio}),
        ("I lifted weights", {DataType.Strength}),
        ("I read a book", {DataType.Content}),
        ("I watched a movie", {DataType.Content}),
        ("I listened to a podcast", {DataType.Content}),
        ("I checked my blood pressure", {DataType.BodyMetric, DataType.BloodPressure}),
        ("I measured my blood glucose", {DataType.BodyMetric, DataType.BloodGlucose}),
        ("I had coffee", {DataType.Drink}),
        ("I ate a sandwich", {DataType.Food}),
        ("I'm feeling stressed", {DataType.Stress}),
        ("I'm feeling happy", {DataType.Emotion}),
        ("I did something", {DataType.Activity}),
        ("Not sure what happened", {DataType.Activity}),
        (
            "I went for a long run and did some strength training",
            {DataType.Cardio, DataType.Strength, DataType.Exercise},
        ),
        ("I watched a video podcast", {DataType.Content}),
        (
            "I checked my blood pressure and blood glucose",
            {DataType.BodyMetric, DataType.BloodPressure, DataType.BloodGlucose},
        ),
    ],
)
async def test_classify_multi_event_type(
    classify_multi_event_type_use_case: ClassifyMultiEventTypeUseCase,
    query: str,
    expected_types: set[EventType],
):
    # Act
    result = await classify_multi_event_type_use_case.execute(query=query)

    # Assert
    actual_types = {schema.event_type.type_id() for schema in result}
    assert actual_types == expected_types
