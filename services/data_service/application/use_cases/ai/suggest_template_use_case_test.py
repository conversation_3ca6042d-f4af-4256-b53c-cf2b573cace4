from typing import Any, AsyncGenerator, Awaitable, Callable, Sequence, cast

import pytest

from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.payload.exercise_payload_builders import CardioPayloadBuilder
from services.base.tests.domain.builders.template.payload.nutrition_payload_builders import (
    DrinkPayloadBuilder,
    FoodPayloadBuilder,
)
from services.base.tests.domain.builders.template.payload.symptom_payload_builder import SymptomPayloadBuilder
from services.data_service.application.use_cases.ai.suggest_template_use_case import (
    SuggestTemplateUseCase,
    SuggestTemplateUseCaseInputBoundary,
)


class TestSuggestTemplateUseCase:
    @pytest.fixture
    async def user_with_specific_templates(
        self,
        user_factory: Callable[[], Awaitable[MemberUser]],
        template_repo: TemplateRepository,
    ) -> AsyncGenerator[tuple[MemberUser, Sequence[EventTemplate]], Any]:
        """Create a user with specific well-named templates for testing"""
        user = await user_factory()

        templates = [
            # Coffee template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("morning coffee")
            .with_document(DrinkPayloadBuilder().with_name("morning coffee").build())
            .with_tags(["morning", "coffee", "routine"])
            .build(),
            # Running template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("morning run")
            .with_document(CardioPayloadBuilder().with_name("morning run").build())
            .with_tags(["morning", "exercise", "cardio"])
            .build(),
            # Specific food template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("thai curry lunch")
            .with_document(FoodPayloadBuilder().with_name("thai curry lunch").build())
            .with_tags(["lunch", "thai", "spicy"])
            .build(),
            # Headache template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("headache")
            .with_document(SymptomPayloadBuilder().with_name("headache").build())
            .with_tags(["pain", "head"])
            .build(),
            # Generic template that shouldn't match specific queries
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("generic activity")
            .with_document(CardioPayloadBuilder().with_name("generic activity").build())
            .with_tags(["generic"])
            .build(),
        ]

        inserted_templates = await template_repo.insert(templates=templates, force_strong_consistency=True)
        yield user, cast(Sequence[EventTemplate], inserted_templates)

        # Cleanup
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "query,expected_template_name",
        [
            # Exact matches should work
            ("I had my morning coffee", "morning coffee"),
            ("I went for my morning run", "morning run"),
            ("I had thai curry for lunch", "thai curry lunch"),
            ("I have a headache", "headache"),
            # Close variations should work
            ("I had coffee this morning", "morning coffee"),
            ("I ran in the morning", "morning run"),
            ("I ate thai curry today", "thai curry lunch"),
        ],
    )
    async def test_suggest_template_with_specific_queries(
        self,
        suggest_template_use_case: SuggestTemplateUseCase,
        user_with_specific_templates: tuple[MemberUser, Sequence[EventTemplate]],
        query: str,
        expected_template_name: str | None,
    ):
        # Arrange
        user, templates = user_with_specific_templates

        # Act
        result = await suggest_template_use_case.execute(
            user_uuid=user.user_uuid, input_boundary=SuggestTemplateUseCaseInputBoundary(query=query)
        )

        # Assert
        if expected_template_name is None:
            assert result is None
            return

        assert result is not None, f"Expected template match for query: '{query}', but got None"
        assert result.document_name == expected_template_name
        assert result.id in [t.id for t in templates]

    async def test_suggest_template_with_no_templates(
        self,
        suggest_template_use_case: SuggestTemplateUseCase,
        user_factory: Callable[[], Awaitable[MemberUser]],
    ):
        # Arrange
        user = await user_factory()  # User with no templates

        # Act
        result = await suggest_template_use_case.execute(
            user_uuid=user.user_uuid,
            input_boundary=SuggestTemplateUseCaseInputBoundary(query="I had coffee this morning"),
        )

        # Assert
        assert result is None, "Should return None when no templates exist for user"

    async def test_suggest_template_with_non_matching_templates(
        self,
        suggest_template_use_case: SuggestTemplateUseCase,
        user_factory: Callable[[], Awaitable[MemberUser]],
        template_repo: TemplateRepository,
    ):
        # Arrange
        user = await user_factory()

        # Create templates that clearly don't match the query
        templates = [
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("swimming workout")
            .with_document(CardioPayloadBuilder().with_name("swimming workout").build())
            .build(),
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("pizza dinner")
            .with_document(FoodPayloadBuilder().with_name("pizza dinner").build())
            .build(),
        ]

        inserted_templates = await template_repo.insert(templates=templates, force_strong_consistency=True)

        try:
            # Act
            result = await suggest_template_use_case.execute(
                user_uuid=user.user_uuid,
                input_boundary=SuggestTemplateUseCaseInputBoundary(query="I took my morning medicine"),
            )
            assert result is None
        finally:
            # Cleanup
            await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    @pytest.mark.integration
    async def test_suggest_template_prefers_more_specific_matches(
        self,
        suggest_template_use_case: SuggestTemplateUseCase,
        user_factory: Callable[[], Awaitable[MemberUser]],
        template_repo: TemplateRepository,
    ):
        # Arrange
        user = await user_factory()

        # Create both generic and specific templates
        templates = [
            # Generic template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("coffee")
            .with_document(DrinkPayloadBuilder().with_name("coffee").build())
            .build(),
            # More specific template
            EventTemplateBuilder()
            .with_owner_id(user.user_uuid)
            .with_document_name("morning coffee with oat milk")
            .with_document(DrinkPayloadBuilder().with_name("morning coffee with oat milk").build())
            .build(),
        ]

        inserted_templates = await template_repo.insert(templates=templates, force_strong_consistency=True)

        try:
            # Act - query that matches both but should prefer the more specific one
            result = await suggest_template_use_case.execute(
                user_uuid=user.user_uuid,
                input_boundary=SuggestTemplateUseCaseInputBoundary(query="I had my morning coffee with oat milk"),
            )

            # Assert
            assert result is not None, "Should return a template match"
            assert (
                result.document_name == "morning coffee with oat milk"
            ), f"Should prefer more specific template, but got: {result.document_name}"

        finally:
            # Cleanup
            await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])
