from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.records.record import Record
from services.data_service.application.use_cases.records.models.delete_record_input_boundary import (
    DeleteRecordInputBoundary,
)


class DeleteRecordByIdUseCase:
    def __init__(self, record_repository: RecordRepository):
        self._record_repository = record_repository

    async def execute_async(self, boundary: DeleteRecordInputBoundary, owner_id: UUID) -> Sequence[UUID]:
        input_ids = boundary.ids
        records_to_be_deleted = await self._fetch_records_from_db(input_ids=input_ids, owner_id=owner_id)

        return await self._record_repository.delete_by_id(ids=[e.id for e in records_to_be_deleted])

    async def _fetch_records_from_db(self, input_ids: Sequence[UUID], owner_id: UUID) -> Sequence[Record]:
        existing_records = await self._record_repository.search_by_id(ids=input_ids)

        if len(input_ids) != len(existing_records):
            e_ids = [e.id for e in existing_records]
            not_found_ids = [str(p) for p in input_ids if p not in e_ids]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for record in existing_records:
            if record.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to delete some of the documents")

        return existing_records
