from typing import Sequence
from uuid import UUID

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.records.record import Record
from services.base.type_resolver import TypeResolver
from services.data_service.application.use_cases.records.models.update_record_input_boundary import (
    UpdateRecordInputBoundary,
)
from services.data_service.application.use_cases.records.update_record_inputs import UpdateRecordInputs


class UpdateRecordUseCase:
    def __init__(
        self,
        record_repo: RecordRepository,
        duplicate_check_service: DuplicateCheckService,
    ):
        self._record_repo = record_repo
        self._duplicate_check_service = duplicate_check_service

    async def execute_async(self, boundary: UpdateRecordInputBoundary, owner_id: UUID) -> Sequence[Record]:
        input_records = boundary.documents
        existing_records = await self._fetch_records_from_db(input_records=input_records, owner_id=owner_id)

        updated_records = []

        existing_record: Record
        input_record: UpdateRecordInputs
        for existing_record, input_record in zip(
            sorted(existing_records, key=lambda e: e.id), sorted(input_records, key=lambda e: e.id)
        ):
            domain_type = TypeResolver.get_record(type_id=input_record.type_id())
            updated_record = domain_type(
                **(existing_record.model_dump(by_alias=True) | input_record.model_dump(by_alias=True))
            )
            updated_records.append(updated_record)

        await self._duplicate_check_service.validate_no_document_duplicates(documents=updated_records)
        return await self._record_repo.update(records=updated_records)

    async def _fetch_records_from_db(
        self, input_records: Sequence[UpdateRecordInputs], owner_id: UUID
    ) -> Sequence[Record]:
        existing_records: Sequence[Record] = await self._record_repo.search_by_id(ids=[d.id for d in input_records])

        if len(input_records) != len(existing_records):
            ee = [e.id for e in existing_records]
            not_found_ids = [str(p.id) for p in input_records if p.id not in ee]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for record in existing_records:
            if record.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to update some of the documents")

        return existing_records
