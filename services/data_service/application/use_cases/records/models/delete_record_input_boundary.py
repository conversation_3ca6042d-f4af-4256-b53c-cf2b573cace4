from typing import Sequence
from uuid import UUID

from pydantic import Field, field_validator

from services.base.domain.schemas.shared import BaseDataModel


class DeleteRecordInputBoundary(BaseDataModel):
    ids: Sequence[UUID] = Field(min_length=1)

    @field_validator("ids")
    def duplicates_validator(cls, ids: Sequence[UUID]) -> Sequence[UUID]:
        ids_set = {d for d in ids}
        if len(ids_set) != len(ids):
            raise ValueError("duplicate entries found")
        return ids
