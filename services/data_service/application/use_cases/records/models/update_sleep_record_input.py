from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.records.sleep_record import (
    SleepRecordCategory,
    SleepR<PERSON>ordFields,
    SleepRecordIdentifier,
)
from services.data_service.application.models.shared import InputTimeIntervalStrict
from services.data_service.application.use_cases.events.models.shared import (
    IdentifiableInputDocument,
)


class UpdateSleepRecordInput(InputTimeIntervalStrict, IdentifiableInputDocument, SleepRecordIdentifier):
    type: Literal[DataType.SleepRecord] = Field(alias=SleepRecordFields.TYPE)
    category: SleepRecordCategory = Field(alias=SleepRecordFields.CATEGORY)
    stage: SleepStage = Field(alias=SleepRecordFields.STAGE)

    @computed_field()
    @property
    def content_hash(self) -> str:
        content_string: str = "".join(
            [
                self.timestamp.isoformat(),
                self.category.value,
            ]
        )
        return Hasher.content_sha256(content=content_string)
