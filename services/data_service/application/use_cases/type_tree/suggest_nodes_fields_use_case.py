from typing import Sequence, get_origin, get_args
from types import UnionType, NoneType

from pydantic import Field

from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class SuggestNodesFieldsUseCaseInputBoundary(BaseDataModel):
    nodes: Sequence[TreeNode]
    #max_depth: int = Field(default=0, ge=0)


class SuggestNodesFieldsUseCaseOutputBoundary(BaseDataModel):
    fields: dict[str, type]


class SuggestNodesFieldsUseCase:

    def execute(
        self, input_boundary: SuggestNodesFieldsUseCaseInputBoundary
    ) -> SuggestNodesFieldsUseCaseOutputBoundary:
        data_types = set.union(*(type_tree.get_node_data_types(node) for node in input_boundary.nodes))

        type_mappings = [
            FieldValidator.get_type_properties(data_type.to_domain_model()) for data_type in data_types
        ]

        common_keys = set.intersection(*(set(m.keys()) for m in type_mappings))
        combined_type_mapping = {k: type_mappings[0][k] for k in common_keys}

        # Expand nested fields hierarchically
        expanded_fields = self._expand_fields_hierarchically(combined_type_mapping)

        return SuggestNodesFieldsUseCaseOutputBoundary(fields=expanded_fields)

    def _expand_fields_hierarchically(self, type_mapping: dict[str, type], prefix: str = "") -> dict[str, type]:
        """
        Recursively expand nested BaseDataModel fields into dot notation.

        Args:
            type_mapping: Dictionary of field names to their types
            prefix: Current dot notation prefix for nested fields

        Returns:
            Dictionary with expanded dot notation field names and their primitive types
        """
        expanded = {}

        for field_name, field_type in type_mapping.items():
            full_field_name = f"{prefix}.{field_name}" if prefix else field_name

            # Handle Union types (e.g., Optional fields like int | None)
            resolved_type = self._resolve_union_type(field_type)

            # Check if this is a BaseDataModel that should be expanded
            if self._is_expandable_model(resolved_type):
                # Get the nested model's fields and expand them recursively
                nested_fields = FieldValidator.get_type_properties(resolved_type)
                nested_expanded = self._expand_fields_hierarchically(nested_fields, full_field_name)
                expanded.update(nested_expanded)
            else:
                # This is a primitive type, add it directly
                expanded[full_field_name] = field_type

        return expanded

    def _resolve_union_type(self, field_type: type) -> type:
        """
        Resolve Union types (like Optional) to get the actual data type.

        Args:
            field_type: The type to resolve

        Returns:
            The resolved non-None type, or the original type if not a Union
        """
        origin = get_origin(field_type)
        if origin in (UnionType, type(int | None)):  # Handle Union types
            args = get_args(field_type)
            # Filter out NoneType to get the actual type
            non_none_types = [arg for arg in args if arg is not NoneType]
            if len(non_none_types) == 1:
                return non_none_types[0]

        return field_type

    def _is_expandable_model(self, field_type: type) -> bool:
        """
        Check if a type is a BaseDataModel that should be expanded into nested fields.

        Args:
            field_type: The type to check

        Returns:
            True if the type should be expanded, False otherwise
        """
        try:
            return (
                hasattr(field_type, '__bases__') and
                issubclass(field_type, BaseDataModel) and
                hasattr(field_type, 'model_fields')
            )
        except (TypeError, AttributeError):
            return False