from typing import Sequence

from pydantic import Field

from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class SuggestNodesFieldsUseCaseInputBoundary(BaseDataModel):
    nodes: Sequence[TreeNode]
    #max_depth: int = Field(default=0, ge=0)


class SuggestNodesFieldsUseCaseOutputBoundary(BaseDataModel):
    fields: dict


class SuggestNodesFieldsUseCase:

    def execute(
        self, input_boundary: SuggestNodesFieldsUseCaseInputBoundary
    ) -> SuggestNodesFieldsUseCaseOutputBoundary:
        data_types = set.union(*(type_tree.get_node_data_types(node) for node in input_boundary.nodes))

        type_mappings = [
            FieldValidator.get_type_properties(data_type.to_domain_model()) for data_type in data_types
        ]

