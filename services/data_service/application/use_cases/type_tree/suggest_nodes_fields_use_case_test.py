import pytest
from types import UnionType
from typing import get_origin

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.type_tree.type_tree import TreeNode
from services.data_service.application.use_cases.type_tree.suggest_nodes_fields_use_case import (
    SuggestNodesFieldsUseCase,
    SuggestNodesFieldsUseCaseInputBoundary,
)


class TestSuggestNodesFieldsUseCase:
    
    def test_single_exercise_node_returns_exercise_fields(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        exercise_node = TreeNode.from_path("doc.event.exercise")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        assert len(result.fields) > 0
        
        # All events should have these common fields
        assert "name" in result.fields
        assert result.fields["name"] == str
        
        # Exercise should have rating field
        assert "rating" in result.fields
        # Rating is Optional[int], so it should be int | None or similar Union type
        rating_type = result.fields["rating"]
        assert rating_type == int | None or get_origin(rating_type) in (UnionType, type(int | None))
        
        # Exercise should have category field
        assert "category" in result.fields
        
    def test_single_nutrition_node_expands_nested_fields(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        assert len(result.fields) > 0
        
        # Common event fields
        assert "name" in result.fields
        assert result.fields["name"] == str
        
        # Nutrition-specific fields
        assert "brand" in result.fields
        assert "calories" in result.fields
        assert "consumed_amount" in result.fields
        assert result.fields["consumed_amount"] == Rounded6Float
        
        # Should NOT have the nested object itself
        assert "nutrients" not in result.fields
        
        # Should have expanded nutrients fields
        expected_nutrient_fields = [
            "nutrients.protein",
            "nutrients.fat", 
            "nutrients.carbohydrates",
            "nutrients.fiber",
            "nutrients.sugar",
            "nutrients.sodium",
            "nutrients.potassium",
            "nutrients.vitamin_a",
            "nutrients.vitamin_c",
            "nutrients.iron",
            "nutrients.calcium",
            "nutrients.saturated_fat",
            "nutrients.polyunsaturated_fat",
            "nutrients.monounsaturated_fat",
            "nutrients.trans_fat",
            "nutrients.cholesterol"
        ]
        
        for field in expected_nutrient_fields:
            assert field in result.fields, f"Expected nutrient field {field} not found"
            # All nutrient fields should be Optional[Rounded6Float]
            field_type = result.fields[field]
            assert field_type == Rounded6Float | None or get_origin(field_type) in (UnionType, type(Rounded6Float | None))
            
    def test_multiple_nutrition_nodes_returns_intersection(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        drink_node = TreeNode.from_path("doc.event.nutrition.drink")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node, drink_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        
        # Should have common fields between food and drink
        assert "name" in result.fields
        assert "brand" in result.fields
        assert "calories" in result.fields
        assert "consumed_amount" in result.fields
        assert "rating" in result.fields
        
        # Should have common expanded nutrients fields
        common_nutrient_fields = [
            "nutrients.protein",
            "nutrients.fat",
            "nutrients.carbohydrates"
        ]
        
        for field in common_nutrient_fields:
            assert field in result.fields, f"Expected common nutrient field {field} not found"
            
    def test_exercise_and_nutrition_intersection_returns_only_common_fields(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        exercise_node = TreeNode.from_path("doc.event.exercise")
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node, food_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        
        # Should have common event fields
        assert "name" in result.fields
        
        # Exercise-specific fields should NOT be present
        assert "distance" not in result.fields
        assert "elevation" not in result.fields
        
        # Nutrition-specific fields should NOT be present
        assert "brand" not in result.fields
        assert "calories" not in result.fields
        assert "consumed_amount" not in result.fields
        
        # Expanded nutrient fields should NOT be present
        assert "nutrients.protein" not in result.fields
        assert "nutrients.fat" not in result.fields
        
    def test_empty_nodes_list_raises_error(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[])
        
        # Act & Assert
        with pytest.raises(TypeError):  # set.union with empty sequence should raise TypeError
            use_case.execute(input_boundary)
            
    def test_resolve_union_type_handles_optional_types(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        
        # Act & Assert
        # Test with int | None
        resolved = use_case._resolve_union_type(int | None)
        assert resolved == int
        
        # Test with non-union type
        resolved = use_case._resolve_union_type(str)
        assert resolved == str
        
    def test_is_expandable_model_identifies_base_data_models(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        
        # Import a known BaseDataModel for testing
        from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
        from services.base.domain.schemas.shared import BaseDataModel
        
        # Act & Assert
        assert use_case._is_expandable_model(Nutrients) == True
        assert use_case._is_expandable_model(BaseDataModel) == True
        assert use_case._is_expandable_model(str) == False
        assert use_case._is_expandable_model(int) == False
        assert use_case._is_expandable_model(None) == False
        
    def test_expand_fields_hierarchically_with_simple_types(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        simple_mapping = {
            "name": str,
            "age": int,
            "height": float | None
        }
        
        # Act
        result = use_case._expand_fields_hierarchically(simple_mapping)
        
        # Assert
        assert result == simple_mapping  # Should be unchanged for primitive types
        
    def test_expand_fields_hierarchically_with_nested_prefix(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        simple_mapping = {
            "protein": float | None,
            "fat": float | None
        }
        
        # Act
        result = use_case._expand_fields_hierarchically(simple_mapping, prefix="nutrients")
        
        # Assert
        expected = {
            "nutrients.protein": float | None,
            "nutrients.fat": float | None
        }
        assert result == expected
        
    def test_cardio_node_has_distance_and_elevation_fields(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        cardio_node = TreeNode.from_path("doc.event.exercise.cardio")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[cardio_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert "name" in result.fields
        assert "distance" in result.fields
        assert "elevation" in result.fields
        assert "rating" in result.fields
        
    def test_strength_node_has_count_and_weight_fields(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        strength_node = TreeNode.from_path("doc.event.exercise.strength")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[strength_node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        assert "name" in result.fields
        assert "count" in result.fields
        assert "weight" in result.fields
        assert "rating" in result.fields

    def test_multiple_exercise_types_intersection(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        cardio_node = TreeNode.from_path("doc.event.exercise.cardio")
        strength_node = TreeNode.from_path("doc.event.exercise.strength")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[cardio_node, strength_node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        # Should have common exercise fields
        assert "name" in result.fields
        assert "rating" in result.fields
        assert "category" in result.fields

        # Should NOT have cardio-specific fields
        assert "distance" not in result.fields
        assert "elevation" not in result.fields

        # Should NOT have strength-specific fields
        assert "count" not in result.fields
        assert "weight" not in result.fields

    def test_drink_node_has_nutrition_fields_without_food_specific_consumed_type(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        drink_node = TreeNode.from_path("doc.event.nutrition.drink")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[drink_node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        assert "name" in result.fields
        assert "brand" in result.fields
        assert "calories" in result.fields
        assert "consumed_amount" in result.fields
        assert "consumed_type" in result.fields  # Drink has VolumeUnit | "item" | "serving"
        assert "nutrients.protein" in result.fields
        assert "nutrients.fat" in result.fields

    def test_single_node_with_no_nested_models(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        # Use a simpler event type that might not have nested models
        note_node = TreeNode.from_path("doc.event.note")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[note_node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        assert isinstance(result.fields, dict)
        assert "name" in result.fields
        # Should not have any nested fields since Note doesn't have complex nested models
        nested_fields = [field for field in result.fields.keys() if "." in field]
        # Note might still have some nested fields, but fewer than nutrition
        assert len(nested_fields) < 10  # Arbitrary threshold

    def test_three_different_node_types_intersection(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        exercise_node = TreeNode.from_path("doc.event.exercise")
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        note_node = TreeNode.from_path("doc.event.note")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node, food_node, note_node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        # Should only have the most basic common fields
        assert "name" in result.fields
        # Most type-specific fields should be filtered out
        assert "rating" not in result.fields or "rating" in result.fields  # Depends on if all three have rating
        assert "brand" not in result.fields
        assert "distance" not in result.fields
        assert "nutrients.protein" not in result.fields

    @pytest.mark.parametrize("node_path,expected_fields", [
        pytest.param(
            "doc.event.exercise",
            ["name", "rating", "category"],
            id="exercise-basic-fields"
        ),
        pytest.param(
            "doc.event.exercise.cardio",
            ["name", "rating", "category", "distance", "elevation"],
            id="cardio-specific-fields"
        ),
        pytest.param(
            "doc.event.exercise.strength",
            ["name", "rating", "category", "count", "weight"],
            id="strength-specific-fields"
        ),
        pytest.param(
            "doc.event.nutrition.food",
            ["name", "brand", "calories", "consumed_amount", "consumed_type", "rating"],
            id="food-basic-fields"
        ),
        pytest.param(
            "doc.event.nutrition.drink",
            ["name", "brand", "calories", "consumed_amount", "consumed_type", "rating"],
            id="drink-basic-fields"
        ),
    ])
    def test_single_node_contains_expected_fields(self, node_path, expected_fields):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        node = TreeNode.from_path(node_path)
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        for field in expected_fields:
            assert field in result.fields, f"Expected field '{field}' not found in {node_path} fields"

    @pytest.mark.parametrize("node_path,expected_nutrient_fields", [
        pytest.param(
            "doc.event.nutrition.food",
            ["nutrients.protein", "nutrients.fat", "nutrients.carbohydrates", "nutrients.vitamin_a"],
            id="food-nutrient-expansion"
        ),
        pytest.param(
            "doc.event.nutrition.drink",
            ["nutrients.protein", "nutrients.fat", "nutrients.carbohydrates", "nutrients.vitamin_c"],
            id="drink-nutrient-expansion"
        ),
        pytest.param(
            "doc.event.nutrition.supplement",
            ["nutrients.protein", "nutrients.fat", "nutrients.carbohydrates", "nutrients.calcium"],
            id="supplement-nutrient-expansion"
        ),
    ])
    def test_nutrition_nodes_expand_nutrient_fields(self, node_path, expected_nutrient_fields):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        node = TreeNode.from_path(node_path)
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[node])

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        # Should not have the nested object itself
        assert "nutrients" not in result.fields

        # Should have expanded nutrient fields
        for field in expected_nutrient_fields:
            assert field in result.fields, f"Expected nutrient field '{field}' not found in {node_path} fields"

    @pytest.mark.parametrize("node_paths,expected_common_fields,unexpected_fields", [
        pytest.param(
            ["doc.event.exercise.cardio", "doc.event.exercise.strength"],
            ["name", "rating", "category"],
            ["distance", "elevation", "count", "weight"],
            id="cardio-strength-intersection"
        ),
        pytest.param(
            ["doc.event.nutrition.food", "doc.event.nutrition.drink"],
            ["name", "brand", "calories", "consumed_amount", "rating", "nutrients.protein"],
            [],  # Both should have all nutrition fields
            id="food-drink-intersection"
        ),
        pytest.param(
            ["doc.event.exercise", "doc.event.nutrition.food"],
            ["name"],  # Very minimal intersection
            ["rating", "brand", "calories", "distance", "nutrients.protein"],
            id="exercise-nutrition-minimal-intersection"
        ),
    ])
    def test_multiple_nodes_intersection_behavior(self, node_paths, expected_common_fields, unexpected_fields):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        nodes = [TreeNode.from_path(path) for path in node_paths]
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=nodes)

        # Act
        result = use_case.execute(input_boundary)

        # Assert
        for field in expected_common_fields:
            assert field in result.fields, f"Expected common field '{field}' not found in intersection of {node_paths}"

        for field in unexpected_fields:
            assert field not in result.fields, f"Unexpected field '{field}' found in intersection of {node_paths}"
