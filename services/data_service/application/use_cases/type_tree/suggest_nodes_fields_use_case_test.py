import pytest

from services.base.domain.type_tree.type_tree import TreeNode
from services.data_service.application.use_cases.type_tree.suggest_nodes_fields_use_case import (
    SuggestNodesFieldsUseCase,
    SuggestNodesFieldsUseCaseInputBoundary,
)


class TestSuggestNodesFieldsUseCase:
    
    def test_single_exercise_node(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        exercise_node = TreeNode.from_path("doc.event.exercise")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        assert "name" in result.fields
        assert result.fields["name"] == str
        # Exercise should have rating field
        assert "rating" in result.fields
        assert result.fields["rating"] == int | None
        
    def test_single_nutrition_node(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        assert "name" in result.fields
        assert result.fields["name"] == str
        # Food should have nutrition-specific fields
        assert "brand" in result.fields
        assert "calories" in result.fields
        assert "consumed_amount" in result.fields
        
    def test_multiple_nutrition_nodes_intersection(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        drink_node = TreeNode.from_path("doc.event.nutrition.drink")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node, drink_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        # Should have common fields between food and drink
        assert "name" in result.fields
        assert "brand" in result.fields
        assert "calories" in result.fields
        assert "consumed_amount" in result.fields
        assert "rating" in result.fields
        
    def test_exercise_and_nutrition_intersection(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        exercise_node = TreeNode.from_path("doc.event.exercise")
        food_node = TreeNode.from_path("doc.event.nutrition.food")
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node, food_node])
        
        # Act
        result = use_case.execute(input_boundary)
        
        # Assert
        assert isinstance(result.fields, dict)
        # Should only have common fields between exercise and nutrition
        assert "name" in result.fields  # Common to all events
        # Exercise-specific fields should not be present
        assert "distance" not in result.fields
        assert "elevation" not in result.fields
        # Nutrition-specific fields should not be present
        assert "brand" not in result.fields
        assert "calories" not in result.fields
        
    def test_empty_nodes_list(self):
        # Arrange
        use_case = SuggestNodesFieldsUseCase()
        input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[])
        
        # Act & Assert
        with pytest.raises(TypeError):  # set.union with empty sequence should raise TypeError
            use_case.execute(input_boundary)
