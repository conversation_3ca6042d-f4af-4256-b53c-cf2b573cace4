from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Sequence, Type

from pydantic import Field

from services.base.application.io.httpclient import HttpClient
from services.base.domain.schemas.environment import SpaceTimeInput
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.models.air_quality_bucket import AirQualityBucket
from services.data_service.application.models.pollen_bucket import PollenBucket
from services.data_service.application.models.weather_bucket import WeatherBucket


class EnvironmentResponseBoundary[T](BaseDataModel):
    data: Sequence[T] = Field(min_length=1)


class FetchEnvironmentBySpaceTimeUseCase:
    def __init__(self, http_client: HttpClient, aq_url: str, weather_url: str, pollen_url: str):
        self._http_client = http_client
        self._urls = {
            AirQualityBucket: aq_url,
            WeatherBucket: weather_url,
            PollenBucket: pollen_url,
        }

    async def execute_async[T: AirQualityBucket | WeatherBucket | PollenBucket](
        self, space_time: SpaceTimeInput, environment_model: Type[T]
    ) -> EnvironmentResponseBoundary[T]:
        body = {"space_time_coordinates": [space_time.model_dump(by_alias=True, exclude_none=True)]}
        response = await self._http_client.do_request(
            url=self._urls[environment_model],
            response_model=EnvironmentResponseBoundary[environment_model],
            method=HTTPMethod.POST,
            body=body,
            timeout=10.0,
        )
        tz = space_time.end_time.tzinfo
        filtered_data = []
        for d in response.data:
            if space_time.timestamp <= d.timestamp <= space_time.end_time:
                d.timestamp = d.timestamp.astimezone(tz)
                filtered_data.append(d)
        return EnvironmentResponseBoundary[environment_model](data=filtered_data)
