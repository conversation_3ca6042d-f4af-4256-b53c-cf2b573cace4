from typing import AsyncIterator

from services.base.application.assets import Assets
from services.base.application.exceptions import BadRequestException, InvalidDatabaseRequestException
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.annotated_types import AssetId


class FetchAssetByIdUseCase:
    def __init__(self, object_storage_service: ObjectStorageService):
        self._object_storage_service = object_storage_service

    async def execute_async(self, user_uuid, asset_id: AssetId) -> AsyncIterator[bytes]:
        asset_path = Assets.generate_asset_path(asset_id=asset_id)
        try:
            return await self._object_storage_service.stream_object(
                container_name=Assets.generate_user_storage_container_name(user_uuid=user_uuid), object_name=asset_path
            )
        except InvalidDatabaseRequestException:
            raise BadRequestException(message="error looking up given asset ids")
