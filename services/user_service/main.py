from contextlib import asynccontextmanager

from fastapi import <PERSON>API
from fastapi.encoders import ENCODERS_BY_TYPE
from fastapi_health import health
from fastapi_injector import attach_injector
from pydantic_core import PydanticUndefinedType

from services.base.api.exception_handlers import set_default_exception_handlers
from services.base.api.health_endpoints import server_run_time
from services.base.api.middleware.cors_middleware import add_cors_middleware
from services.base.api.set_logging import set_uvicorn_logging
from services.base.telemetry.fastapi_instrumentor import instrument_app
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.user_service.api.api_auth_endpoints import api_auth_router
from services.user_service.api.inbox_message_endpoints import message_router
from services.user_service.api.notification_inbox_endpoints import notification_router
from services.user_service.api.v02_sign_in_endpoints import v02_sign_in_router
from services.user_service.api.v02_user_endpoints import v01_user_router
from services.user_service.api.v1.user_endpoints import user_router
from services.user_service.dependency_bootstrapper import DependencyBootstrapper
from services.user_service.scheduler import Scheduler
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL, RUN_ENV_PRODUCTION
from settings.app_secrets import secrets

# TODO: Fixes issues with list query params until [https://github.com/tiangolo/fastapi/discussions/10331] is resolved
ENCODERS_BY_TYPE[PydanticUndefinedType] = lambda o: None

# telemetry
set_uvicorn_logging()
TelemetryInstrumentor.initialize(service_name="user_service", settings=settings, secrets=secrets)


@asynccontextmanager
async def lifespan(_: FastAPI):
    # startup
    scheduler.start()

    yield

    # shutdown
    scheduler.shutdown()
    await bootstrapper.cleanup()


app = FastAPI(
    root_path=None if settings.RUN_ENV == RUN_ENV_LOCAL else "/user",
    openapi_url="/openapi.json" if not settings.RUN_ENV == RUN_ENV_PRODUCTION else None,
    title="User service",
    version="0.2",
    lifespan=lifespan,
)
instrument_app(app=app, settings=settings)

set_default_exception_handlers(app)
add_cors_middleware(app=app)
# routes
app.add_api_route("/health", health([server_run_time]), response_model=dict[str, str])
app.include_router(user_router)
app.include_router(v01_user_router)
app.include_router(notification_router)
app.include_router(v02_sign_in_router)
app.include_router(api_auth_router)
app.include_router(message_router)

bootstrapper = DependencyBootstrapper().build()
attach_injector(app=app, injector=bootstrapper.injector)
scheduler = Scheduler(bootstrapper=bootstrapper)
