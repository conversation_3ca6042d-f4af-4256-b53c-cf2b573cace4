from dataclasses import dataclass
from datetime import datetime, time, timedelta
from zoneinfo import ZoneInfo

import pytest

from services.user_service.application.use_cases.remind_user_activity_use_case import RemindUserActivityUseCase


class TestRemindUserActivityUseCase:
    @dataclass
    class TestShouldUserBeRemindedInput:
        now: datetime
        hour_of_triggering: time
        last_logged_at: datetime
        staleness_period: timedelta

    @pytest.mark.parametrize(
        "input",
        [
            # Exactly staleness
            TestShouldUserBeRemindedInput(
                now=datetime(hour=20, year=2023, month=1, day=3, tzinfo=ZoneInfo("UTC")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("UTC")),
            ),
            TestShouldUserBeRemindedInput(
                now=datetime(hour=20, year=2023, month=1, day=4, tzinfo=ZoneInfo("UTC")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=3),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("UTC")),
            ),
            TestShouldUserBeRemindedInput(
                now=datetime(hour=1, year=2023, month=1, day=5, tzinfo=ZoneInfo("EST")),
                hour_of_triggering=time(hour=1),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("EST")),
            ),
            TestShouldUserBeRemindedInput(
                now=datetime(hour=10, year=2023, month=2, day=4, tzinfo=ZoneInfo("CET")),
                hour_of_triggering=time(hour=10),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("CET")),
            ),
        ],
    )
    def test_should_user_be_reminded_stale_user_should_remind(self, input: TestShouldUserBeRemindedInput):
        assert RemindUserActivityUseCase.should_user_be_reminded(
            now=input.now,
            local_time_of_triggering=input.hour_of_triggering,
            user_last_logged_at=input.last_logged_at,
            staleness_period=input.staleness_period,
        )

    @pytest.mark.parametrize(
        "input",
        [
            # Less than day
            TestShouldUserBeRemindedInput(
                now=datetime(hour=20, year=2023, month=1, day=2, tzinfo=ZoneInfo("UTC")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=2, tzinfo=ZoneInfo("UTC")),
            ),
            # Less than 3 days
            TestShouldUserBeRemindedInput(
                now=datetime(hour=20, year=2023, month=1, day=2, tzinfo=ZoneInfo("CET")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=3),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("CET")),
            ),
            # Non triggering hour
            TestShouldUserBeRemindedInput(
                now=datetime(hour=1, year=2023, month=1, day=4, tzinfo=ZoneInfo("EST")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=1, tzinfo=ZoneInfo("EST")),
            ),
            # Last logged at later than now
            TestShouldUserBeRemindedInput(
                now=datetime(hour=20, year=2023, month=1, day=3, tzinfo=ZoneInfo("EST")),
                hour_of_triggering=time(hour=20),
                staleness_period=timedelta(days=2),
                last_logged_at=datetime(year=2023, month=1, day=5, tzinfo=ZoneInfo("EST")),
            ),
        ],
    )
    def test_should_user_be_reminded_user_should_not_remind(self, input: TestShouldUserBeRemindedInput):
        assert not RemindUserActivityUseCase.should_user_be_reminded(
            now=input.now,
            local_time_of_triggering=input.hour_of_triggering,
            user_last_logged_at=input.last_logged_at,
            staleness_period=input.staleness_period,
        )
