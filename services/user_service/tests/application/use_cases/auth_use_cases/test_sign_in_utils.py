from contextlib import ContextDecorator

import pytest

from services.base.application.exceptions import InvalidPrivilegesException
from services.user_service.application.use_cases.auth_use_cases.sign_in_utils import validate_email
from settings.app_config import settings
from settings.app_constants import RUN_ENV_DEV, RUN_ENV_LOCAL, RUN_ENV_PRODUCTION, RUN_ENV_STAGING
from settings.app_secrets import secrets


class RunEnvContext(ContextDecorator):
    def __init__(self, run_env: str):
        self._old_env = settings.RUN_ENV
        self._new_env = run_env

    def __enter__(self):
        self.set_run_env(self._new_env)
        return self

    def __exit__(self, *exc):
        self.set_run_env(self._old_env)
        return False

    def set_run_env(self, value: str):
        settings.RUN_ENV = value


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("test@test"),
    ],
)
@RunEnvContext(RUN_ENV_PRODUCTION)
def test_validate_email_should_pass_production(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    assert validate_email(test_input) is None


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_STAGING)
def test_validate_email_should_pass_staging(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    assert validate_email(test_input) is None


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_STAGING)
def test_validate_external_email_should_pass_staging(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    secrets.LOGIN_MAIL_WHITELIST.append(test_input)
    assert validate_email(test_input) is None


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_STAGING)
def test_validate_email_invalid_email_should_fail_staging(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    with pytest.raises(InvalidPrivilegesException):
        validate_email(test_input)


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_DEV)
def test_validate_email_invalid_email_should_fail_dev(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    with pytest.raises(InvalidPrivilegesException):
        validate_email(test_input)


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_DEV)
def test_validate_internal_email_should_pass_dev(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    assert validate_email(test_input) is None


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
    ],
)
@RunEnvContext(RUN_ENV_DEV)
def test_validate_external_email_should_pass_dev(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    secrets.LOGIN_MAIL_WHITELIST.append(test_input)
    assert validate_email(test_input) is None


@pytest.mark.parametrize(
    "test_input",
    [
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("<EMAIL>"),
        ("test@test"),
    ],
)
@RunEnvContext(RUN_ENV_LOCAL)
def test_validate_email_should_pass_localhost(test_input):
    secrets.LOGIN_MAIL_WHITELIST.clear()
    assert validate_email(test_input) is None
