from datetime import datetime, timedelta, timezone

import pytest
from dateutil.rrule import DAILY, HOURLY

from services.base.domain.custom_rrule import CustomRRule
from services.user_service.application.use_cases.plan.remind_plans_workflow import RemindPlansWorkflow


class TestRemindPlansWorkflow:
    @pytest.mark.parametrize(
        "next_scheduled, recurrence, tick, now, expected_result",
        [
            # Plan is within the tick window
            (
                datetime(2023, 1, 1, 12, 0, tzinfo=timezone.utc),
                None,
                timedelta(minutes=10),
                datetime(2023, 1, 1, 11, 55, tzinfo=timezone.utc),
                datetime(2023, 1, 1, 12, 0, tzinfo=timezone.utc),
            ),
            # Hourly plan
            (
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                CustomRRule(
                    freq=HOURLY,
                    dtstart=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    interval=1,
                ),
                timedelta(minutes=10),
                datetime(2023, 1, 1, 9, 53, tzinfo=timezone.utc),
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
            ),
            # Daily Plan
            (
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                CustomRRule(
                    freq=DAILY,
                    dtstart=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    interval=1,
                ),
                timedelta(minutes=10),
                datetime(2023, 1, 13, 9, 53, tzinfo=timezone.utc),
                datetime(2023, 1, 13, 10, 0, tzinfo=timezone.utc),
            ),
        ],
    )
    def test_remind_at_passes(self, next_scheduled, recurrence, tick, now, expected_result):
        # Act
        result = RemindPlansWorkflow._remind_at(
            next_scheduled_at=next_scheduled, recurrence=recurrence, tick=tick, now=now
        )

        # Assert
        assert result == expected_result

    @pytest.mark.parametrize(
        "next_scheduled, recurrence, tick, now, expected_result",
        [
            # Plan is outside the tick window (too far in future)
            (
                datetime(2023, 1, 1, 14, 0, tzinfo=timezone.utc),
                None,
                timedelta(minutes=10),
                datetime(2023, 1, 1, 12, 0, tzinfo=timezone.utc),
                None,
            ),
            # Plan is in the past but no recurrence
            (
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                None,
                timedelta(minutes=10),
                datetime(2023, 1, 1, 12, 0, tzinfo=timezone.utc),
                None,
            ),
            # Plan is in the past with recurrence not within tick window
            (
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                CustomRRule(
                    freq=HOURLY,
                    dtstart=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    interval=2,
                ),
                timedelta(minutes=10),
                datetime(2023, 1, 1, 13, 30, tzinfo=timezone.utc),
                None,
            ),
            # Plan is too stale, not enough ticks to get to the tick window
            (
                datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                CustomRRule(
                    freq=HOURLY,
                    dtstart=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    interval=1,
                ),
                timedelta(minutes=10),
                datetime(2023, 1, 4, 12, 0, tzinfo=timezone.utc),
                None,
            ),
        ],
    )
    def test_remind_at_returns_none(self, next_scheduled, recurrence, tick, now, expected_result):
        # Act
        result = RemindPlansWorkflow._remind_at(
            next_scheduled_at=next_scheduled, recurrence=recurrence, tick=tick, now=now
        )

        # Assert
        assert result == expected_result
