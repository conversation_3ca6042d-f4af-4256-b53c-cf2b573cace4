from typing import Optional, Sequence

from services.base.application.notifications.push_notification_models import PushNotificationMessage
from services.user_service.application.use_cases.plan.remind_plan_summary_workflow import RemindPlanSummaryWorkflow
from services.user_service.dependency_bootstrapper import DependencyBootstrapper


class TestRemindPlanWorkflow:
    # @TODO: How to e2e test push notification workflows?
    def test_remind_plan_workflow_generates_push_notifications(self, dependency_bootstrapper: DependencyBootstrapper):
        def find_push_notification_by_substring(
            s: str, push_notifications: Sequence[PushNotificationMessage]
        ) -> Optional[PushNotificationMessage]:
            for pn in push_notifications:
                if s in pn.body:
                    return pn

        workflow = dependency_bootstrapper.get(interface=RemindPlanSummaryWorkflow)

        late_plan_messages = ["hello", "world", "!"]
        upcoming_plan_messages = ["upcoming"]

        push_notifications: Sequence[PushNotificationMessage] = workflow._generate_push_notifications(
            device_tokens=["123"],
            late_plan_messages=late_plan_messages,
            upcoming_plan_messages=upcoming_plan_messages,
        )

        late_push_notification = find_push_notification_by_substring("hello", push_notifications)
        upcoming_push_notification = find_push_notification_by_substring("upcoming", push_notifications)

        assert late_push_notification
        assert upcoming_push_notification

        assert late_push_notification.title == "You have 3 late plans"
        assert upcoming_push_notification.title == "Today, you have 1 upcoming plan"

        assert all(msg in late_push_notification.body for msg in late_plan_messages)
        assert all(msg in upcoming_push_notification.body for msg in upcoming_plan_messages)

    def test_remind_plan_workflow_push_notification_cuts_off_after_few_entries(
        self, dependency_bootstrapper: DependencyBootstrapper
    ):
        workflow = dependency_bootstrapper.get(interface=RemindPlanSummaryWorkflow)

        plan_messages = ["1", "2", "3", "4", "5"]

        push_notifications: Sequence[PushNotificationMessage] = workflow._generate_push_notifications(
            device_tokens=["123"],
            late_plan_messages=plan_messages,
            upcoming_plan_messages=plan_messages,
        )

        assert len(push_notifications) == 2

        for pn in push_notifications:
            assert "See more in the app!" in pn.body
