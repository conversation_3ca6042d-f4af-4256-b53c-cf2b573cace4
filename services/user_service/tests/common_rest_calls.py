from typing import Any

from httpx import ASGITransport, AsyncClient, Response
from starlette import status

from services.base.application.retry import retry_until_value
from services.user_service.main import app


@retry_until_value(
    max_times=10, value_validator=lambda x: x.status_code == status.HTTP_200_OK, raise_on_too_many_retries=False
)
async def call_post_endpoint(
    request_url: str,
    body_json: dict | None = None,
    params: dict[str, Any] | None = None,
    headers: dict[str, str] | None = None,
) -> Response:
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
        return await async_client.post(request_url, json=body_json, params=params, headers=headers)


@retry_until_value(
    max_times=10, value_validator=lambda x: x.status_code == status.HTTP_200_OK, raise_on_too_many_retries=False
)
async def call_get_endpoint(
    request_url: str, params: dict[str, Any] | None = None, headers: dict[str, str] | None = None
) -> Response:
    async with AsyncC<PERSON>(transport=ASGITransport(app=app), base_url="http://test") as async_client:
        return await async_client.get(url=request_url, params=params, headers=headers)


@retry_until_value(
    max_times=10, value_validator=lambda x: x.status_code == status.HTTP_200_OK, raise_on_too_many_retries=False
)
async def call_patch_endpoint(
    request_url: str, body_json: dict | None = None, headers: dict[str, str] | None = None
) -> Response:
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
        return await async_client.patch(request_url, json=body_json, headers=headers)


@retry_until_value(
    max_times=10, value_validator=lambda x: x.status_code == status.HTTP_200_OK, raise_on_too_many_retries=False
)
async def call_delete_endpoint(
    request_url: str, params: dict[str, Any] | None = None, headers: dict[str, str] | None = None
) -> Response:
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as async_client:
        return await async_client.delete(request_url, params=params, headers=headers)
