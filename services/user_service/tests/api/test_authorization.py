from starlette import status

from services.user_service.api.api_auth_endpoints import api_auth_router
from services.user_service.api.notification_inbox_endpoints import notification_router
from services.user_service.api.v02_user_endpoints import v01_user_router
from services.user_service.tests.api.conftest import get_path_operation


def test_user_endpoints_unauthorized(test_client):
    for route in (
        *v01_user_router.routes,
        *api_auth_router.routes,
        *notification_router.routes,
    ):
        for method in route.methods:
            response = get_path_operation(method=method, test_client=test_client)(route.path)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
