from datetime import timedel<PERSON>
from uuid import uuid4

from httpx import ASGITransport, AsyncClient
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.common_data_response import CommonDataResponse
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.infrastructure.database.opensearch.index_settings.user_action_log import UserLogsIndexModel
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.base.tests.domain.builders.user_action_log_builder import UserActionLogBuilder
from services.user_service.api.urls import V02SignInEndpointUrls
from services.user_service.application.use_cases.get_user_logs_use_case.get_user_logs_use_case_output import (
    UserActionLogOutput,
)
from services.user_service.main import app


async def test_delete_user_should_be_deleted_and_not_found(member_user_repository: MemberUserRepository):
    # Arrange
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    user = MemberUserBuilder().build()
    await member_user_repository.insert_or_update(user)
    access_token = generate_access_token(
        user_uuid=user.user_uuid,
        time_delta=timedelta(minutes=10),
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    # Act
    request_url = V02SignInEndpointUrls.DELETE_USER

    delete_response = await client.delete(request_url, headers=headers)

    re_fetched_user = await member_user_repository.get(
        wrapper=ReadFromDatabaseWrapper(search_keys={DocumentLabels.USER_UUID: user.user_uuid})
    )
    # Assert
    assert delete_response.status_code == status.HTTP_200_OK
    assert not re_fetched_user


async def test_delete_non_existing_user_should_return_bad_request():
    # Arrange
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    user_uuid = uuid4()
    access_token = generate_access_token(
        user_uuid=user_uuid,
        time_delta=timedelta(minutes=10),
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    # Act
    request_url = V02SignInEndpointUrls.DELETE_USER

    delete_response = await client.delete(request_url, headers=headers)

    # Assert
    assert delete_response.status_code == status.HTTP_400_BAD_REQUEST


async def test_get_user_logs_endpoint_should_return_pass(async_os_client):
    # Arrange
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    owner_id = uuid4()
    access_token = generate_access_token(
        user_uuid=owner_id,
        time_delta=timedelta(minutes=10),
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    expected_log = UserActionLogBuilder().with_owner_id(owner_id=owner_id).build()
    await async_os_client.index(
        index=UserLogsIndexModel.name,
        body=expected_log.model_dump_json(by_alias=True, exclude={DocumentLabels.ID: True}),
        id=expected_log.id,
        refresh="wait_for",
    )

    # Act
    request_url = V02SignInEndpointUrls.USER_LOGS

    response = await client.get(request_url, headers=headers)

    # Assert
    assert response.status_code == status.HTTP_200_OK
    data = CommonDataResponse[UserActionLogOutput](**response.json())
    for out_log in data.Values:
        assert isinstance(out_log, UserActionLogOutput)
        assert out_log.events == expected_log.events
        assert out_log.filename == expected_log.filename
        assert out_log.action == expected_log.action.value
        assert out_log.timestamp == expected_log.timestamp

    await async_os_client.delete(index=UserLogsIndexModel.name, id=expected_log.id)


async def test_get_user_logs_endpoint_should_return_no_content():
    # Arrange
    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    user_uuid = uuid4()
    access_token = generate_access_token(
        user_uuid=user_uuid,
        time_delta=timedelta(minutes=10),
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
    }

    # Act
    request_url = V02SignInEndpointUrls.USER_LOGS

    response = await client.get(request_url, headers=headers)

    # Assert
    assert response.status_code == status.HTTP_204_NO_CONTENT
