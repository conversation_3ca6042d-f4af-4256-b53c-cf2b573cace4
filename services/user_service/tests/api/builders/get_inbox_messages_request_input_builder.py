from __future__ import annotations

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPI
from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.models.sorts import Sort


class ListInboxMessageRequestInputBuilder:
    def __init__(self):
        self._sort: SortRequestInput | None = None
        self._limit: int | None = None
        self._query: LeafQueryAPI | CompoundBooleanQueryAPI | None = None

    def with_limit(self, limit: int) -> ListInboxMessageRequestInputBuilder:
        self._limit = limit
        return self

    def with_sort(self, sort: Sort) -> ListInboxMessageRequestInputBuilder:
        self._sort = sort
        return self

    def with_query(self, query: LeafQueryAPI | CompoundBooleanQueryAPI) -> ListInboxMessageRequestInputBuilder:
        self._query = query
        return self

    def build_body_as_dict(self) -> dict:
        sort_as_dict = {"sort": {"field_name": self._sort.field_name, "order": self._sort.order}} if self._sort else {}
        query_as_dict = {"query": self._query.model_dump(mode="json")} if self._query else {}

        return {**sort_as_dict, **query_as_dict}

    def build_params_as_dict(self) -> dict:
        return {"limit": self._limit} if self._limit else {}
