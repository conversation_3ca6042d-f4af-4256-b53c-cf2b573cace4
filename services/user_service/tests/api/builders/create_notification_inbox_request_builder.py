from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.notification import NotificationType
from services.user_service.api.request_models.create_notification_request_input import (
    CreateNotificationRequestInput,
    CreateNotificationRequestInputItem,
)


class CreateNotificationInboxRequestBuilder:
    def build_n(self, n: int) -> CreateNotificationRequestInput:
        return CreateNotificationRequestInput(
            values=[CreateNotificationRequestInputItemBuilder().build() for _ in range(n)]
        )


class CreateNotificationRequestInputItemBuilder:
    def build(self) -> CreateNotificationRequestInputItem:
        return CreateNotificationRequestInputItem(
            title=PrimitiveTypesGenerator.generate_random_string(),
            message=PrimitiveTypesGenerator.generate_random_string(),
            timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(),
            type=PrimitiveTypesGenerator.generate_random_enum(NotificationType),
        )
