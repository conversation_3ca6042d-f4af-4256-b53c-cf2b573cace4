from typing import List, Optional
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.schemas.shared import BaseDataModel


class ListInboxMessagesContinuationToken(BaseDataModel):
    token: str
    include_senders: Optional[List[UUID]] = Field(default=None, min_length=1)
    include_statuses: Optional[List[InboxMessageStatus]] = Field(default=None, min_length=1)
