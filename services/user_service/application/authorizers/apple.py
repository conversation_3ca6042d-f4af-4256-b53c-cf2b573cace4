import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from uuid import UUID

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.exceptions import RuntimeException
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.apple_response_mode import AppleResponseMode
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import SupportedLoginProviders
from services.base.domain.repository.login_apple_repository import LoginAppleRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.user_service.application.boundaries.apple_authorize_callback import AppleAuthorizeCallback
from services.user_service.application.use_cases.auth_use_cases.auth import ProviderOAuth2LoginAuthorizer
from services.user_service.application.use_cases.auth_use_cases.enums.sign_in_action_type import SignInActionType
from services.user_service.application.use_cases.auth_use_cases.providers.apple.sign_in_utils import AppleOAuth2Utils
from services.user_service.application.use_cases.auth_use_cases.utils import (
    get_common_oauth2_authorize_parameters,
    get_oauth2_authorize_url,
)
from services.user_service.domain.providers.apple.constants import AppleOAuthKeys


class AppleOAuth2Authorizer(ProviderOAuth2LoginAuthorizer):
    def __init__(
        self,
        member_user_repository: MemberUserRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
        login_repository: LoginAppleRepository,
        message_broker_client: AsyncMessageBrokerClient,
    ):
        self._member_user_repository = member_user_repository
        self._member_user_settings_repository = member_user_settings_repository
        self._login_repository = login_repository
        self._message_broker_client = message_broker_client

    def build_login_auth_url(
        self, redirect_uri: str, client: ClientApps, provider: SupportedLoginProviders, state: str, nonce: Optional[str]
    ) -> str:
        params = get_common_oauth2_authorize_parameters(
            redirect_uri=redirect_uri, scope="name email", client=client, provider=provider.value, state=state
        )

        params[AppleOAuthKeys.RESPONSE_MODE.value] = AppleResponseMode.FORM_POST.value

        if nonce:
            params[AppleOAuthKeys.NONCE.value] = nonce

        return join_as_url(
            base_url=get_oauth2_authorize_url(client=client, provider=provider.value), query_params=params
        )

    def get_login_auth_parameters(
        self, client: ClientApps, provider: SupportedLoginProviders, state: str
    ) -> dict[str, str]:
        return {}

    async def register_or_login_user(
        self,
        client: ClientApps,
        response_body: AppleAuthorizeCallback,
        provider: SupportedLoginProviders,
        user_uuid: Optional[UUID] = None,
    ) -> Tuple[MemberUser, SignInActionType]:
        # response from provider token endpoint, also checks client validity
        try:
            provider_id_token_jwt: str = response_body.id_token
        except Exception as error:
            logging.exception(
                f"Apple Oauth2 response does not include id token, response: {response_body}, err: {error}"
            )
            raise RuntimeException(message="Unable to process Apple identity response.") from error

        decoded_id_token = await AppleOAuth2Utils.validate_user(
            provider_id_token_jwt=provider_id_token_jwt, client_app=client
        )
        return await AppleOAuth2Utils.register_or_update_user(
            provider_id_token=decoded_id_token,
            provider_user_object=response_body.user,
            member_user_repository=self._member_user_repository,
            login_apple_repository=self._login_repository,
            member_user_settings_repository=self._member_user_settings_repository,
            message_broker_client=self._message_broker_client,
            user_uuid=user_uuid,
        )
