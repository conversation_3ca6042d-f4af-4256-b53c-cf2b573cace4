import logging
from typing import List
from uuid import UUID

from sqlalchemy.orm import Session

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.enums.provider import SupportedDataProviders
from services.base.domain.enums.upload_states import UploadStates
from services.base.infrastructure.database.sql_alchemy.models.upload_state_entity import UploadStateEntity
from services.user_service.api.boundaries.provider_upload_state_output import ProviderUploadStateOutput


class GetUploadStates(UseCaseBase):
    def execute(self, user_uuid: UUID, db_session: Session, **kwargs) -> List[ProviderUploadStateOutput]:
        try:
            output = []

            # code dependant alternative for filter_by could be unpacking dict like:
            # **{UploadState.user_uuid.name: current_user.user_uuid}
            upload_states: List[UploadStateEntity] = UploadStateEntity.get_all_upload_states_for_user_uuid(
                db_session=db_session, user_uuid=user_uuid
            )
            for upload_state in upload_states:
                output.append(
                    ProviderUploadStateOutput(
                        provider=SupportedDataProviders(upload_state.provider),
                        state=UploadStates(upload_state.state),
                        loading_progress=upload_state.loading_progress,
                        in_progress=upload_state.in_progress,
                        user_filename=upload_state.user_filename,
                        timestamp=upload_state.last_timestamp.isoformat(),
                    )
                )

            return output

        except Exception as error:
            logging.exception("Error getting from upload_states, %s", repr(error))
