from asyncio import Task, TaskGroup
from datetime import datetime, time, timedelta
from typing import Sequence
from uuid import UUID

from services.base.application.notifications.push_notification_models import PushNotificationMessage
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.application.utils.member_user.member_user_settings import get_user_timezone
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.user_service.application.use_cases.plan.plan_late_and_upcoming_use_case import (
    PlanLateAndUpcomingUseCase,
)
from services.user_service.application.use_cases.plan.plan_stringifier import PlanStringifier


class RemindPlanSummaryWorkflow:
    def __init__(
        self,
        user_repository: MemberUserRepository,
        member_user_device_repository: MemberUserDeviceRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
        plan_late_and_upcoming_use_case: PlanLateAndUpcomingUseCase,
        push_notification_service: PushNotificationService,
    ):
        self._user_repo = user_repository
        self._settings_repo = member_user_settings_repository
        self._plan_late_and_upcoming_use_case = plan_late_and_upcoming_use_case
        self._device_repo = member_user_device_repository
        self._push_notification_service = push_notification_service
        workflow_counter = TelemetryInstrumentor.get_counter(name=__name__)
        self._counter = workflow_counter

    async def run(self, local_time_of_triggering: time, active_considered_period: timedelta):
        self._counter.add(1)

        async for users in self._user_repo.yield_active_users(delta=active_considered_period):
            push_notifications: list[PushNotificationMessage] = []

            async with TaskGroup() as group:
                tasks: Sequence[Task] = [
                    group.create_task(
                        self._handle_user(owner_id=user.user_uuid, local_time_of_triggering=local_time_of_triggering)
                    )
                    for user in users
                ]

            for task in tasks:
                if result := task.result():
                    push_notifications.extend(result)

            if push_notifications:
                self._push_notification_service.publish(messages=push_notifications)

    async def _handle_user(self, owner_id: UUID, local_time_of_triggering: time) -> Sequence[PushNotificationMessage]:
        device_tokens = [device.device_token for device in await self._device_repo.get_by_uuid(user_uuid=owner_id)]
        # If the user has no registered device we cannot send push notifications
        if not device_tokens:
            return []

        user_timezone = await get_user_timezone(uuid=owner_id, member_user_settings_repo=self._settings_repo)
        now = datetime.now(tz=user_timezone)
        if now.hour != local_time_of_triggering.hour:
            return []

        plan_times = await self._plan_late_and_upcoming_use_case.execute_async(
            owner_id=owner_id, user_timezone=user_timezone, size=10
        )
        upcoming_plans_messages = PlanStringifier.stringify_list(
            reminders=[
                PlanStringifier.stringify_plan_upcoming_time(
                    plan_name=plan.name, next_scheduled_at=plan.next_scheduled_at
                )
                for plan in plan_times.upcoming
            ]
        )
        late_plans_messages = PlanStringifier.stringify_list(
            reminders=[
                PlanStringifier.stringify_plan_late_time(plan_name=plan.name, delta=now - plan.next_scheduled_at)
                for plan in plan_times.late
            ]
        )

        return self._generate_push_notifications(
            device_tokens=device_tokens,
            late_plan_messages=late_plans_messages,
            upcoming_plan_messages=upcoming_plans_messages,
        )

    def _generate_push_notifications(
        self,
        device_tokens: Sequence[str],
        late_plan_messages: list[str],
        upcoming_plan_messages: list[str],
    ) -> Sequence[PushNotificationMessage]:
        messages: list[PushNotificationMessage] = []

        if late_plan_messages:
            title = PlanStringifier.stringify_plan_summary_late(plan_count=len(late_plan_messages))

            # Due to the limitation of the push notification size, we can only show a few plans
            if len(late_plan_messages) > 4:
                late_plan_messages = late_plan_messages[:4]
                late_plan_messages.append("See more in the app!")

            body = "\n".join(late_plan_messages)

            messages.append(
                PushNotificationMessage(
                    title=title,
                    body=body,
                    device_tokens=device_tokens,
                )
            )

        if upcoming_plan_messages:
            title = PlanStringifier.stringify_plan_summary_upcoming(plan_count=len(upcoming_plan_messages))

            # Due to the limitation of the push notification size, we can only show a few plans
            if len(upcoming_plan_messages) > 4:
                upcoming_plan_messages = upcoming_plan_messages[:4]
                upcoming_plan_messages.append("See more in the app!")

            body = "\n".join(upcoming_plan_messages)

            messages.append(
                PushNotificationMessage(
                    title=title,
                    body=body,
                    device_tokens=device_tokens,
                )
            )

        return messages
