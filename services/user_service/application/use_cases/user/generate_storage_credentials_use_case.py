from uuid import UUID

from pydantic import HttpUrl

from services.base.application.assets import Assets
from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.schemas.shared import BaseDataModel


class GenerateAssetsCredentialsUseCaseOutputBoundary(BaseDataModel):
    credentials: NonEmptyStr
    storage_base_url: HttpUrl


class GenerateAssetsCredentialsUseCase(AsyncUseCaseBase):
    def __init__(self, object_storage: ObjectStorageService):
        self._object_storage = object_storage

    async def execute_async(self, user_uuid: UUID) -> GenerateAssetsCredentialsUseCaseOutputBoundary:
        container_name = Assets.generate_user_storage_container_name(user_uuid=user_uuid)
        await self._object_storage.create_container(container_name=container_name)
        return GenerateAssetsCredentialsUseCaseOutputBoundary(
            credentials=await self._object_storage.generate_container_access_credentials(container_name=container_name),
            storage_base_url=self._object_storage.base_url,
        )
