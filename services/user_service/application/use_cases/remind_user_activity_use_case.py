import logging
from asyncio import TaskGroup
from datetime import datetime, time, timedelta, timezone
from typing import List
from zoneinfo import ZoneInfo

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.notifications.push_notification_models import PushNotificationMessage
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.wrappers import Range, ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor


class RemindUserActivityUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        user_repository: MemberUserRepository,
        settings_repository: MemberUserSettingsRepository,
        device_repository: MemberUserDeviceRepository,
        push_notification_service: PushNotificationService,
    ):
        self._user_repository = user_repository
        self._settings_repository = settings_repository
        self._device_repository = device_repository
        self._push_notification_service = push_notification_service
        workflow_counter = TelemetryInstrumentor.get_counter(
            name=__name__,
        )
        self._counter = workflow_counter

    async def execute_async(
        self,
        staleness_period: timedelta,
        local_time_of_triggering: time,
    ):
        self._counter.add(1)
        now = datetime.now(timezone.utc)
        async for user_batch in self._user_repository.yield_results(
            wrapper=ReadFromDatabaseWrapper(
                search_keys={}, range_filter=Range(field_name="last_logged_at", end_date=now - staleness_period)
            ),
            size=100,
        ):
            messages: List[PushNotificationMessage] = []
            async with TaskGroup() as group:
                tasks = [
                    group.create_task(
                        self.do_execute(
                            now=now,
                            local_time_of_triggering=local_time_of_triggering,
                            user=user,
                            staleness_period=staleness_period,
                        )
                    )
                    for user in user_batch
                ]
            for task in tasks:
                if result := task.result():
                    messages.append(result)
            if messages:
                self._push_notification_service.publish(messages=messages)

    async def do_execute(
        self, now: datetime, user: MemberUser, local_time_of_triggering: time, staleness_period: timedelta
    ) -> PushNotificationMessage | None:
        try:
            settings = await self._settings_repository.get_by_uuid(user.user_uuid)
            user_timezone = settings.general.timezone if settings and settings.general else ZoneInfo("UTC")
            if self.should_user_be_reminded(
                user_last_logged_at=user.last_logged_at.astimezone(tz=user_timezone),
                now=now.astimezone(tz=user_timezone),
                local_time_of_triggering=local_time_of_triggering,
                staleness_period=staleness_period,
            ):
                device_tokens = [
                    device.device_token
                    for device in await self._device_repository.get_by_uuid(user_uuid=user.user_uuid)
                ]

                return (
                    PushNotificationMessage(
                        device_tokens=device_tokens,
                        title="Your Best Life awaits you \ud83d\ude4c",
                        body="Tap into better health before it’s too late! Log symptoms, activity, vitals, and more to start generating insights today.",
                    )
                    if device_tokens
                    else None
                )
            return None
        except Exception as error:
            logging.exception(f"Error while reminding inactive users: user: {user.user_uuid}, errs: {error}")

        return None

    @staticmethod
    def should_user_be_reminded(
        user_last_logged_at: datetime, now: datetime, local_time_of_triggering: time, staleness_period: timedelta
    ) -> bool:
        """Returns True if the user has not logged in for predefined period of time
        AND if now time hour equals to the provided local hour of triggering"""
        if (now - user_last_logged_at) >= staleness_period:
            day_diff = (now - user_last_logged_at).days
            if day_diff % staleness_period.days == 0 and now.hour == local_time_of_triggering.hour:
                return True
        return False
