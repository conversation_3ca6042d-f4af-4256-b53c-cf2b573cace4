from typing import Optional

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.enums.client_apps import ClientApps
from services.user_service.application.use_cases.auth_use_cases.sign_in_authorizer import (
    GetSignInParametersOutputBoundary,
    SignInAuthorizer,
)


class GetSignInParametersUseCase(UseCaseBase):
    def execute(
        self,
        authorizer: SignInAuthorizer,
        client: ClientApps,
        state: str,
        nonce: Optional[str] = None,
    ) -> GetSignInParametersOutputBoundary:
        return authorizer.get_sign_in_parameters(client=client, state=state, nonce=nonce)
