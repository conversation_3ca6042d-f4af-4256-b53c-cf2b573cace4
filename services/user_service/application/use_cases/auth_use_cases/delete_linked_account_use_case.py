from typing import List
from uuid import UUID

from services.base.application.async_use_case_base import AsyncUse<PERSON>aseBase
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.linked_data_provider import LinkedDataProvider
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2


class DeleteLinkedAccountUseCase(AsyncUseCaseBase):
    def __init__(self, member_oauth2_repository: MemberUserOAuth2Repository):
        self._member_oauth2_repository = member_oauth2_repository

    async def execute_async(
        self,
        provider: LinkedDataProvider,
        user_uuid: UUID,
    ):
        wrapper = ReadFromDatabaseWrapper(
            search_keys={DocumentLabels.USER_UUID: user_uuid, DocumentLabels.PROVIDER: provider.value}
        )
        rows: List[MemberUserOAuth2] = await self._member_oauth2_repository.get(wrapper)
        if not rows:
            raise NoContentException(message="Provider not linked")

        for row in rows:
            await self._member_oauth2_repository.delete(row)
