from typing import Optional

from services.base.application.use_case_base import UseCaseBase
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.client_apps import ClientApps
from services.user_service.application.use_cases.auth_use_cases.sign_in_authorizer import (
    SignInAuthorizer,
)


class GetSignInUrlUseCase(UseCaseBase):
    def execute(
        self,
        authorizer: SignInAuthorizer,
        client: ClientApps,
        state: str,
        nonce: Optional[str] = None,
    ) -> str:
        output = authorizer.get_sign_in_parameters(client=client, state=state, nonce=nonce)
        return join_as_url(base_url=authorizer.auth_url, query_params=output.model_dump(exclude_none=True))
