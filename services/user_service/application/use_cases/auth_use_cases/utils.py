from typing import Dict

from fastapi import HTTPException
from starlette import status

from services.base.application.constants import OAuth2Keys
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import Provider
from services.user_service.application.use_cases.auth_use_cases.settings_mapping import provider_settings_auth_options
from services.user_service.domain.settings import OAuth2SettingsTemplate


def get_common_oauth2_authorize_parameters(
    redirect_uri: str, scope: str, client: ClientApps, provider: str, state: str
) -> Dict[str, str]:
    oauth2_settings = get_oauth2_client(client=client, provider=Provider(provider))

    oauth2_settings.AUTH_URL_PARAMS[OAuth2Keys.REDIRECT_URI] = redirect_uri
    oauth2_settings.AUTH_URL_PARAMS[OAuth2Keys.SCOPE] = scope
    oauth2_settings.AUTH_URL_PARAMS[OAuth2Keys.STATE] = state
    return oauth2_settings.model_dump()["AUTH_URL_PARAMS"]


def get_oauth2_authorize_url(client: Client<PERSON><PERSON>, provider: str) -> str:
    oauth2_settings = get_oauth2_client(client=client, provider=Provider(provider))
    return oauth2_settings.AUTH_URL


def get_oauth2_token_url(client: ClientApps, provider: Provider) -> str:
    oauth2_settings = get_oauth2_client(client=client, provider=provider)
    return oauth2_settings.TOKEN_URL


def get_oauth2_client(client: ClientApps, provider: Provider) -> OAuth2SettingsTemplate:
    if client == ClientApps.WEB:
        return provider_settings_auth_options[provider].get_web_settings()
    elif client == ClientApps.ANDROID:
        return provider_settings_auth_options[provider].get_web_settings()
    elif client == ClientApps.IOS and provider == Provider.GOOGLE:
        return provider_settings_auth_options[provider].get_web_settings()
    elif client == ClientApps.IOS:
        return provider_settings_auth_options[provider].get_iOS_settings()
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported client for provider.",
        )
