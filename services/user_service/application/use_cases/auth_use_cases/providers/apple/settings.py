from typing import Dict, Optional

from services.base.application.constants import OAuth2Keys
from services.user_service.domain.providers.apple.constants import AppleOAuthKeys
from services.user_service.domain.settings import OAuth2ProviderSettingsWrapper, OAuth2SettingsTemplate
from settings.app_config import settings


class AppleOAuth2SettingsTemplate(OAuth2SettingsTemplate):
    AUTH_URL: str = "https://appleid.apple.com/auth/authorize"
    TOKEN_URL: str = "https://appleid.apple.com/auth/token"
    AUTH_URL_PARAMS: Dict[OAuth2Keys | AppleOAuthKeys, Optional[str]]
    TOKEN_URL_PARAMS: Dict[OAuth2Keys | AppleOAuthKeys, Optional[str]]


class AppleOAuth2Settings(OAuth2ProviderSettingsWrapper):
    @staticmethod
    def get_web_settings() -> AppleOAuth2SettingsTemplate:
        return AppleOAuth2SettingsTemplate(
            AUTH_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.APPLE_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.STATE: None,
                OAuth2Keys.RESPONSE_TYPE: "code id_token",
                OAuth2Keys.SCOPE: None,
                AppleOAuthKeys.RESPONSE_MODE: None,
            },
            TOKEN_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.APPLE_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.CLIENT_SECRET: None,
                OAuth2Keys.CODE: None,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.GRANT_TYPE: "authorization_code",
                OAuth2Keys.STATE: None,
            },
        )
