import logging
from datetime import datetime, timezone
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import UUID

from google.auth.transport import requests
from google.oauth2 import id_token

from services.base.application.async_message_broker_client import Async<PERSON>essageBrokerClient
from services.base.application.constants import OAuth2Keys
from services.base.application.exceptions import BadRequestException, InvalidPrivilegesException, RuntimeException
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.enums.provider import Provider
from services.base.domain.repository.login_google_repository import LoginGoogleRepository
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import Member<PERSON>ser
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2
from services.user_service.application.use_cases.auth_use_cases.enums.sign_in_action_type import SignInActionType
from services.user_service.application.use_cases.auth_use_cases.sign_in_utils import (
    create_login_model,
    create_user_settings_if_none,
    publish_account_linked,
    publish_user_registered,
    validate_email,
)
from services.user_service.application.use_cases.auth_use_cases.utils import get_oauth2_client
from services.user_service.domain.providers.google.constants import GoogleIdTokenKeys


class GoogleOAuth2Utils:
    @staticmethod
    async def validate_user(client: ClientApps, provider_id_token_jwt: str, provider: Provider) -> Dict[str, str]:
        """
        Verifies google user based on provided id_token
        """
        try:
            settings_class = get_oauth2_client(client=client, provider=provider)
            provider_user_info: Dict[str, str] = id_token.verify_oauth2_token(
                id_token=provider_id_token_jwt,
                request=requests.Request(),
                audience=settings_class.TOKEN_URL_PARAMS[OAuth2Keys.CLIENT_ID],
            )
            return provider_user_info
        except ValueError as e:
            # implementation note:
            # verify_oauth2_token raises ValueError for more errors besides expired e.g. "wrong audience"
            logging.exception(f"Error validating id token claims, token: {provider_id_token_jwt}, err: {e}")
            raise BadRequestException(message="Unable to validate identity")

    @staticmethod
    def update_user_model(user: MemberUser, provider_user_info: Dict[str, str]) -> MemberUser:
        """
        Updates user data object
        """
        # Update user data
        user.first_name = provider_user_info.get(GoogleIdTokenKeys.GIVEN_NAME.value, "")
        user.last_name = provider_user_info.get(GoogleIdTokenKeys.FAMILY_NAME.value, "")
        user.display_name = provider_user_info.get(GoogleIdTokenKeys.NAME.value, "")
        user.picture_link = provider_user_info.get(GoogleIdTokenKeys.PICTURE.value, "")

        if provider_user_info.get(GoogleIdTokenKeys.EMAIL_VERIFIED.value, False):
            # @TODO: consider whether this should propagate somewhere if it's update of existing
            user.primary_email = provider_user_info.get(GoogleIdTokenKeys.EMAIL.value, None)

        return user

    @staticmethod
    async def register_or_update_user(
        provider_user_info: Dict[str, str],
        login_google_repository: LoginGoogleRepository,
        member_user_repository: MemberUserRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
        message_broker_client: AsyncMessageBrokerClient,
        user_uuid: Optional[UUID] = None,
    ) -> Tuple[MemberUser, SignInActionType]:
        """
        Updates user in database
        """
        try:
            original_email = None

            validate_email(email=provider_user_info.get(GoogleIdTokenKeys.EMAIL.value, ""))
            user_google_id = str(provider_user_info.get(GoogleIdTokenKeys.SUB.value, ""))
            if not user_google_id:
                logging.exception(f"Unable to parse subject from google, data: {provider_user_info}")
                raise RuntimeException(message="Error while processing identity.")

            login_model = await login_google_repository.get_by_google_id(google_id=user_google_id)

            if user_uuid and login_model:
                if login_model.user_uuid == user_uuid:
                    raise BadRequestException(message="Account is already linked")

                if login_model.user_uuid != user_uuid:
                    raise BadRequestException(message="Can't link existing account")

            action_type = None
            if not user_uuid and not login_model:
                action_type = SignInActionType.REGISTERED
            if user_uuid and not login_model:
                action_type = SignInActionType.LINKED
            if not user_uuid and login_model:
                action_type = SignInActionType.SIGNED

            if not action_type:
                # Should never happen
                logging.error(
                    f"Error while processing sign in request, login_model: {login_model},"
                    f" provider_user_info: {provider_user_info}, user_uuid: {user_uuid}."
                )
                raise RuntimeException(message="an unknown error occurred")

            if action_type == SignInActionType.LINKED:
                member_user = await member_user_repository.get_by_uuid(user_uuid=user_uuid)
                original_email = member_user.primary_email
                if member_user.type == MemberUserType.ANONYMOUS:
                    member_user.type = MemberUserType.STANDARD
                    member_user = GoogleOAuth2Utils.update_user_model(
                        user=member_user, provider_user_info=provider_user_info
                    )
                    member_user = await member_user_repository.insert_or_update(member_user)

            # Signing in or Registering
            else:
                # Get user or create new one if it does not exist
                member_user = (
                    await member_user_repository.get_by_uuid(user_uuid=login_model.user_uuid)
                    if login_model
                    else MemberUser(last_logged_at=datetime.now(timezone.utc), created_at=datetime.now(timezone.utc))
                )

                # Update user fields
                member_user = GoogleOAuth2Utils.update_user_model(
                    user=member_user, provider_user_info=provider_user_info
                )

                member_user = await member_user_repository.insert_or_update(member_user)

                await create_user_settings_if_none(
                    member_user=member_user,
                    member_user_settings_repository=member_user_settings_repository,
                )

            validate_email(email=member_user.primary_email)
            await create_login_model(
                user_uuid=member_user.user_uuid,
                login_model=login_model,
                provider_user_info=provider_user_info,
                login_model_repository=login_google_repository,
                provider_id=user_google_id,
            )

            if action_type == SignInActionType.LINKED:
                await publish_account_linked(
                    user_uuid=member_user.user_uuid,
                    timestamp=datetime.now(timezone.utc),
                    new_email=provider_user_info["email"],
                    original_email=original_email,
                    message_broker_client=message_broker_client,
                )
            if action_type == SignInActionType.REGISTERED:
                await publish_user_registered(
                    user_uuid=member_user.user_uuid,
                    timestamp=datetime.now(timezone.utc),
                    message_broker_client=message_broker_client,
                )

            return member_user, action_type

        except (BadRequestException, InvalidPrivilegesException) as exception:
            # Just re-raise those because those are known exceptions which will be later wrapped into correct HTTP
            # response. Check `exception_handlers` in `exception_handlers.py`
            raise exception
        except Exception as e:
            logging.exception(f"Sign in with Google failed, exception: {e}")
            raise RuntimeException(message="Registration failed, please try again later.") from e

    @staticmethod
    async def update_user_in_database(
        google_user_id: str,
        user_uuid: UUID,
        user_data: str,
        provider_refresh_token,
        provider_scopes,
        member_user_oauth2_repository: MemberUserOAuth2Repository,
    ) -> bool:
        try:
            oauth2_data = await member_user_oauth2_repository.get_by_primary_key(
                user_uuid=user_uuid,
                provider=Provider.GOOGLE.value,
            )

            if not oauth2_data:
                oauth2_data = MemberUserOAuth2(
                    user_uuid=user_uuid,
                    provider=Provider.GOOGLE.value,
                    provider_user_id=google_user_id,
                    refresh_token=provider_refresh_token,
                    scope=provider_scopes,
                )
            oauth2_data.user_data = user_data
            oauth2_data.refresh_token = provider_refresh_token
            oauth2_data.scope = provider_scopes

            await member_user_oauth2_repository.insert_or_update(oauth2_data=oauth2_data)

            return True

        except Exception as error:
            logging.exception(f"Failed to update Google refresh token, exception: {error}")
            raise RuntimeException(message="Authenticating api access failed.") from error
