from typing import List, Optional

from services.base.application.database.models.sorts import Sort
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.models.list_inbox_messages_continuation_token import (
    ListInboxMessagesContinuationToken,
)


class ListInboxMessagesInputBoundary(BaseDataModel):
    limit: int
    sorts: Optional[List[Sort]] = None
    continuation_token: Optional[ListInboxMessagesContinuationToken] = None
    query: Optional[SingleDocumentTypeQuery] = None
