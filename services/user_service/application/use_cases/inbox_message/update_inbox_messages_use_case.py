from typing import List

from services.base.application.async_use_case_base import AsyncUse<PERSON>aseBase
from services.base.application.exceptions import InvalidPrivilegesException
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.schemas.identity import Identity
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.user_service.application.use_cases.inbox_message.models.update_inbox_message_input_boundary import (
    UpdateInboxMessagesInputBoundary,
)


class UpdateInboxMessagesUseCase(AsyncUseCaseBase):
    def __init__(self, inbox_message_repository: InboxMessageRepository):
        self._inbox_message_repository = inbox_message_repository

    async def execute_async(
        self,
        identity: Identity,
        input_boundary: UpdateInboxMessagesInputBoundary,
    ) -> List[InboxMessage]:
        message_ids = [msg.id for msg in input_boundary.messages]
        messages = await self._inbox_message_repository.search_by_id(ids=message_ids)

        for msg in messages:
            if msg.destination != identity:
                raise InvalidPrivilegesException(message="You need to be owner of all those messages")

        for input_mgs, stored_msg in zip(input_boundary.messages, messages):
            if input_mgs.status and input_mgs.status != stored_msg.status:
                stored_msg.status = input_mgs.status
            if input_mgs.is_urgent is not None and input_mgs.is_urgent != stored_msg.is_urgent:
                stored_msg.is_urgent = input_mgs.is_urgent

        return await self._inbox_message_repository.update(messages=messages)
