from typing import Optional, Sequence

from services.base.application.async_use_case_base import AsyncUse<PERSON>aseBase
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.schemas.identity import Identity
from services.base.domain.schemas.inbox.inbox_message import InboxMessage, InboxMessageFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.user_service.application.use_cases.inbox_message.models.list_inbox_messages_input_boundary import (
    ListInboxMessagesInputBoundary,
)


class ListInboxMessagesUseCase(AsyncUseCaseBase):
    def __init__(self, inbox_message_repository: InboxMessageRepository):
        self._inbox_message_repository = inbox_message_repository

    async def execute_async(
        self,
        identity: Identity,
        input_boundary: ListInboxMessagesInputBoundary,
    ) -> Sequence[InboxMessage]:
        single_query = self._create_query(identity=identity, single_document_query=input_boundary.query)

        search_result = await self._inbox_message_repository.search_by_query(
            size=input_boundary.limit,
            continuation_token=input_boundary.continuation_token,
            query=single_query,
            sorts=input_boundary.sorts,
        )

        return search_result.documents

    def _create_query(
        self,
        identity: Identity,
        single_document_query: Optional[SingleDocumentTypeQuery],
    ) -> SingleDocumentTypeQuery[InboxMessage]:
        bool_query_builder = BooleanQueryBuilder()

        # the user has to equal destination (so you are receiver)
        bool_query_builder.add_query(
            ValuesQuery(
                field_name=f"{InboxMessageFields.DESTINATION}.{DocumentLabels.ID}",
                values=[str(identity.id)],
            )
        )

        if single_document_query:
            bool_query_builder.add_query(single_document_query.query)

        return SingleDocumentTypeQuery[InboxMessage](
            query=bool_query_builder.build_and_query(), domain_type=InboxMessage
        )
