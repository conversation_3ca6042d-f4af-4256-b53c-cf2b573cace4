from typing import List
from uuid import uuid4

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.schemas.inbox.inbox_message import (
    InboxMessage,
)
from services.user_service.application.use_cases.inbox_message.models.create_inbox_messages_input_boundary import (
    CreateInboxMessagesInputBoundary,
)


class CreateInboxMessagesUseCase(AsyncUseCaseBase):
    def __init__(self, inbox_message_repository: InboxMessageRepository):
        self._inbox_message_repository = inbox_message_repository

    async def execute_async(
        self,
        input_boundary: CreateInboxMessagesInputBoundary,
    ) -> List[InboxMessage]:
        messages = [
            InboxMessage(
                sender=msg.sender,
                destination=msg.destination,
                message=msg.message,
                timestamp=msg.timestamp,
                title=msg.title,
                context=msg.context,
                is_urgent=msg.is_urgent,
                id=uuid4(),
            )
            for msg in input_boundary.messages
        ]
        return await self._inbox_message_repository.insert(messages=messages)
