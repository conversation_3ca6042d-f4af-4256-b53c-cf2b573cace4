from typing import List
from uuid import UUID

from services.base.application.exceptions import NoContentException
from services.base.application.use_case_base import Use<PERSON>aseBase
from services.base.domain.enums.notification import NotificationStatus
from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.domain.schemas.inbox.member_user_notification_inbox import MemberUserNotificationInbox


class NotificationInboxSetStatusUseCase(UseCaseBase):
    def __init__(self, notification_repository: NotificationInboxRepository):
        self._notification_repository = notification_repository

    async def execute(
        self,
        user_uuid: UUID,
        notification_uuids: List[str],
        status: NotificationStatus,
        **kwargs,
    ) -> List[MemberUserNotificationInbox]:
        notifications = await self._notification_repository.get_notifications_by_uuid(notification_uuids)
        valid_notifications = []
        # Check whether notifications to be read do belong to the user
        for notification in notifications:
            if notification.user_uuid == user_uuid:
                notification.status = status
                valid_notifications.append(notification)
        if not valid_notifications:
            raise NoContentException(message="No valid notifications were given.")
        return await self._notification_repository.upsert(notifications=valid_notifications)
