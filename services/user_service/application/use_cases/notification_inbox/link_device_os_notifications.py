from typing import List, Optional
from uuid import UUID

from services.base.application.exceptions import IncorrectOperationException
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.member_user.member_user_device import MemberUserDevice
from services.base.domain.schemas.shared import BaseDataModel


class UserFilterPolicyModel(BaseDataModel):
    user_uuid: List[UUID]


class LinkDeviceOSNotificationsUseCase:
    def __init__(
        self,
        user_repo: MemberUserRepository,
        member_user_device_repository: MemberUserDeviceRepository,
        push_notification_service: PushNotificationService,
    ):
        self._user_repo = user_repo
        self._member_user_device_repository = member_user_device_repository
        self._push_notification_service = push_notification_service

    async def execute(
        self,
        user_uuid: UUID,
        device_token: str,
        device_name: Optional[str] = None,
    ) -> Optional[MemberUserDevice]:
        # TODO: add some sort of device id to differentiate whether single device has multiple tokens stored
        if not await self._user_repo.get_by_uuid(user_uuid=user_uuid):
            raise IncorrectOperationException(message=f"User {user_uuid} does not exist.")
        if not self._push_notification_service.is_token_valid(token=device_token):
            raise IncorrectOperationException(message=f"Invalid device token {device_token}")
        return await self._member_user_device_repository.insert_or_update(
            member_user_device=MemberUserDevice(
                user_uuid=user_uuid,
                device_token=device_token,
                device_name=device_name,
            )
        )
