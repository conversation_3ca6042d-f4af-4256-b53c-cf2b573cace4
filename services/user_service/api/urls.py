from dataclasses import dataclass

from services.user_service.api.constants import (
    InboxMessageEndpointRoutes,
    NotificationInboxEndpointRoutes,
    UserEndpointRoutes,
    V01UserEndpointRoutes,
    V02SignInEndpointRoutes,
)
from services.user_service.api.inbox_message_endpoints import message_router
from services.user_service.api.notification_inbox_endpoints import notification_router
from services.user_service.api.v02_sign_in_endpoints import v02_sign_in_router
from services.user_service.api.v02_user_endpoints import v01_user_router
from services.user_service.api.v1.user_endpoints import user_router


@dataclass(frozen=True)
class UserEndpointUrls:
    ASSETS_CREDENTIALS = f"{user_router.prefix}{UserEndpointRoutes.ASSETS_CREDENTIALS}"
    EXPORT_TASKS = f"{user_router.prefix}{UserEndpointRoutes.EXPORT_TASKS}"


@dataclass(frozen=True)
class V02SignInEndpointUrls:
    ANONYMOUSLY = f"{v02_sign_in_router.prefix}{V02SignInEndpointRoutes.ANONYMOUSLY}"
    DELETE_USER = f"{v01_user_router.prefix}{V01UserEndpointRoutes.DELETE_USER}"
    USER_LOGS = f"{v01_user_router.prefix}{V01UserEndpointRoutes.LOGS}"


@dataclass(frozen=True)
class InboxMessageEndpointUrls:
    INBOX = f"{message_router.prefix}{InboxMessageEndpointRoutes.INBOX}"
    SEARCH = f"{message_router.prefix}{InboxMessageEndpointRoutes.SEARCH}"


@dataclass(frozen=True)
class NotificationInboxEndpointUrls:
    INBOX = f"{notification_router.prefix}{NotificationInboxEndpointRoutes.INBOX}"
