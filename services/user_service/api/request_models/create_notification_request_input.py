from typing import List

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.use_cases.notification_inbox.input_boundaries.create_notification_inbox_input_boundary import (
    CreateNotificationInbox,
)


class CreateNotificationRequestInputItem(CreateNotificationInbox):
    pass


class CreateNotificationRequestInput(BaseDataModel):
    values: List[CreateNotificationRequestInputItem] = Field(min_length=1)
