from __future__ import annotations

import json
import logging
from typing import Optional

from fastapi import Body, Query
from fastapi.exceptions import RequestValidationError
from pydantic import Field

from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.single_document_type_query_mapper import SingleDocumentTypeQueryMapper
from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.models.sorts import Sort
from services.base.application.utils.encoders import decode_base_64
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.models.list_inbox_messages_continuation_token import (
    ListInboxMessagesContinuationToken,
)
from services.user_service.application.use_cases.inbox_message.models.list_inbox_messages_input_boundary import (
    ListInboxMessagesInputBoundary,
)


class ListInboxMessagesRequestInput(BaseDataModel):
    sort: SortRequestInput = Field(default=SortRequestInput())
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    @staticmethod
    def to_input_boundary(
        request_input: ListInboxMessagesRequestInput = Body(...),
        limit: int = Query(default=100, gt=0, le=10000),
        continuation_token: Optional[str] = Query(default=None, min_length=1),
    ) -> ListInboxMessagesInputBoundary:
        query = (
            SingleDocumentTypeQueryMapper.map(query=request_input.query, domain_type=InboxMessage)
            if request_input.query
            else None
        )
        return ListInboxMessagesInputBoundary(
            limit=limit,
            continuation_token=request_input._decode_continuation_token(continuation_token),
            query=query,
            sorts=[Sort(name=request_input.sort.field_name, order=request_input.sort.order)],
        )

    @staticmethod
    def _decode_continuation_token(continuation_token: Optional[str]) -> ListInboxMessagesContinuationToken | None:
        # TODO add validation
        if continuation_token:
            try:
                return ListInboxMessagesContinuationToken(
                    **json.loads(decode_base_64(encoded_token=continuation_token))
                )
            except Exception as error:
                logging.exception(error)
                raise RequestValidationError("Unable to decode continuation token.")
        else:
            return None
