from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.user_service.api.constants import (
    UserEndpointRoutes,
    UserServicePrefixes,
)
from services.user_service.api.output_models.export_task_api_output import ExportTaskApiOutput
from services.user_service.api.response_models.assets_credentials_response import AssetsCredentialsResponse
from services.user_service.api.response_models.export_tasks_response import ExportTasksResponse
from services.user_service.application.use_cases.fetch_export_tasks_use_case import FetchUserExportTasksUseCase
from services.user_service.application.use_cases.user.generate_storage_credentials_use_case import (
    GenerateAssetsCredentialsUseCase,
)

user_router = APIRouter(
    prefix=f"{UserServicePrefixes.VERSION1_PREFIX}{UserServicePrefixes.USER_PREFIX}",
    tags=["user", "profile"],
    responses={404: {"description": "Not found"}},
)


@user_router.get(UserEndpointRoutes.ASSETS_CREDENTIALS)
async def assets_credentials_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: GenerateAssetsCredentialsUseCase = Injected(GenerateAssetsCredentialsUseCase),
) -> AssetsCredentialsResponse:
    """Returns serialised asset authorization credentials, which can be used as query params to query user assets"""
    output = await use_case.execute_async(user_uuid=user_uuid)
    return AssetsCredentialsResponse.map(model=output)


@user_router.get(UserEndpointRoutes.EXPORT_TASKS)
async def export_tasks_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    active_only: bool = Query(default=False),
    use_case: FetchUserExportTasksUseCase = Injected(FetchUserExportTasksUseCase),
) -> ExportTasksResponse:
    output_boundary = await use_case.execute_async(user_id=user_uuid, active_only=active_only)
    if not output_boundary:
        raise NoContentException("No export tasks found")
    return ExportTasksResponse(export_tasks=ExportTaskApiOutput.multi_map(models=output_boundary))
