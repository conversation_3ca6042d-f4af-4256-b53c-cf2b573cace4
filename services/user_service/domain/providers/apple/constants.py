from enum import StrEnum


class AppleIdTokenKeys(StrEnum):
    """Keys for Apple id_token (decoded from JW<PERSON> token)"""

    C_HASH = "c_hash"
    AUD = "aud"
    EMAIL = "email"
    EMAIL_VERIFIED = "email_verified"
    EXP = "exp"
    IAT = "iat"
    ISS = "iss"
    SUB = "sub"


class AppleUserObjectKeys(StrEnum):
    """Keys for Apple user object"""

    NAME = "name"
    FIRST_NAME = "firstName"
    LAST_NAME = "lastName"


class AppleUtilConstants(StrEnum):
    """Constants used with Apple utils"""

    KEYS_URL = "https://appleid.apple.com/auth/keys"
    DOMAIN = "https://appleid.apple.com/"


class AppleOAuthKeys(StrEnum):
    RESPONSE_MODE = "response_mode"
    NONCE = "nonce"
