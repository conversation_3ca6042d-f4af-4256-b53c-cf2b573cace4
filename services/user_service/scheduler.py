from datetime import time, timedelta

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from services.user_service.application.use_cases.plan.remind_plan_summary_workflow import RemindPlanSummaryWorkflow
from services.user_service.application.use_cases.plan.remind_plans_workflow import RemindPlansWorkflow
from services.user_service.application.use_cases.remind_user_activity_use_case import RemindUserActivityUseCase
from services.user_service.dependency_bootstrapper import DependencyB<PERSON>strapper
from settings.app_config import settings


class Scheduler:
    def __init__(self, bootstrapper: DependencyBootstrapper):
        self.bootstrapper = bootstrapper
        self.scheduler = AsyncIOScheduler()
        self.scheduler.add_job(
            func=self._remind_inactive_user,
            trigger="cron",
            hour="*",
            kwargs={"use_case": self.bootstrapper.get(interface=RemindUserActivityUseCase)},
        )
        self.scheduler.add_job(
            func=self._remind_plan_summary_workflow,
            trigger="cron",
            hour="*",
            kwargs={"workflow": self.bootstrapper.get(interface=RemindPlanSummaryWorkflow)},
        )
        self.scheduler.add_job(
            func=self._remind_plans_workflow,
            trigger="cron",
            minute="*/10",
            kwargs={"workflow": self.bootstrapper.get(interface=RemindPlansWorkflow)},
        )

    async def _remind_inactive_user(self, use_case: RemindUserActivityUseCase):
        await use_case.execute_async(
            local_time_of_triggering=time(hour=settings.REMIND_ACTIVITY_LOCAL_HOUR),
            staleness_period=timedelta(days=3),
        )

    async def _remind_plan_summary_workflow(self, workflow: RemindPlanSummaryWorkflow):
        await workflow.run(
            local_time_of_triggering=time(hour=settings.REMIND_PLANS_LOCAL_HOUR),
            active_considered_period=timedelta(days=settings.DAYS_CONSIDERED_ACTIVE),
        )

    async def _remind_plans_workflow(self, workflow: RemindPlansWorkflow):
        await workflow.run(
            tick=timedelta(minutes=10),
            active_considered_period=timedelta(days=settings.DAYS_CONSIDERED_ACTIVE),
        )

    def start(self):
        self.scheduler.start()

    def shutdown(self):
        self.scheduler.shutdown()
