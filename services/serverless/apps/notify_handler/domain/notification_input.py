from typing import Optional

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.notification import NotificationType
from services.base.domain.schemas.identity import Identity
from services.base.domain.schemas.inbox.context_models import MessageContext
from services.base.domain.schemas.shared import BaseDataModel


class NotificationInput(BaseDataModel):
    destination: Identity
    sender: Identity
    title: NonEmptyStr
    message: NonEmptyStr = Field(description="Message Inbox message, can be rich text")
    summary: NonEmptyStr = Field(description="Push notification message")
    description: Optional[NonEmptyStr] = None
    is_urgent: bool = False
    is_important: bool = False
    context: MessageContext
    # TODO(jaja): Delete with notifications
    category: NotificationType = NotificationType.LIFESTYLE
