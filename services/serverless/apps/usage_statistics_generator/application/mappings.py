from datetime import timedelta

from dateutil.relativedelta import relativedelta

from services.base.domain.enums.usage_statistics import ReportTimeframe

report_timeframe_to_word_type_mapping = {
    ReportTimeframe.DAILY: {"noun": "day", "adverb": "daily"},
    ReportTimeframe.WEEKLY: {"noun": "week", "adverb": "weekly"},
    ReportTimeframe.MONTHLY: {"noun": "month", "adverb": "monthly"},
    ReportTimeframe.QUARTERLY: {"noun": "quarter", "adverb": "quarterly"},
    ReportTimeframe.YEARLY: {"noun": "year", "adverb": "yearly"},
}

get_timedelta_from_report_timeframe = {
    ReportTimeframe.DAILY: timedelta(days=1),
    ReportTimeframe.WEEKLY: timedelta(days=7),
    ReportTimeframe.MONTHLY: relativedelta(months=1),
    ReportTimeframe.QUARTERLY: relativedelta(months=4),
    ReportTimeframe.YEARLY: relativedelta(months=12),
}

input_data_to_report_timeframe = {
    "daily": ReportTimeframe.DAILY,
    "weekly": ReportTimeframe.WEEKLY,
    "monthly": ReportTimeframe.MONTHLY,
}
