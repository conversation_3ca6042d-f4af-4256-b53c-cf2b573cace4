from typing import Self, Type

from injector import Injector
from opensearchpy import Async<PERSON><PERSON>Search
from slack_sdk.web.async_client import Async<PERSON>eb<PERSON>lient
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.usage_stats_repository import UsageStatsRepository
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_usage_stats_repository import OSUsageStatsRepository
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_device_repository import (
    SqlAlchMemberUserDeviceRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector import (
    StatsCollector,
)
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector_runner import (
    StatsCollectorRunner,
)
from services.serverless.apps.usage_statistics_generator.application.slack_bot.generate_cost_notification_report import (
    CostNotificationReporter,
)
from services.serverless.apps.usage_statistics_generator.application.slack_bot.slack_bot import SlackBot
from services.serverless.apps.usage_statistics_generator.application.usage_statistics_use_case import (
    UsageStatisticsUseCase,
)
from settings.app_config import settings
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: Type[T]) -> T:
        return self.injector.get(interface=interface)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_repositories()
        self._bind_use_cases()

        return self

    async def cleanup(self):
        await self.get(DeprEventRepository).close()

    def _bind_singleton[T](self, interface: Type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserDeviceRepository,
            to=SqlAlchMemberUserDeviceRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserDeviceRepository,
            to=SqlAlchMemberUserDeviceRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=UsageStatsRepository, to=OSUsageStatsRepository(client=self.get(AsyncOpenSearch))
        )

    def _bind_use_cases(self):
        self._bind_singleton(
            interface=StatsCollector,
            to=StatsCollector(
                member_user_repository=self.get(MemberUserRepository),
                device_user_repository=self.get(MemberUserDeviceRepository),
                client=self.get(interface=AsyncOpenSearch),
            ),
        )
        self._bind_singleton(
            interface=StatsCollectorRunner,
            to=StatsCollectorRunner(
                collector=self.get(StatsCollector),
                search_service=self.get(DocumentSearchService),
            ),
        )
        self._bind_singleton(
            interface=AsyncWebClient,
            to=AsyncWebClient(
                token=secrets.COST_NOTIFICATION_BOT_TOKEN,
            ),
        )
        self._bind_singleton(
            interface=SlackBot,
            to=SlackBot(
                client=self.get(AsyncWebClient),
            ),
        )
        self._bind_singleton(
            interface=CostNotificationReporter,
            to=CostNotificationReporter(
                slack_bot=self.get(SlackBot),
            ),
        )
        self._bind_singleton(
            interface=UsageStatisticsUseCase,
            to=UsageStatisticsUseCase(
                collector_runner=self.get(StatsCollectorRunner),
                cost_notification_reporter=self.get(CostNotificationReporter),
                usage_stats_repository=self.get(UsageStatsRepository),
            ),
        )
