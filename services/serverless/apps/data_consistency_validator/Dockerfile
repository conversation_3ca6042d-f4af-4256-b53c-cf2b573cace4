FROM public.ecr.aws/lambda/python:3.13 AS data_consistency_validator
RUN dnf install gcc -y

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1

ENV IS_CONTAINERIZED true
ENV APP_VERSION $APP_VERSION
ENV BUILD_VERSION $BUILD_VERSION
ARG RUN_ENV

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv
COPY /services/serverless/apps/data_consistency_validator/requirements.txt  .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY settings ${LAMBDA_TASK_ROOT}/settings
COPY services/base ${LAMBDA_TASK_ROOT}/services/base
COPY /services/serverless/apps/data_consistency_validator ${LAMBDA_TASK_ROOT}/services/serverless/apps/data_consistency_validator

# Assert that the entrypoint can be executed by the Python runtime with all necessary requirements via imports
RUN PYTHONPATH=${LAMBDA_TASK_ROOT} python3 services/serverless/apps/data_consistency_validator/api.py

CMD ["services.serverless.apps.data_consistency_validator.api.handler"]
