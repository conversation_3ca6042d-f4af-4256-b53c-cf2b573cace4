from opensearchpy import Async<PERSON>penSearch

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.use_cases.cleanup_missing_references_use_case import CleanupMissingReferencesUseCase
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_aggregation_service import OSAggregationService
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_plan_repository import OSPlanRepository
from services.base.infrastructure.database.opensearch.repository.os_template_repository import OSTemplateRepository
from settings.app_config import settings

_async_opensearch_client = AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
_document_client = OpenSearchClient(client=_async_opensearch_client)
_search_service = OSDocumentSearchService(client=_document_client)
_template_repo = OSTemplateRepository(client=_async_opensearch_client, search_service=_search_service)
_plan_repo = OSPlanRepository(client=_async_opensearch_client, search_service=_search_service)
_agg_service = OSAggregationService(client=_async_opensearch_client)
_cleanup_uc = CleanupMissingReferencesUseCase(
    client=_async_opensearch_client,
    search_service=_search_service,
    template_repo=_template_repo,
    plan_repo=_plan_repo,
    agg_service=_agg_service,
)


def get_search_service() -> DocumentSearchService:
    return _search_service


def get_cleanup_uc() -> CleanupMissingReferencesUseCase:
    return _cleanup_uc
