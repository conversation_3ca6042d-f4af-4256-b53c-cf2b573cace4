FROM python:3.13-slim-bookworm as extensions_test_handler

RUN rm -f /etc/apt/apt.conf.d/docker-clean
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,sharing=locked,target=/var/lib/apt \
    apt-get update && \
    apt-get dist-upgrade -yq && \
    apt-get install -yq \
    python3-dev \
    build-essential \
    curl \
    tidy
RUN rm -rf /var/lib/apt/lists/*

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1

ENV PYTHONPATH ..
ENV APP_VERSION $APP_VERSION
ENV IS_CONTAINERIZED true
ENV BUILD_VERSION $BUILD_VERSION
ARG RUN_ENV

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv

COPY ./requirements-dev.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements-dev.txt

COPY /services/serverless/apps/alexa_voice_log/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/analytics_scheduler/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/data_consistency_validator/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/notify_handler/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/single_correlation_app/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/trend_insights/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

COPY /services/serverless/apps/usage_statistics_generator/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt


COPY Makefile Makefile
COPY pyproject.toml pyproject.toml
COPY settings settings
COPY services/base services/base
COPY services/serverless/apps services/serverless/apps
COPY services/serverless/base services/serverless/base
# Trend detector no longer touches data service usecases but correlator might investigation needed
COPY services/data_service services/data_service


# Assert that the entrypoint can be executed by the Python runtime with all necessary requirements via imports
RUN python services/serverless/apps/alexa_voice_log/api.py
RUN python services/serverless/apps/analytics_scheduler/api.py
RUN python services/serverless/apps/data_consistency_validator/api.py
RUN python services/serverless/apps/notify_handler/api.py
RUN python services/serverless/apps/single_correlation_app/api.py
RUN python services/serverless/apps/trend_insights/api.py
RUN python services/serverless/apps/usage_statistics_generator/api.py
