from typing import Self

from httpx import AsyncClient
from injector import Injector
from opensearchpy import Async<PERSON>penSearch
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.io.httpclient import HttpClient
from services.base.application.voice.voice_service import VoiceService
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.login_google_repository import LoginGoogleRepository
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_plan_repository import OSPlanRepository
from services.base.infrastructure.database.opensearch.repository.os_template_repository import OSTemplateRepository
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_login_google_repository import (
    SqlAlchLoginGoogleRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_oauth2_repository import (
    SqlAlchMemberUserOAuth2Repository,
)
from services.serverless.apps.alexa_voice_log.application.complete_plan_use_case import CompletePlanUseCase
from services.serverless.apps.alexa_voice_log.application.log_event_use_case import LogEventUseCase
from services.serverless.apps.alexa_voice_log.infrastructure.alexa_voice_service import AlexaVoiceService
from settings.app_config import settings
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: type[T]) -> T:
        return self.injector.get(interface=interface)

    def _bind_singleton[T](self, interface: type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_repositories()
        self._bind_services()
        self._bind_use_cases()
        return self

    async def cleanup(self):
        await self.get(DeprEventRepository).close()

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )
        self._bind_singleton(interface=AsyncClient, to=AsyncClient())
        self._bind_singleton(interface=HttpClient, to=HttpClient(client=self.get(interface=AsyncClient)))

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=TemplateRepository,
            to=OSTemplateRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=PlanRepository,
            to=OSPlanRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=LoginGoogleRepository,
            to=SqlAlchLoginGoogleRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserOAuth2Repository,
            to=SqlAlchMemberUserOAuth2Repository(session_maker=self.get(interface=async_sessionmaker)),
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=VoiceService,
            to=AlexaVoiceService(
                login_google_repository=self.get(LoginGoogleRepository),
                member_user_oauth2_repository=self.get(MemberUserOAuth2Repository),
            ),
        )

    def _bind_use_cases(self):
        self._bind_singleton(
            interface=LogEventUseCase,
            to=LogEventUseCase(
                event_repo=self.get(interface=EventRepository),
                search_service=self.get(interface=DocumentSearchService),
                template_repo=self.get(interface=TemplateRepository),
            ),
        )

        self._bind_singleton(
            interface=CompletePlanUseCase,
            to=CompletePlanUseCase(
                search_service=self.get(interface=DocumentSearchService),
                http_client=self.get(interface=HttpClient),
            ),
        )
