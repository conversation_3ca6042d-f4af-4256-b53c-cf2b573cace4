import logging

from ask_sdk_core.skill_builder import Skill<PERSON>uilder

from services.base.application.voice.voice_service import VoiceService
from services.base.telemetry.lambda_telemetry_instrumentor import LambdaTelemetryInstrumentor
from services.serverless.apps.alexa_voice_log.application.log_event_use_case import LogEventUseCase
from services.serverless.apps.alexa_voice_log.dependency_bootstrapper import DependencyBootstrapper
from services.serverless.apps.alexa_voice_log.handlers.cancel_or_stop_intent_handler import (
    CancelOrStopIntentHandler,
)
from services.serverless.apps.alexa_voice_log.handlers.exception_handler import ExceptionHandler
from services.serverless.apps.alexa_voice_log.handlers.fallback_intent_handler import FallbackIntentHandler
from services.serverless.apps.alexa_voice_log.handlers.help_intent_handler import HelpIntentHandler
from services.serverless.apps.alexa_voice_log.handlers.intent_reflector_handler import Intent<PERSON>eflector<PERSON>andler
from services.serverless.apps.alexa_voice_log.handlers.launch_request_handler import LaunchRe<PERSON><PERSON>andler
from services.serverless.apps.alexa_voice_log.handlers.log_event_handler import LogEventHandler
from services.serverless.apps.alexa_voice_log.handlers.session_ended_request_handler import (
    SessionEndedRequestHandler,
)
from settings.app_config import settings
from settings.app_secrets import secrets

LambdaTelemetryInstrumentor.initialize(service_name="alexa_voice_log", settings=settings, secrets=secrets)


logging.info("starting alexa best life log app")
bootstrapper = DependencyBootstrapper().build()

sb = SkillBuilder()
sb.add_request_handler(CancelOrStopIntentHandler())
sb.add_request_handler(FallbackIntentHandler())
sb.add_request_handler(HelpIntentHandler())
sb.add_request_handler(LaunchRequestHandler())
sb.add_request_handler(SessionEndedRequestHandler())
sb.add_request_handler(
    LogEventHandler(
        log_event_use_case=bootstrapper.get(LogEventUseCase),
        voice_interaction_service=bootstrapper.get(VoiceService),
    )
)
sb.add_request_handler(IntentReflectorHandler())  # Has to be last

sb.add_exception_handler(ExceptionHandler())

lambda_handler = sb.lambda_handler()
handler = lambda_handler
