import logging
from typing import Union

from ask_sdk_core.dispatch_components.exception_components import Abstract<PERSON>x<PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_model.response import Response

from services.serverless.apps.alexa_voice_log.domain.phrases import SOMETHING_WENT_WRONG_PHRASE


class ExceptionHandler(AbstractExceptionHandler):
    """Fallback for all wrong usages of the skill. Generic exception handler."""

    def can_handle(self, handler_input: HandlerInput, exception: Exception) -> bool:
        return True

    def handle(self, handler_input: HandlerInput, exception: Exception) -> Union[None, Response]:
        logging.exception(f"Did not get a valid response. Instead got: {repr(exception)}")
        return (
            handler_input.response_builder.speak(SOMETHING_WENT_WRONG_PHRASE).ask(SOMETHING_WENT_WRONG_PHRASE).response
        )
