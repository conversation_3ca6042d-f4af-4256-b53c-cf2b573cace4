from typing import Union

from ask_sdk_core.dispatch_components.request_components import <PERSON>bstract<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_core.utils.predicate import is_request_type
from ask_sdk_model.response import Response

from services.serverless.apps.alexa_voice_log.domain.request_types import RequestType


class SessionEndedRequestHandler(AbstractRequestHandler):
    """Handler for Session End."""

    def can_handle(self, handler_input: HandlerInput) -> bool:
        return is_request_type(RequestType.SESSION_ENDED_REQUEST)(handler_input)

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        return handler_input.response_builder.response
