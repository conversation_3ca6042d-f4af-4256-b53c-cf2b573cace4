import random
from datetime import datetime, timezone
from uuid import uuid4

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin
from services.base.tests.domain.builders.plan_builder import PlanBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.serverless.apps.alexa_voice_log.application.complete_plan_use_case import CompletePlanUseCase
from services.serverless.apps.alexa_voice_log.application.voice_exceptions import NoPlansFoundException
from services.serverless.apps.alexa_voice_log.tests.integration.application.voice_test_helpers import (
    _VOICE_TEST_WORDS,
    VoiceTestHelpers,
)


class TestCompletePlanUseCase:

    @pytest.fixture(scope="class")
    async def user_with_plans(self, template_repo, plan_repo):
        owner_id = uuid4()
        randon_input_origin = PrimitiveTypesGenerator.generate_random_enum(InsertableOrigin)
        templates_to_insert = (
            EventTemplateBuilder().with_origin(randon_input_origin).with_owner_id(owner_id=owner_id).build_n()
        )

        trigger_template = (
            EventTemplateBuilder()
            .with_origin(randon_input_origin)
            .with_owner_id(owner_id=owner_id)
            .with_name(random.choice(seq=_VOICE_TEST_WORDS))
            .with_archived_at(archived_at=False)
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=[*templates_to_insert, trigger_template], force_strong_consistency=True
        )

        plans_to_insert = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=owner_id).build()
            for t in templates_to_insert
        ]
        trigger_plan = (
            PlanBuilder()
            .with_template_id(trigger_template.id)
            .with_name(trigger_template.name)
            .with_archived_at(False)
            .with_owner_id(owner_id)
            .with_is_confirmation_required(True)
            .build()
        )
        inserted_plans = await plan_repo.insert(plans=[*plans_to_insert, trigger_plan], force_strong_consistency=True)

        yield owner_id, trigger_plan

        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    async def test_complete_event_use_case_completes_event_template_plan_passes(
        self, bootstrapper, user_with_plans, event_repo
    ):
        # Arrange
        owner_id, plan = user_with_plans
        uc = bootstrapper.get(CompletePlanUseCase)
        now = datetime.now(timezone.utc).replace(microsecond=0)

        # Act
        completed_plan = await uc.execute_async(
            prompt=VoiceTestHelpers.malform_word(word=plan.name),
            now=now,
            user_id=owner_id,
        )

        # Assert
        assert completed_plan.name == plan.name
        for e in completed_plan.events:
            assert e.timestamp == now

        await event_repo.delete_by_id(ids=[e.id for e in completed_plan.events])

    @pytest.fixture(scope="class")
    async def user_with_group_template_plan(self, template_repo, plan_repo):
        owner_id = uuid4()

        random_origin = PrimitiveTypesGenerator.generate_random_enum(InsertableOrigin)
        templates = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_origin(random_origin)
            .with_archived_at(archived_at=False)
            .build_n()
        )

        trigger_group_template = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_archived_at(archived_at=False)
            .with_template_ids(template_ids=[t.id for t in templates])
            .with_name(random.choice(seq=_VOICE_TEST_WORDS))
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=[*templates, trigger_group_template], force_strong_consistency=True
        )
        plans_to_insert = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=owner_id).build() for t in templates
        ]
        trigger_plan = (
            PlanBuilder()
            .with_template_id(trigger_group_template.id)
            .with_name(trigger_group_template.name)
            .with_archived_at(False)
            .with_owner_id(owner_id)
            .with_is_confirmation_required(True)
            .build()
        )
        inserted_plans = await plan_repo.insert(plans=[*plans_to_insert, trigger_plan], force_strong_consistency=True)

        yield owner_id, trigger_plan

        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    async def test_complete_plan_use_case_group_template_plan_passes(
        self, bootstrapper, user_with_group_template_plan, event_repo
    ):
        # Arrange
        owner_id, plan = user_with_group_template_plan
        log_event_use_case = bootstrapper.get(CompletePlanUseCase)
        now = datetime.now(timezone.utc).replace(microsecond=0)

        # Act
        completed_plan = await log_event_use_case.execute_async(
            prompt=VoiceTestHelpers.malform_word(word=plan.name),
            now=now,
            user_id=owner_id,
        )

        # Assert
        assert completed_plan.name == plan.name
        for e in completed_plan.events:
            assert e.timestamp == now

        await event_repo.delete_by_id(ids=[e.id for e in completed_plan.events])

    async def test_complete_plan_use_case_no_plan_found_raises(self, bootstrapper, user_with_plans):
        # Arrange
        owner_id, _ = user_with_plans
        log_event_use_case = bootstrapper.get(CompletePlanUseCase)
        event_input = PrimitiveTypesGenerator.generate_random_string(min_length=1, max_length=10)

        timestamp = datetime.now(timezone.utc).replace(microsecond=0)

        # Act & Assert
        with pytest.raises(NoPlansFoundException):
            await log_event_use_case.execute_async(
                prompt=event_input,
                now=timestamp,
                user_id=owner_id,
            )

    @pytest.fixture(scope="class")
    async def user_with_malformed_templates(self, bootstrapper, template_repo, plan_repo):
        owner_id = uuid4()
        templates = EventTemplateBuilder().with_owner_id(owner_id=owner_id).build_n()

        name, malformed_names = random.choice(
            (
                ("mucinex decongestant nasal spray", ("nasal spray", "mucinex spray", "decongestant", "spray")),
                ("multi test", ("test", "multi", "mult tes")),
            )
        )
        trigger_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_archived_at(archived_at=False)
            .with_name(name=name)
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=[*templates, trigger_template], force_strong_consistency=True
        )

        plans_to_insert = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=owner_id).build() for t in templates
        ]
        trigger_plan = (
            PlanBuilder()
            .with_template_id(trigger_template.id)
            .with_name(trigger_template.name)
            .with_archived_at(False)
            .with_owner_id(owner_id)
            .with_is_confirmation_required(True)
            .build()
        )
        inserted_plans = await plan_repo.insert(plans=[*plans_to_insert, trigger_plan], force_strong_consistency=True)

        yield owner_id, trigger_plan, malformed_names

        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    async def test_log_event_use_case_lookup_malformed_templates_passes(
        self, bootstrapper, user_with_malformed_templates, event_repo
    ):
        # Arrange
        owner_id, plan, malformed_names = user_with_malformed_templates
        uc = bootstrapper.get(CompletePlanUseCase)

        for word in malformed_names:
            # Act
            found_plan = await uc._lookup_plan(
                prompt=word,
                owner_id=owner_id,
            )
            # Assert
            assert found_plan.id == plan.id

    @pytest.fixture(scope="class")
    async def user_with_similar_plans(self, bootstrapper, template_repo, plan_repo):
        owner_id = uuid4()
        name, similar_names = random.choice(
            (
                ("turkey stick", ("flea tick", "turkey meat", "turkish visit", "clock tick", "stick")),
                ("piece of cake", ("bake a cake", "take a break", "fake snake", "lake wake", "stake shake")),
            )
        )

        # To make things harder, also malform few similar words
        similar_names = list(similar_names) + [
            VoiceTestHelpers.malform_word(word=random.choice(similar_names)) for _ in range(10)
        ]

        similar_templates = [
            EventTemplateBuilder().with_owner_id(owner_id=owner_id).with_name(name=name).build()
            for name in similar_names
        ]
        trigger_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_archived_at(archived_at=False)
            .with_name(name=name)
            .build()
        )
        inserted_templates = await template_repo.insert(
            templates=[*similar_templates, trigger_template], force_strong_consistency=True
        )

        similar_plans = [
            PlanBuilder().with_template_id(template_id=t.id).with_owner_id(owner_id=owner_id).build()
            for t in similar_templates
        ]
        trigger_plan = (
            PlanBuilder()
            .with_template_id(trigger_template.id)
            .with_name(trigger_template.name)
            .with_archived_at(False)
            .with_owner_id(owner_id)
            .with_is_confirmation_required(True)
            .build()
        )
        inserted_plans = await plan_repo.insert(plans=[*similar_plans, trigger_plan], force_strong_consistency=True)

        yield owner_id, trigger_plan

        await plan_repo.delete_by_id(ids=[p.id for p in inserted_plans])
        await template_repo.delete_by_id(ids=[t.id for t in inserted_templates])

    async def test_log_event_use_case_lookup_expected_in_similar_passes(
        self, bootstrapper, user_with_similar_plans, event_repo
    ):
        # Arrange
        owner_id, plan = user_with_similar_plans
        uc = bootstrapper.get(CompletePlanUseCase)

        # Act
        found_plan = await uc._lookup_plan(
            prompt=plan.name,
            owner_id=owner_id,
        )
        # Assert
        assert found_plan.id == plan.id
