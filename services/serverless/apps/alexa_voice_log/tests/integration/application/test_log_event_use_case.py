import random
from datetime import datetime, timezone
from uuid import uuid4

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.serverless.apps.alexa_voice_log.application.log_event_use_case import LogEventUseCase
from services.serverless.apps.alexa_voice_log.application.voice_exceptions import NoTemplatesFoundException
from services.serverless.apps.alexa_voice_log.tests.integration.application.voice_test_helpers import (
    _VOICE_TEST_WORDS,
    VoiceTestHelpers,
)


class TestLogEventUseCase:

    @pytest.fixture(scope="class")
    async def user_with_event_templates(self, template_repo):
        owner_id = uuid4()
        templates = EventTemplateBuilder().with_owner_id(owner_id=owner_id).build_n()

        single_word_template = (
            EventTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_name(random.choice(seq=_VOICE_TEST_WORDS))
            .build()
        )
        templates = [*templates, single_word_template]
        _ = await template_repo.insert(templates=templates, force_strong_consistency=True)
        yield owner_id, single_word_template

        await template_repo.delete_by_id(ids=[t.id for t in templates])

    async def test_log_event_use_case_should_log_from_event_template_passes(
        self, bootstrapper, user_with_event_templates, event_repo
    ):
        # Arrange
        owner_id, template = user_with_event_templates
        log_event_use_case = bootstrapper.get(LogEventUseCase)
        now = datetime.now(timezone.utc).replace(microsecond=0)

        # Act
        stored_events = await log_event_use_case.execute_async(
            prompt=VoiceTestHelpers.malform_word(word=template.name),
            timestamp=now,
            owner_id=owner_id,
        )

        stored_event = stored_events[0]

        # Assert
        assert len(stored_events) == 1
        assert stored_event.name == template.document.name
        assert stored_event.type_id() == template.document.type == template.document_type
        assert stored_event.timestamp == now
        assert stored_event.metadata.origin == Origin.LLIF
        assert stored_event.metadata.source_service == SourceService.AMAZON_ALEXA
        assert stored_event.metadata.origin_device is None
        assert stored_event.metadata.source_os == SourceOS.UNKNOWN
        assert stored_event.metadata.service == Service.VOICE
        assert stored_event.template_id == template.id
        await event_repo.delete_by_id(ids=[stored_event.id])

    @pytest.fixture(scope="class")
    async def user_with_group_templates(self, template_repo):
        owner_id = uuid4()
        event_templates = EventTemplateBuilder().with_owner_id(owner_id=owner_id).build_n()

        group_template = (
            GroupTemplateBuilder()
            .with_owner_id(owner_id=owner_id)
            .with_template_ids(template_ids=[t.id for t in event_templates])
            .with_name(random.choice(seq=_VOICE_TEST_WORDS))
            .build()
        )
        templates = [*event_templates, group_template]
        _ = await template_repo.insert(templates=templates, force_strong_consistency=True)
        yield owner_id, group_template, event_templates

        await template_repo.delete_by_id(ids=[t.id for t in templates])

    async def test_log_event_use_case_should_log_from_group_template_passes(
        self, bootstrapper, user_with_group_templates, event_repo
    ):
        # Arrange
        owner_id, group_template, event_templates = user_with_group_templates
        log_event_use_case = bootstrapper.get(LogEventUseCase)
        now = datetime.now(timezone.utc).replace(microsecond=0)

        # Act
        stored_events = await log_event_use_case.execute_async(
            prompt=VoiceTestHelpers.malform_word(word=group_template.name),
            timestamp=now,
            owner_id=owner_id,
        )

        # Assert
        assert len(stored_events) == len(group_template.template_ids)
        for stored_event, event_template in zip(
            sorted(stored_events, key=lambda e: e.template_id), sorted(event_templates, key=lambda t: t.id)
        ):
            assert stored_event.name == event_template.document.name
            assert stored_event.type_id() == event_template.document.type == event_template.document_type
            assert stored_event.timestamp == now
            assert stored_event.metadata.origin == Origin.LLIF
            assert stored_event.metadata.source_service == SourceService.AMAZON_ALEXA
            assert stored_event.metadata.service == Service.VOICE
            assert stored_event.metadata.source_os == SourceOS.UNKNOWN
            assert stored_event.metadata.origin_device is None
            assert stored_event.template_id == event_template.id

        await event_repo.delete_by_id(ids=[e.id for e in stored_events])

    async def test_log_event_use_case_no_template_found_raises(self, bootstrapper, user_with_event_templates):
        # Arrange
        owner_id, _ = user_with_event_templates
        log_event_use_case = bootstrapper.get(LogEventUseCase)
        event_input = PrimitiveTypesGenerator.generate_random_string(min_length=1, max_length=10)

        timestamp = datetime.now(timezone.utc).replace(microsecond=0)

        # Act & Assert
        with pytest.raises(NoTemplatesFoundException):
            await log_event_use_case.execute_async(
                prompt=event_input,
                timestamp=timestamp,
                owner_id=owner_id,
            )

    @pytest.fixture(scope="class")
    async def user_with_malformed_templates(self, bootstrapper, template_repo):
        owner_id = uuid4()
        templates = EventTemplateBuilder().with_owner_id(owner_id=owner_id).build_n()

        template_name, malformed_template_names = random.choice(
            (
                ("mucinex decongestant nasal spray", ("nasal spray", "mucinex spray", "decongestant", "spray")),
                ("multi test", ("test", "multi", "mult tes")),
            )
        )
        multi_word_template = (
            EventTemplateBuilder().with_owner_id(owner_id=owner_id).with_name(name=template_name).build()
        )
        templates = [*templates, multi_word_template]

        _ = await template_repo.insert(templates=templates, force_strong_consistency=True)
        yield owner_id, multi_word_template, malformed_template_names

        await template_repo.delete_by_id(ids=[t.id for t in templates])

    async def test_log_event_use_case_lookup_malformed_templates_passes(
        self, bootstrapper, user_with_malformed_templates, event_repo
    ):
        # Arrange
        owner_id, template, malformed_names = user_with_malformed_templates
        log_event_use_case = bootstrapper.get(LogEventUseCase)

        for word in malformed_names:
            # Act
            found_template = await log_event_use_case._lookup_template(
                prompt=word,
                owner_id=owner_id,
            )
            # Assert
            assert found_template.id == template.id

    @pytest.fixture(scope="class")
    async def user_with_similar_templates(self, bootstrapper, template_repo):
        owner_id = uuid4()
        templates = EventTemplateBuilder().with_owner_id(owner_id=owner_id).build_n()

        template_name, similar_template_names = random.choice(
            (
                ("turkey stick", ("flea tick", "turkey meat", "turkish visit", "clock tick", "stick")),
                ("piece of cake", ("bake a cake", "take a break", "fake snake", "lake wake", "stake shake")),
            )
        )

        # To make things harder, also malform few similar words
        similar_template_names = list(similar_template_names) + [
            VoiceTestHelpers.malform_word(word=random.choice(similar_template_names)) for _ in range(10)
        ]

        similar_templates = [
            EventTemplateBuilder().with_owner_id(owner_id=owner_id).with_name(name=name).build()
            for name in similar_template_names
        ]

        expected_template = (
            EventTemplateBuilder().with_owner_id(owner_id=owner_id).with_name(name=template_name).build()
        )

        templates = [*templates, *similar_templates, expected_template]
        _ = await template_repo.insert(templates=templates, force_strong_consistency=True)
        yield owner_id, expected_template

        await template_repo.delete_by_id(ids=[t.id for t in templates])

    async def test_log_event_use_case_lookup_expected_in_similar_passes(
        self, bootstrapper, user_with_similar_templates, event_repo
    ):
        # Arrange
        owner_id, template = user_with_similar_templates
        log_event_use_case = bootstrapper.get(LogEventUseCase)

        # Act
        found_template = await log_event_use_case._lookup_template(
            prompt=template.name,
            owner_id=owner_id,
        )
        # Assert
        assert found_template.id == template.id
