import asyncio

import pytest

from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.serverless.apps.alexa_voice_log.dependency_bootstrapper import DependencyBootstrapper


@pytest.fixture(scope="session")
def bootstrapper() -> DependencyBootstrapper:
    return DependencyBootstrapper().build()


@pytest.fixture(scope="session")
def event_repo(bootstrapper) -> EventRepository:
    return bootstrapper.get(interface=EventRepository)


@pytest.fixture(scope="session")
def template_repo(bootstrapper) -> TemplateRepository:
    return bootstrapper.get(interface=TemplateRepository)


@pytest.fixture(scope="session")
def plan_repo(bootstrapper) -> PlanRepository:
    return bootstrapper.get(interface=PlanRepository)


# Needs to be added, otherwise it seems that multiple AsyncClients share the same event loop
@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
