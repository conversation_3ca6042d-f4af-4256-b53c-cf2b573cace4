from typing import Self, Type

from aioboto3 import Session
from injector import Injector
from opensearchpy import Async<PERSON><PERSON><PERSON>earch
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.async_message_broker_client import Async<PERSON>essageBrokerClient
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.infrastructure.aws.async_sns_wrapper import Async<PERSON><PERSON><PERSON>rapper
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearch<PERSON>lient
from services.base.infrastructure.database.opensearch.os_aggregation_service import OSAggregationService
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_extension_result_repository import (
    OSExtensionResultRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_extension_run_repository import (
    OSExtensionRunRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_settings_repository import (
    SqlAlchMemberUserSettingsRepository,
)
from services.serverless.apps.single_correlation_app.app.correlation_analysis_runner import CorrelationAnalysisRunner
from services.serverless.apps.single_correlation_app.app.correlation_preparer import CorrelationPreparer
from services.serverless.apps.single_correlation_app.app.event_to_event_correlation_analyzer import (
    EventToEventCorrelationAnalyzer,
)
from services.serverless.apps.single_correlation_app.app.single_correlation_use_case import SingleCorrelationUseCase
from settings.app_config import settings
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: Type[T]) -> T:
        return self.injector.get(interface=interface)

    def _bind_singleton[T](self, interface: Type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_services()
        self._bind_repositories()
        self._bind_use_cases()
        return self

    async def cleanup(self):
        await self.get(DeprEventRepository).close()

    def _bind_infrastructure(self):
        self._bind_singleton(interface=AsyncMessageBrokerClient, to=AsyncSNSWrapper(session=self.get(Session)))
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ExtensionRunRepository, to=OSExtensionRunRepository(client=self.get(AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ExtensionResultRepository,
            to=OSExtensionResultRepository(
                client=self.get(AsyncOpenSearch), extension_run_repository=self.get(interface=ExtensionRunRepository)
            ),
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=MemberUserSettingsRepository,
            to=SqlAlchMemberUserSettingsRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=AggregationService, to=OSAggregationService(client=self.get(interface=AsyncOpenSearch))
        )

    def _bind_use_cases(self):
        self._bind_singleton(
            interface=EventToEventCorrelationAnalyzer,
            to=EventToEventCorrelationAnalyzer(search_service=self.get(interface=DocumentSearchService)),
        )
        self._bind_singleton(
            interface=CorrelationPreparer,
            to=CorrelationPreparer(
                search_service=self.get(interface=DocumentSearchService),
                agg_service=self.get(interface=AggregationService),
            ),
        )
        self._bind_singleton(
            interface=CorrelationAnalysisRunner,
            to=CorrelationAnalysisRunner(
                search_service=self.get(interface=DocumentSearchService),
                corr_analyzer=self.get(interface=EventToEventCorrelationAnalyzer),
            ),
        )
        self._bind_singleton(
            interface=SingleCorrelationUseCase,
            to=SingleCorrelationUseCase(
                search_service=self.get(interface=DocumentSearchService),
                extension_run_repo=self.get(interface=ExtensionRunRepository),
                extension_result_repo=self.get(interface=ExtensionResultRepository),
                message_broker_client=self.get(interface=AsyncMessageBrokerClient),
                corr_analysis_runner=self.get(interface=CorrelationAnalysisRunner),
                agg_service=self.get(interface=AggregationService),
            ),
        )
