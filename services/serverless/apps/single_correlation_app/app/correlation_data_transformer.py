import random
from bisect import bisect_left, bisect_right
from datetime import datetime, timedelta, timezone
from typing import Sequence

import pandas as pd


class CorrelationDataTransformer:
    @staticmethod
    def create_negative_outcome_timestamps(
        outcome_timestamps: Sequence[datetime], active_days: Sequence[datetime]
    ) -> list[datetime]:
        """
        Generates negative events to outcome to allow for correlations calculation. Picks a date at random that is
        not included in the timestamps list and picks a time that is close to a time included in the original list.
        1)Initializes the output list,
        2)Creates a set of unique days in which the outcome occurred,
        3)Loops through users active days,
        4)For each day not found in unique days generates a random timestamp to correspond with a negative outcome event,
        5)Returns the list of these timestamps.
        """
        # Initialize a list to store the new timestamps
        new_timestamps = []
        # Create a set of unique days
        outcome_days = set(timestamp.date() for timestamp in outcome_timestamps)
        for day in [d.date() for d in active_days]:
            if day not in outcome_days:
                # Shuffle the existing timestamps to pick a random time of day
                time = (random.choice(outcome_timestamps) + timedelta(minutes=random.randint(-100, 100))).time()
                new_datetime = datetime.combine(day, time, tzinfo=timezone.utc)
                new_timestamps.append(new_datetime)

        return new_timestamps

    @staticmethod
    def create_df_from_timestamps(
        positive_outcomes: Sequence[datetime],
        negative_outcomes: Sequence[datetime],
        triggers: Sequence[datetime],
        top_boundary: timedelta,
        low_boundary: timedelta,
    ) -> pd.DataFrame:
        """
        Combines the negative and positive outcomes timestamps lists and generates a Pandas
        DataFrame object, with a row for each outcome timestamp and trigger count for how many trigger
        objects are within the specified boundary.
        1) Combines the two outcomes lists,
        2) Creates the empty DataFrame,
        3) Converts the triggers datetime list to a list of datetimes
        4) Loops through outcome timestamps and counts the number of triggers inside the boundary
        5) Creates a new column in the DataFrame to store these trigger counts
        6) Adds the Outcome value column that represents positive (1) and a negative (0) event
        7) Sorts and returns the DataFrame
        """
        outcomes = [*positive_outcomes, *negative_outcomes]
        # Create an empty DataFrame to store the results
        df = pd.DataFrame({"Outcome Timestamp": outcomes})

        # Convert triggers to datetime
        triggers = [pd.to_datetime(trigger) for trigger in triggers]

        # Define a function to count triggers within the boundary
        def count_triggers(outcome_timestamp: datetime) -> int:
            start_time = outcome_timestamp - top_boundary
            end_time = outcome_timestamp - low_boundary
            left_index = bisect_left(triggers, start_time)
            right_index = bisect_right(triggers, end_time)
            return right_index - left_index

        # Calculate trigger counts for each outcome timestamp
        df["Trigger Count"] = df["Outcome Timestamp"].apply(count_triggers)

        # Add the "Outcome Value" column (1 for positive, -1 for negative)
        df["Outcome Value"] = [1] * len(positive_outcomes) + [-1] * len(negative_outcomes)

        # Sort the DataFrame by the 'Outcome Timestamp' column
        df.sort_values(by="Outcome Timestamp", inplace=True)

        return df
