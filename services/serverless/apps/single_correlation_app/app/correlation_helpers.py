from typing import Sequence

from services.base.domain.annotated_types import RoundedFloat
from services.serverless.apps.single_correlation_app.app.enums.enums import (
    ResultImplication,
)
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    CorrelationResultsSummary,
    EligibleCorrelationInput,
    OutcomeTriggersObject,
    SingleOutcomeCorrelationResult,
)


def parse_corr_inputs_into_extension_input_output_boundary(
    outcome_triggers_input: Sequence[OutcomeTriggersObject],
) -> EligibleCorrelationInput:
    outcomes = {outcome_trigger.outcome.name for outcome_trigger in outcome_triggers_input}
    triggers = {trigger.name for outcome_trigger in outcome_triggers_input for trigger in outcome_trigger.triggers}

    return EligibleCorrelationInput(
        eligible_outcome_events=list(outcomes) or None,
        eligible_triggers_events=list(triggers) or None,
    )


def get_correlation_output_summary_from_results(results: list[SingleOutcomeCorrelationResult]):
    summary = CorrelationResultsSummary()
    if not results:
        return summary
    for result in results:
        for trigger_correlation_results in result.single_correlation_results:
            res_implication = trigger_correlation_results.result_implication
            summary.very_weak_count += 1 if res_implication == ResultImplication.VERY_WEAK.value else 0
            summary.weak_count += 1 if res_implication == ResultImplication.WEAK.value else 0
            summary.moderate_count += 1 if res_implication == ResultImplication.MODERATE.value else 0
            summary.strong_count += 1 if res_implication == ResultImplication.STRONG.value else 0
            summary.very_strong_count += 1 if res_implication == ResultImplication.VERY_STRONG.value else 0

    return summary


def get_result_implication_from_correlation(correlation: RoundedFloat):
    if 0 == abs(correlation):
        return ResultImplication.NO_CORRELATION.value
    if 0 < abs(correlation) <= 0.2:
        return ResultImplication.VERY_WEAK.value
    if 0.2 < abs(correlation) <= 0.4:
        return ResultImplication.WEAK.value
    if 0.4 < abs(correlation) <= 0.6:
        return ResultImplication.MODERATE.value
    if 0.6 < abs(correlation) <= 0.8:
        return ResultImplication.STRONG.value
    if 0.8 < abs(correlation) <= 1:
        return ResultImplication.VERY_STRONG.value


def get_message_from_result(
    pre_message: str, summary: CorrelationResultsSummary, results: list[SingleOutcomeCorrelationResult]
) -> str:
    summary_paragraph = (
        f"<p>Summary: Very Strong - {summary.very_strong_count}, Strong - {summary.strong_count}, Moderate - "
        f"{summary.moderate_count}, Weak - {summary.weak_count}, Very Weak - {summary.very_weak_count}</p>"
    )

    outcome_results = []
    for count, result in enumerate(results):
        outcome_name = result.outcome_name
        for v in result.single_correlation_results[:3]:
            outcome_results.append((outcome_name, v))
        if count == 2:
            break

    outcome_results = sorted(outcome_results, key=lambda x: x[1].max_correlation, reverse=True)[:3]

    top_correlation_paragraph = "<p>Highest found correlation: <br>"
    for outcome_name, v in outcome_results:
        top_correlation_paragraph += f"{outcome_name} to {v.trigger_name} - {v.max_correlation}<br>"
    top_correlation_paragraph += "</p>"

    return pre_message + summary_paragraph + top_correlation_paragraph
