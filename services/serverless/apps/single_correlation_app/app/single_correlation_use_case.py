import logging
from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID, uuid4

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.event_models.analytics_completed_event_model import AnalyticsCompletedEventModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.messaging import ATT_NAME_ANALYTICS_EVENT, MessageTopics
from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import AnalyticMetadata, <PERSON><PERSON><PERSON><PERSON>, ExtensionRun
from services.base.domain.schemas.query.aggregations import AggregationMethod, DateHistogramAggregation
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.leaf_query import RangeQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.message_queue.utils import create_string_message_attribute
from services.serverless.apps.single_correlation_app.app.boundaries.single_correlation_input_boundary import (
    CorrelationInputBoundary,
)
from services.serverless.apps.single_correlation_app.app.constants.messages import SingleCorrelationResultMessages
from services.serverless.apps.single_correlation_app.app.correlation_analysis_runner import (
    CorrelationAnalysisRunner,
)
from services.serverless.apps.single_correlation_app.app.correlation_helpers import (
    get_correlation_output_summary_from_results,
    get_message_from_result,
    parse_corr_inputs_into_extension_input_output_boundary,
)
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    CorrelationOutputModel,
    EligibleCorrelationInput,
    SingleOutcomeCorrelationResult,
)


class SingleCorrelationUseCase:
    def __init__(
        self,
        search_service: DocumentSearchService,
        extension_run_repo: ExtensionRunRepository,
        extension_result_repo: ExtensionResultRepository,
        message_broker_client: AsyncMessageBrokerClient,
        corr_analysis_runner: CorrelationAnalysisRunner,
        agg_service: AggregationService,
    ):
        self._search_service = search_service
        self._extension_run_repo = extension_run_repo
        self._extension_result_repo = extension_result_repo
        self._message_broker_client = message_broker_client
        self._corr_analysis_runner = corr_analysis_runner
        self._agg_service = agg_service

    async def execute_async(
        self, input_boundary: CorrelationInputBoundary
    ) -> tuple[ExtensionRun, Sequence[ExtensionResult]]:
        stored_results = []
        analytic_output = await self._evaluate_correlations(input_boundary=input_boundary)

        stored_run: ExtensionRun = (
            await self._extension_run_repo.insert(
                extension_runs=[analytic_output.correlation_run_document], force_strong_consistency=True
            )
        )[0]
        if analytic_output.correlation_result_documents:
            stored_results = await self._extension_result_repo.insert(
                extension_results=analytic_output.correlation_result_documents,
                parent_id=stored_run.id,
                force_strong_consistency=True,
            )
        await self._publish(
            user_id=input_boundary.user_id,
            doc_id=stored_run.id,
            extension_id=input_boundary.extension_id,
            analytic_output=analytic_output,
        )

        return stored_run, stored_results

    async def _publish(self, user_id: UUID, doc_id: UUID, extension_id: UUID, analytic_output: CorrelationOutputModel):
        result_status = analytic_output.correlation_run_document.result_status
        await self._message_broker_client.publish_topic(
            topic_name=MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
            message_body=AnalyticsCompletedEventModel(
                doc_id=doc_id,
                user_uuid=user_id,
                timestamp=datetime.now(timezone.utc),
                extension_id=extension_id,
                title=(
                    SingleCorrelationResultMessages.RUN_WITH_OUTPUT_TITLE
                    if result_status
                    else SingleCorrelationResultMessages.RUN_WITHOUT_OUTPUT_TITLE
                ),
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_ANALYTICS_EVENT, MessageTopics.TOPIC_ANALYTIC_FINISHED.value
            ),
        )

    async def _evaluate_correlations(self, input_boundary: CorrelationInputBoundary) -> CorrelationOutputModel:
        """
        Loops through all outcome triggers objects and appends their results
        """
        timestamp_at_start = datetime.now(timezone.utc)
        if not input_boundary.extension_input.outcome_triggers_input:
            run_document = ExtensionRun(
                timestamp=timestamp_at_start,
                extension_input=EligibleCorrelationInput(
                    eligible_outcome_events=None,
                    eligible_triggers_events=None,
                ).model_dump(),
                metadata=AnalyticMetadata(
                    extension_id=input_boundary.extension_id,
                    user_uuid=input_boundary.user_id,
                    provider_id=input_boundary.provider_id,
                    urgent=input_boundary.extension_input.should_notify,
                    important=input_boundary.extension_input.should_notify,
                ),
                type=DataType.ExtensionRun,
                message=SingleCorrelationResultMessages.RUN_WITHOUT_OUTPUT_MESSAGE,
                summary=SingleCorrelationResultMessages.RUN_WITHOUT_OUTPUT_SUMMARY,
                extension_status=ExtensionStatus.SUCCEEDED,
                result_status=False,
                id=uuid4(),
            )
            return CorrelationOutputModel(
                correlation_run_document=run_document,
                correlation_result_documents=[],
            )

        # Parse input boundary into extension inputs output boundary
        extension_input_output_boundary = parse_corr_inputs_into_extension_input_output_boundary(
            outcome_triggers_input=input_boundary.extension_input.outcome_triggers_input
        )

        single_correlation_results_list = []
        # TODO Re-think
        # Fetch all documents into single storage DataFrame and run correlations on subsets of the DataFrame
        # instead of fetching and processing them individually
        document_cache = {}
        active_days = await self._find_active_days(
            start_time=None,
            end_time=datetime.now(timezone.utc),
            user_id=input_boundary.user_id,
        )
        for outcome_triggers_object in input_boundary.extension_input.outcome_triggers_input:
            logging.info(f"Processing outcome trigger: {outcome_triggers_object}")
            single_correlation_output: SingleOutcomeCorrelationResult = await self._corr_analysis_runner.run(
                outcome=outcome_triggers_object.outcome,
                triggers=outcome_triggers_object.triggers,
                user_id=input_boundary.user_id,
                historical_offset=input_boundary.extension_input.historical_offset,
                document_cache=document_cache,
                active_days=active_days,
            )
            if single_correlation_output.single_correlation_results:
                single_correlation_results_list.append(single_correlation_output)

        single_correlation_results_list.sort(
            key=lambda result: result.single_correlation_results[0].max_correlation, reverse=True
        )

        summary = get_correlation_output_summary_from_results(results=single_correlation_results_list)

        message = get_message_from_result(
            pre_message=SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE,
            summary=summary,
            results=single_correlation_results_list,
        )

        correlation_result_documents_list = [
            ExtensionResult(
                output=result.model_dump_json(),
                timestamp=timestamp_at_start,
                metadata=AnalyticMetadata(
                    extension_id=input_boundary.extension_id,
                    user_uuid=input_boundary.user_id,
                    provider_id=input_boundary.provider_id,
                    urgent=input_boundary.extension_input.should_notify,
                    important=input_boundary.extension_input.should_notify,
                ),
                type=DataType.ExtensionResult,
                result_status=True,
                id=uuid4(),
            )
            for result in single_correlation_results_list
        ]

        run_document = ExtensionRun(
            timestamp=timestamp_at_start,
            run_summary=summary.json(),
            extension_input=extension_input_output_boundary.model_dump(),
            metadata=AnalyticMetadata(
                extension_id=input_boundary.extension_id,
                user_uuid=input_boundary.user_id,
                provider_id=input_boundary.provider_id,
                urgent=input_boundary.extension_input.should_notify,
                important=input_boundary.extension_input.should_notify,
            ),
            type=DataType.ExtensionRun,
            message=message,
            summary=SingleCorrelationResultMessages.RUN_WITH_OUTPUT_SUMMARY,
            extension_status=ExtensionStatus.SUCCEEDED,
            result_status=True,
            id=uuid4(),
        )

        return CorrelationOutputModel(
            correlation_run_document=run_document,
            correlation_result_documents=correlation_result_documents_list,
        )

    async def _find_active_days(
        self,
        start_time: datetime | None,
        end_time: datetime | None,
        user_id: UUID,
    ) -> Sequence[datetime]:
        """Find active time buckets within the given time range"""
        query = Query(
            type_queries=[
                TypeQuery(
                    domain_types=[event_type.to_event_model() for event_type in EventV3Type],
                    query=RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=start_time, lte=end_time),
                )
            ]
        )
        query = CommonQueryAdjustments.add_user_uuid_to_query(user_uuid=user_id, query=query)
        aggs = await self._agg_service.date_histogram_by_query(
            query=query,
            aggregation=DateHistogramAggregation(
                default_aggregation_method=AggregationMethod.SUM,
                histogram_field_aggregations=[],
                interval="1d",
            ),
        )
        return [agg.timestamp for agg in aggs if agg.doc_count > 0]
