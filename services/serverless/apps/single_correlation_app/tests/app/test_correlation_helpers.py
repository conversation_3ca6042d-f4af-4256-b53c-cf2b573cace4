import random

import pytest

from services.base.domain.enums.event_v3_type import EventV3Type
from services.serverless.apps.single_correlation_app.app.builders.single_correlation_builders import (
    CorrelationResultsBuilder,
    CorrelationResultsSummaryBuilder,
)
from services.serverless.apps.single_correlation_app.app.constants.messages import SingleCorrelationResultMessages
from services.serverless.apps.single_correlation_app.app.correlation_helpers import (
    get_message_from_result,
    parse_corr_inputs_into_extension_input_output_boundary,
)
from services.serverless.apps.single_correlation_app.app.enums.enums import (
    CorrelationConfidence,
    CorrelationInputTypes,
)
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    CorrelationResultsSummary,
    EligibleCorrelationInput,
    EventCorrelationResults,
    OutcomeTriggersObject,
    SingleCorrelationSingleInput,
    SingleCorrelationSingleOutput,
    SingleOutcomeCorrelationResult,
)


@pytest.mark.parametrize(
    "outcome_triggers_input,expected_outcome",
    [
        (
            [
                OutcomeTriggersObject(
                    outcome=SingleCorrelationSingleInput(
                        event_type=EventV3Type.Symptom,
                        input_type=CorrelationInputTypes.OUTCOME,
                        name="Symptom1",
                        confidence=CorrelationConfidence.HIGH_CONFIDENCE,
                    ),
                    triggers=[
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger1",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger2",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                    ],
                )
            ],
            EligibleCorrelationInput(
                eligible_outcome_events=["Symptom1"], eligible_triggers_events=["Trigger1", "Trigger2"]
            ),
        ),
        (
            [
                OutcomeTriggersObject(
                    outcome=SingleCorrelationSingleInput(
                        event_type=EventV3Type.Symptom,
                        input_type=CorrelationInputTypes.OUTCOME,
                        name="Symptom1",
                        confidence=CorrelationConfidence.HIGH_CONFIDENCE,
                    ),
                    triggers=[
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger1",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger2",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                    ],
                ),
                OutcomeTriggersObject(
                    outcome=SingleCorrelationSingleInput(
                        event_type=EventV3Type.Symptom,
                        input_type=CorrelationInputTypes.OUTCOME,
                        name="Symptom2",
                        confidence=CorrelationConfidence.HIGH_CONFIDENCE,
                    ),
                    triggers=[
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger1",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                        SingleCorrelationSingleInput(
                            event_type=EventV3Type.Symptom,
                            input_type=CorrelationInputTypes.TRIGGER,
                            name="Trigger2",
                            confidence=CorrelationConfidence.LOW_CONFIDENCE,
                        ),
                    ],
                ),
            ],
            EligibleCorrelationInput(
                eligible_outcome_events=["Symptom1", "Symptom2"], eligible_triggers_events=["Trigger1", "Trigger2"]
            ),
        ),
    ],
)
async def test_parse_corr_inputs_into_extension_input_output_boundary(
    outcome_triggers_input: list[OutcomeTriggersObject], expected_outcome: EligibleCorrelationInput
):
    output_boundary = parse_corr_inputs_into_extension_input_output_boundary(
        outcome_triggers_input=outcome_triggers_input
    )

    eligible_outcome_events = output_boundary.eligible_outcome_events or []
    eligible_triggers_events = output_boundary.eligible_triggers_events or []

    expected_outcome_events = expected_outcome.eligible_outcome_events or []
    expected_triggers_events = expected_outcome.eligible_triggers_events or []

    assert set(expected_outcome_events) == set(eligible_outcome_events)
    assert set(expected_triggers_events) == set(eligible_triggers_events)


test_cases = [
    (
        CorrelationResultsSummary(
            very_weak_count=2,
            weak_count=3,
            moderate_count=1,
            strong_count=4,
            very_strong_count=5,
        ),
        [
            SingleOutcomeCorrelationResult(
                outcome_name="outcome1",
                outcome_document_count=100,
                single_correlation_results=[
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger1",
                        trigger_document_count=200,
                        max_correlation=0.9,
                        correlation=EventCorrelationResults(
                            immediate_term=0.9, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger2",
                        trigger_document_count=250,
                        max_correlation=0.7,
                        correlation=EventCorrelationResults(
                            immediate_term=0.9, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger3",
                        trigger_document_count=180,
                        max_correlation=0.6,
                        correlation=EventCorrelationResults(
                            immediate_term=0.6, short_term=0.5, medium_term=0.6, long_term=0.6
                        ),
                    ),
                ],
            )
        ],
        SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE
        + "<p>Summary: Very Strong - 5, Strong - 4, Moderate - 1, "
        "Weak - 3, Very Weak - 2</p><p>Highest found correlation: <br>outcome1 to trigger1 - 0.9<br>outcome1 to "
        "trigger2 - 0.7<br>outcome1 to trigger3 - 0.6<br></p>",
    ),
    (
        CorrelationResultsSummary(
            very_weak_count=2,
            weak_count=3,
            moderate_count=1,
            strong_count=4,
            very_strong_count=5,
        ),
        [
            SingleOutcomeCorrelationResult(
                outcome_name="outcome1",
                outcome_document_count=100,
                single_correlation_results=[
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger1",
                        trigger_document_count=200,
                        max_correlation=0.9,
                        correlation=EventCorrelationResults(
                            immediate_term=0.9, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger2",
                        trigger_document_count=250,
                        max_correlation=0.7,
                        correlation=EventCorrelationResults(
                            immediate_term=0.9, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger3",
                        trigger_document_count=180,
                        max_correlation=0.6,
                        correlation=EventCorrelationResults(
                            immediate_term=0.6, short_term=0.5, medium_term=0.6, long_term=0.6
                        ),
                    ),
                ],
            ),
            SingleOutcomeCorrelationResult(
                outcome_name="outcome2",
                outcome_document_count=100,
                single_correlation_results=[
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger1",
                        trigger_document_count=200,
                        max_correlation=0.8,
                        correlation=EventCorrelationResults(
                            immediate_term=0.8, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger2",
                        trigger_document_count=250,
                        max_correlation=0.4,
                        correlation=EventCorrelationResults(
                            immediate_term=0.2, short_term=0.4, medium_term=0.3, long_term=0.3
                        ),
                    ),
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger3",
                        trigger_document_count=180,
                        max_correlation=0.3,
                        correlation=EventCorrelationResults(
                            immediate_term=0.3, short_term=0.2, medium_term=0.2, long_term=0.2
                        ),
                    ),
                ],
            ),
        ],
        SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE
        + "<p>Summary: Very Strong - 5, Strong - 4, Moderate - 1, "
        "Weak - 3, Very Weak - 2</p><p>Highest found correlation: <br>outcome1 to trigger1 - 0.9<br>outcome2 to "
        "trigger1 - 0.8<br>outcome1 to trigger2 - 0.7<br></p>",
    ),
    (
        CorrelationResultsSummary(
            very_weak_count=2,
            weak_count=3,
            moderate_count=1,
            strong_count=4,
            very_strong_count=5,
        ),
        [
            SingleOutcomeCorrelationResult(
                outcome_name="outcome1",
                outcome_document_count=100,
                single_correlation_results=[
                    SingleCorrelationSingleOutput(
                        trigger_name="trigger1",
                        trigger_document_count=200,
                        max_correlation=0.9,
                        correlation=EventCorrelationResults(
                            immediate_term=0.9, short_term=0.7, medium_term=0.6, long_term=0.6
                        ),
                    ),
                ],
            )
        ],
        SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE
        + "<p>Summary: Very Strong - 5, Strong - 4, Moderate - 1, "
        "Weak - 3, Very Weak - 2</p><p>Highest found correlation: <br>outcome1 to trigger1 - 0.9<br></p>",
    ),
]


@pytest.mark.parametrize("summary, results, expected_output", test_cases)
def test_get_message_from_result(summary, results, expected_output):
    pre_message = SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE
    generated_message = get_message_from_result(pre_message=pre_message, results=results, summary=summary)
    assert generated_message == expected_output


@pytest.mark.parametrize(
    "results, summary",
    [(CorrelationResultsBuilder().build_n(random.randint(1, 10)), CorrelationResultsSummaryBuilder().build())],
)
def test_dynamic_input_get_message_from_result(results, summary):
    pre_message = SingleCorrelationResultMessages.RUN_WITH_OUTPUT_MESSAGE

    summary_paragraph = (
        f"<p>Summary: Very Strong - {summary.very_strong_count}, Strong - {summary.strong_count}, Moderate - "
        f"{summary.moderate_count}, Weak - {summary.weak_count}, Very Weak - {summary.very_weak_count}</p>"
    )

    outcome_results = []
    for count, result in enumerate(results):
        outcome_name = result.outcome_name
        for v in result.single_correlation_results[:3]:
            outcome_results.append((outcome_name, v))
        if count == 2:
            break

    outcome_results = sorted(outcome_results, key=lambda x: x[1].max_correlation, reverse=True)[:3]

    top_correlation_paragraph = "<p>Highest found correlation: <br>"
    for outcome_name, v in outcome_results:
        top_correlation_paragraph += f"{outcome_name} to {v.trigger_name} - {v.max_correlation}<br>"
    top_correlation_paragraph += "</p>"

    expected_message = pre_message + summary_paragraph + top_correlation_paragraph

    generated_message = get_message_from_result(pre_message=pre_message, results=results, summary=summary)

    assert expected_message == generated_message
