# from datetime import datetime, timezone
# from uuid import uuid4
#
# import pytest
# from temporalio.client import Client
#
# from services.base.application.boundaries.documents import ExtensionResultOutputBoundary, SearchDocumentsOutputBoundary
# from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
# from services.base.domain.constants.document_labels import Document<PERSON>abels
# from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
# from services.base.domain.constants.messaging import MessageQueuesNames
# from services.base.domain.enums.analytics.extension_output import ExtensionStatus
# from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
# from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
# from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
# from services.base.domain.schemas.member_user.member_user import MemberUser
# from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
# from services.base.domain.schemas.query.leaf_query import ValuesQuery
# from services.base.domain.schemas.query.query import Query
# from services.base.domain.schemas.query.type_query import TypeQuery
# from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
#     SingleOutcomeCorrelationResult,
# )
# from services.serverless.apps.single_correlation_app.single_correlation_workflow import SingleCorrelationWorkflow
# from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID
# from settings.temporal_config import temporal_settings
#
#
# @pytest.mark.skip(
#     reason="Temporal Server and Worker not implemented correctly yet in test PRV. Will be fixed in the future."
# )
# async def test_single_correlation_temporal_workflow(user_with_random_event_data: MemberUser, dependency_bootstrapper):
#     analytics_scheduled_model = AnalyticsScheduledEventModel(
#         provider_id=uuid4(),
#         extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
#         user_uuid=user_with_random_event_data.user_uuid,
#         timestamp=datetime.now(timezone.utc),
#     )
#
#     analytics_scheduled_model_json_dump = analytics_scheduled_model.model_dump_json()
#
#     client = await Client.connect(f"{temporal_settings.TEMPORAL_HOST}:{temporal_settings.TEMPORAL_PORT}")
#     handle = await client.start_workflow(
#         SingleCorrelationWorkflow.run,
#         analytics_scheduled_model_json_dump,
#         id=f"single-correlation-{uuid4()}-{analytics_scheduled_model.user_uuid}",
#         task_queue=MessageQueuesNames.TEMPORAL_EXTENSION_QUEUE,
#     )
#
#     stored_run, stored_results = await handle.result()  # pyright: ignore
#
#     # asserts
#     stored_run = ExtensionRun(**stored_run)  # pyright: ignore
#     assert stored_run
#     assert stored_run.extension_status == ExtensionStatus.SUCCEEDED
#     assert stored_results
#     stored_results = [ExtensionResult(**result_dict) for result_dict in stored_results]  # pyright: ignore
#
#     # Re-fetch the results back from database
#     query = (
#         BooleanQueryBuilder()
#         .add_queries(
#             queries=[
#                 ValuesQuery(
#                     field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}",
#                     values=[str(user_with_random_event_data.user_uuid)],
#                 ),
#                 (
#                     ValuesQuery(
#                         field_name=f"{DocumentLabels.METADATA}.{ExtensionLabels.EXTENSION_ID}",
#                         values=[str(SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID)],
#                     )
#                 ),
#             ]
#         )
#         .build_and_query()
#     )
#     query = Query(type_queries=[TypeQuery(domain_types=[ExtensionRun], query=query)])
#
#     run_search_response: SearchDocumentsOutputBoundary[ExtensionRun] = await dependency_bootstrapper.get(
#         interface=ExtensionRunRepository
#     ).search_by_query(
#         size=1,
#         query=query,
#     )
#     returned_run = run_search_response.results[0].document
#     # Validate Run document
#     assert returned_run == stored_run
#     searched_results = await dependency_bootstrapper.get(interface=ExtensionResultRepository).get_all_children(
#         parent_id=returned_run.id
#     )
#     assert searched_results
#     # Validate Result documents
#     for i, searched_result in enumerate(searched_results):
#         searched_result: ExtensionResultOutputBoundary
#         assert searched_result.id == stored_results[i].id
#         assert SingleOutcomeCorrelationResult(**searched_result.output)
