import logging
import random
from collections import defaultdict
from typing import Any, AsyncGenerator, Awaitable, Callable, Sequence

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.base.type_resolver import TypeResolver


class TestCorrelationPreparer:
    def _group_events_by_name_and_type(self, events: Sequence[Event]) -> dict[tuple[Any, str], int]:
        """
        Groups events by their event_type and name, counting occurrences.
        This simulates the frequency distribution aggregation to get counts needed for length checks.
        """
        counts = defaultdict(int)
        for event in events:
            counts[(event.type, event.name)] += 1  # pyright: ignore
        return counts

    @pytest.fixture
    async def user_with_outcome_and_trigger_events(
        self,
        event_repository: EventRepository,
        user_factory: Callable[[], Awaitable[MemberUser]],
        request,  # pytest's fixture 'request' object
    ) -> AsyncGenerator[tuple[MemberUser, Sequence[Event]], None]:
        # Unpack the two boolean parameters from request.param
        generate_symptoms, generate_generics = request.param
        user = await user_factory()

        events = []

        if generate_symptoms:
            logging.info("Generating symptom events for user %s", user.user_uuid)
            for i in range(random.randint(5, 10)):
                events.extend(
                    SymptomBuilder()
                    .with_name(PrimitiveTypesGenerator.generate_random_string())
                    .with_owner_id(user.user_uuid)
                    .build_n(random.randint(20, 35))
                )
            if generate_generics:
                logging.info("Generating generic events for user %s", user.user_uuid)
                for i in range(random.randint(10, 20)):
                    builder_type = random.choice(TypeResolver.EVENT_BUILDERS)
                    events.extend(
                        builder_type()
                        .with_name(PrimitiveTypesGenerator.generate_random_string())
                        .with_owner_id(user.user_uuid)
                        .build_n(random.randint(20, 35))
                    )

        inserted_events = await event_repository.insert(events=events, force_strong_consistency=True)

        yield user, inserted_events

        # teardown
        if inserted_events:
            await event_repository.delete_by_id(ids=[e.id for e in inserted_events])

    @pytest.mark.parametrize(
        "user_with_outcome_and_trigger_events",
        [
            (False, False),  # No events
            (True, False),  # Only symptom events
            (True, True),  # All events (symptoms and generics)
        ],
        ids=["no_events", "symptom_only", "all_events"],
        indirect=True,
    )
    async def test_corr_preparer_user_with_outcome_and_trigger(
        self, user_with_outcome_and_trigger_events, corr_preparer
    ):
        user, inserted_events = user_with_outcome_and_trigger_events

        outcome_triggers = await corr_preparer.get_user_outcome_triggers(user_id=user.user_uuid)

        event_counts = self._group_events_by_name_and_type(inserted_events)

        expected_outcome_candidates = []
        for (event_type, name), count in event_counts.items():
            if event_type == EventV3Type.Symptom and (count > 30 or count > 10):
                expected_outcome_candidates.append((event_type, name))

        expected_trigger_candidates = []
        for (event_type, name), count in event_counts.items():
            if count > 10:
                expected_trigger_candidates.append((event_type, name))

        expected_final_outcome_triggers_count = 0
        for outcome_candidate_type, outcome_candidate_name in expected_outcome_candidates:
            current_outcome_triggers_count = 0
            for trigger_candidate_type, trigger_candidate_name in expected_trigger_candidates:
                if not (
                    trigger_candidate_type == outcome_candidate_type
                    and trigger_candidate_name == outcome_candidate_name
                ):
                    current_outcome_triggers_count += 1

            if current_outcome_triggers_count > 0:
                expected_final_outcome_triggers_count += 1

        assert isinstance(outcome_triggers, list), "outcome_triggers should be a list"

        assert (
            len(outcome_triggers) == expected_final_outcome_triggers_count
        ), f"Expected {expected_final_outcome_triggers_count} outcome trigger objects, but got {len(outcome_triggers)}"

        for i, outcome_trigger_obj in enumerate(outcome_triggers):
            assert hasattr(outcome_trigger_obj, "outcome") and hasattr(
                outcome_trigger_obj, "triggers"
            ), f"OutcomeTriggersObject at index {i} is malformed, missing 'outcome' or 'triggers' attribute."
            assert isinstance(
                outcome_trigger_obj.triggers, list
            ), f"Triggers for outcome '{outcome_trigger_obj.outcome.name}' at index {i} should be a list."

            current_outcome_key = (outcome_trigger_obj.outcome.event_type, outcome_trigger_obj.outcome.name)
            expected_triggers_for_this_outcome = 0
            for trigger_candidate_type, trigger_candidate_name in expected_trigger_candidates:
                if not (
                    trigger_candidate_type == current_outcome_key[0]
                    and trigger_candidate_name == current_outcome_key[1]
                ):
                    expected_triggers_for_this_outcome += 1

            assert len(outcome_trigger_obj.triggers) == expected_triggers_for_this_outcome, (
                f"Trigger count mismatch for outcome '{outcome_trigger_obj.outcome.name}' at index {i}: "
                f"Expected {expected_triggers_for_this_outcome} triggers, but got {len(outcome_trigger_obj.triggers)}"
            )
