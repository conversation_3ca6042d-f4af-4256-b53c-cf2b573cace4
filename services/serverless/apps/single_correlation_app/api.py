import asyncio
import logging

from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
from services.base.telemetry.lambda_telemetry_instrumentor import LambdaTelemetryInstrumentor
from services.serverless.apps.single_correlation_app.app.boundaries.single_correlation_input_boundary import (
    CorrelationInputBoundary,
)
from services.serverless.apps.single_correlation_app.app.correlation_preparer import CorrelationPreparer
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import CorrelationInput
from services.serverless.apps.single_correlation_app.app.single_correlation_use_case import SingleCorrelationUseCase
from services.serverless.apps.single_correlation_app.dependency_bootstrapper import (
    DependencyBootstrapper,
)
from services.serverless.base.parsers.parse_lambda_triggers import parse_lambda_triggers
from settings.app_config import settings
from settings.app_secrets import secrets

LambdaTelemetryInstrumentor.initialize(service_name="single_correlation_app", settings=settings, secrets=secrets)


logging.info("starting single correlation app")
bootstrapper = DependencyBootstrapper().build()


async def handle_async(models: list[AnalyticsScheduledEventModel]):
    preparer = bootstrapper.get(CorrelationPreparer)
    uc = bootstrapper.get(SingleCorrelationUseCase)
    for model in models:
        user_outcome_triggers = await preparer.get_user_outcome_triggers(user_id=model.user_uuid)
        if user_outcome_triggers:
            await uc.execute_async(
                input_boundary=CorrelationInputBoundary(
                    extension_id=model.extension_id,
                    provider_id=model.provider_id,
                    user_id=model.user_uuid,
                    extension_input=CorrelationInput(outcome_triggers_input=user_outcome_triggers, should_notify=True),
                )
            )


def handler(event, _):
    records = event.get("Records", [])
    logging.info(f"single correlation app started with records: {records}")

    if not records:
        logging.error(f"Notify handler invoked without expected payload, invocation event: {event}")
        return
    payloads = parse_lambda_triggers(records=records)
    logging.info(f"Event models: {payloads}")

    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()

    loop.run_until_complete(
        handle_async(models=[AnalyticsScheduledEventModel(**payload.payload) for payload in payloads])
    )
