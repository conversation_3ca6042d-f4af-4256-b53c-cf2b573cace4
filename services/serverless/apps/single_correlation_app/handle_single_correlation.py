# from temporalio import activity
#
# from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
# from services.serverless.apps.single_correlation_app.app.boundaries.single_correlation_input_boundary import (
#     CorrelationInputBoundary,
# )
# from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
#     CorrelationInput,
# )
#
#
# @activity.defn
# async def handle_single_correlation(model_input: str):
#     logger = activity.logger
#     logger.info("Starting handle_single_correlation activity")
#
#     from services.serverless.apps.single_correlation_app.app.correlation_preparer import CorrelationPreparer
#     from services.serverless.apps.single_correlation_app.app.single_correlation_use_case import (
#         SingleCorrelationUseCase,
#     )
#     from services.serverless.apps.single_correlation_app.dependency_bootstrapper import (
#         DependencyBootstrapper,
#     )
#
#     model = AnalyticsScheduledEventModel.model_validate_json(model_input)
#     logger.info(f"Validated input model for user: {model.user_uuid}")
#
#     bootstrapper = DependencyBootstrapper().build()
#     logger.info("Dependency bootstrapper built")
#
#     preparer = bootstrapper.get(CorrelationPreparer)
#     uc = bootstrapper.get(SingleCorrelationUseCase)
#     logger.info("Retrieved CorrelationPreparer and SingleCorrelationUseCase")
#
#     user_outcome_triggers = await preparer.get_user_outcome_triggers(user_id=model.user_uuid)
#     logger.info(
#         f"Retrieved user triggers for user {model.user_uuid}: {len(user_outcome_triggers) if user_outcome_triggers else 0} triggers found"
#     )
#
#     if user_outcome_triggers:
#         logger.info("Executing SingleCorrelationUseCase")
#         result = await uc.execute_async(
#             input_boundary=CorrelationInputBoundary(
#                 extension_id=model.extension_id,
#                 provider_id=model.provider_id,
#                 user_id=model.user_uuid,
#                 extension_input=CorrelationInput(outcome_triggers_input=user_outcome_triggers, should_notify=True),
#             )
#         )
#         logger.info(f"SingleCorrelationUseCase executed successfully for user {model.user_uuid}")
#         return result
#     else:
#         logger.info(f"No user triggers found for user {model.user_uuid}, skipping correlation")
#         return None
