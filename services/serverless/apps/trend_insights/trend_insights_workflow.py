# from datetime import timedelta
#
# from temporalio import workflow
#
#
# @workflow.defn
# class TrendInsightsWorkflow:
#     @workflow.run
#     async def run(self, model_input: str):
#         try:
#             result = await workflow.execute_activity(
#                 "handle_trend_insights",
#                 model_input,
#                 start_to_close_timeout=timedelta(minutes=45),
#             )
#             return result
#
#         except Exception as e:
#             workflow.logger.error(f"An error occurred: {str(e)}")
#             raise
