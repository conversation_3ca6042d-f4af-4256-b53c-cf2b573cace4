import random
from datetime import datetime, timedelta, timezone
from typing import List
from zoneinfo import ZoneInfo

import pandas as pd
import pytest
from dateutil.relativedelta import relativedelta

from services.base.application.boundaries.time_input import TimeInput
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from services.base.domain.schemas.shared import TimeIntervalModel
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.serverless.apps.trend_insights.app.data_transformer import DataTransformer
from services.serverless.apps.trend_insights.app.models.analytic_series_models import (
    SeriesAnalysisAveragedOutputBucket,
    SeriesAnalysisListValuesBucket,
)


def _sample_time_input(time_gte: datetime, time_lte: datetime) -> TimeInput:
    return TimeInput(
        interval="1d",
        time_gte=time_gte,
        time_lte=time_lte,
    )


def _sample_input_data(days_generated: int) -> list[dict]:
    tz_list = ["EST", "UTC", "MST"]
    random.seed(10)

    return [
        {
            DocumentLabels.TIMESTAMP: datetime.now(tz=ZoneInfo(random.choice(tz_list))) - timedelta(days=i),
            DocumentLabels.VALUE: random.randint(50, 100),
        }
        for i in range(days_generated)
    ]


@pytest.mark.parametrize(
    "input_data",
    [
        _sample_input_data(days_generated=28),
    ],
)
def test_to_sorted_data_frame_valid_input_should_pass(input_data: List[dict]):
    # Act
    frame = DataTransformer.to_sorted_data_frame(input_data=input_data, analytic_series=DocumentLabels.VALUE)
    # Assert
    assert frame.values[0][1] == 52
    assert frame.values[-1][1] == 85
    assert frame.values[0][0] > frame.values[-1][0]


def test_from_v3_documents_to_sorted_data_frame():
    # Act
    frame = DataTransformer.from_v3_documents_to_sorted_data_frame(
        documents=EmotionBuilder().build_n(),
        series_value_field="rating",
        series="mood",
    )
    timestamp_list = [ts for ts in frame[DocumentLabels.TIMESTAMP].values]
    assert timestamp_list == sorted(timestamp_list, reverse=True)


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        (
            {
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=7),
            },
            [
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
            ],
        ),
        (
            {
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 8, 2, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 11, 30, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=30),
            },
            [
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 31, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 11, 30, 0, 0, 0, tzinfo=timezone.utc),
                        duration=2592000,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 1, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 31, 0, 0, 0, tzinfo=timezone.utc),
                        duration=2592000,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 1, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 1, 0, 0, 0, tzinfo=timezone.utc),
                        duration=2592000,
                    ),
                ),
                SeriesAnalysisListValuesBucket(
                    value_list=[],
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 8, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 1, 0, 0, 0, tzinfo=timezone.utc),
                        duration=2592000,
                    ),
                ),
            ],
        ),
    ],
)
def test_create_empty_value_lists_buckets_should_pass(test_input, expected_result):
    result = DataTransformer.create_empty_value_lists_buckets(**test_input)
    assert result == expected_result


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        (
            {
                "timestamp_list": [],
                "value_list": [],
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=7),
            },
            [None, None, None, None],
        ),
        (
            {
                "timestamp_list": [
                    datetime(2021, 10, 4, 0, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 9, 26, 0, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 9, 24, 0, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 9, 16, 0, 0, 0, tzinfo=timezone.utc),
                ],
                "value_list": [50, 70, 60, 80],
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=7),
            },
            [50, 70, 60, 80],
        ),
        (
            {
                "timestamp_list": [
                    datetime(2021, 10, 4, 2, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 10, 4, 4, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 10, 4, 6, 0, 0, tzinfo=timezone.utc),
                    datetime(2021, 10, 4, 8, 0, 0, tzinfo=timezone.utc),
                ],
                "value_list": [50, 20, 70, 60],
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=7),
            },
            [50, None, None, None],
        ),
        (
            {
                "timestamp_list": [
                    datetime(2022, 10, 4, 2, 0, 0, tzinfo=timezone.utc),
                    datetime(2022, 9, 4, 4, 0, 0, tzinfo=timezone.utc),
                    datetime(2022, 9, 4, 6, 0, 0, tzinfo=timezone.utc),
                    datetime(2022, 9, 4, 8, 0, 0, tzinfo=timezone.utc),
                ],
                "value_list": [50, 20, 70, 60],
                "time_input": _sample_time_input(
                    time_gte=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                    time_lte=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                ),
                "bucket_range": relativedelta(days=7),
            },
            [None, None, None, None],
        ),
    ],
)
def test_create_averaged_buckets_from_value_lists_buckets_valid_input_should_pass(test_input, expected_result):
    dataframe = pd.DataFrame(
        {DocumentLabels.TIMESTAMP: test_input["timestamp_list"], DocumentLabels.VALUE: test_input["value_list"]}
    )
    value_lists_buckets = DataTransformer.create_empty_value_lists_buckets(
        time_input=test_input[TrendInsightsLabels.TIME_INPUT], bucket_range=test_input["bucket_range"]
    )
    result = DataTransformer.create_averaged_buckets_from_value_lists_buckets(
        data_frame=dataframe,
        value_lists_buckets=value_lists_buckets,
        analytic_series=DocumentLabels.VALUE,
        agg_function="mean",
    )
    result_values = [bucket.aggregated_value for bucket in result]
    assert result_values == expected_result


@pytest.mark.parametrize(
    "input_buckets,expected_result",
    [
        (
            [
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=84.14,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=75.65,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=75.65,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
            ],
            True,
        ),
        (
            [
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
            ],
            False,
        ),
        (
            [
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=84,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
            ],
            False,
        ),
        (
            [
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=84,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=None,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=84,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
                SeriesAnalysisAveragedOutputBucket(
                    aggregated_value=74,
                    time_range=TimeIntervalModel(
                        timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                        duration=604800,
                    ),
                ),
            ],
            False,
        ),
    ],
)
def test_check_if_enough_consecutive_buckets(
    input_buckets: List[SeriesAnalysisAveragedOutputBucket], expected_result: bool
):
    result = DataTransformer.has_enough_consecutive_buckets(input_buckets)
    assert result == expected_result


@pytest.mark.parametrize(
    "row,expected_result",
    [
        (
            {DocumentLabels.TIMESTAMP: datetime(2021, 9, 12, 0, 0, 0, tzinfo=timezone.utc), DocumentLabels.VALUE: 50},
            [50],
        ),
        ({DocumentLabels.TIMESTAMP: datetime(2021, 9, 9, 0, 0, 0, tzinfo=timezone.utc), DocumentLabels.VALUE: 50}, []),
        (
            {DocumentLabels.TIMESTAMP: datetime(2021, 9, 11, 12, 0, 0, tzinfo=timezone.utc), DocumentLabels.VALUE: 50},
            [50],
        ),
        (
            {DocumentLabels.TIMESTAMP: datetime(2021, 9, 18, 12, 0, 0, tzinfo=timezone.utc), DocumentLabels.VALUE: 50},
            [],
        ),
    ],
)
def test_check_if_in_bucket_and_append_to_values_list(row, expected_result):
    bucket = SeriesAnalysisListValuesBucket(
        value_list=[],
        time_range=TimeIntervalModel(
            timestamp=datetime(2021, 9, 11, tzinfo=timezone.utc),
            end_time=datetime(2021, 9, 18, tzinfo=timezone.utc),
            duration=604800,
        ),
    )
    analytic_series = DocumentLabels.VALUE
    DataTransformer.check_if_in_bucket_and_append_to_values_list(
        bucket=bucket, row=row, analytic_series=analytic_series
    )
    assert bucket.value_list == expected_result
