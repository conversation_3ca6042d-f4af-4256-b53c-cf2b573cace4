from enum import StrEnum
from typing import Type

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document
from services.base.type_resolver import TypeResolver


class AnalyticType(StrEnum):
    RestingHeartRate = DataType.RestingHeartRate
    HeartRate = DataType.HeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric
    Stress = DataType.Stress
    Emotion = DataType.Emotion
    Nutrition = "nutrition"

    def to_model(self) -> Type[Document]:
        return TypeResolver.get_document(type_id=self)
