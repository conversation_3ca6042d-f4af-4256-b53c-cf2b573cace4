from uuid import UUID

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.schemas.shared import BaseDataModel
from services.serverless.apps.trend_insights.app.models.trend_insights_models import TrendInsightsInput


class TrendInsightsInputBoundary(BaseDataModel):
    extension_input: TrendInsightsInput = Field(alias=ExtensionLabels.EXTENSION_INPUT)
    provider_id: UUID = Field(alias=ExtensionLabels.PROVIDER_ID)
    extension_id: UUID = Field(alias=ExtensionLabels.EXTENSION_ID)
    user_uuid: UUID = Field(alias=DocumentLabels.USER_UUID)
