import logging
from statistics import mean
from typing import List, Optional

import numpy
from pandas import Series
from pydantic import ValidationError

from services.base.domain.enums.analytics.trend_insights import TrendDirection
from services.serverless.apps.trend_insights.app.models.analysis_models import (
    AnalysisTrendOutputModel,
    StatisticsOutputModel,
)
from services.serverless.apps.trend_insights.app.models.analytic_series_models import (
    SeriesAnalysisAveragedOutputBucket,
)


class DataAnalyzer:
    @classmethod
    def calculate_trend(cls, buckets: List[SeriesAnalysisAveragedOutputBucket]) -> Optional[AnalysisTrendOutputModel]:
        """Calculates trend from input data. Expects the data sorted in descending order."""
        # Creates a list of values from populated buckets
        dependant_variable = buckets[0].aggregated_value
        independent_variables = [
            bucket.aggregated_value for bucket in buckets[1:] if bucket.aggregated_value is not None
        ]

        # Filter out None values and ensure all elements are floats
        populated_values: list[float] = [
            float(value) for value in ([dependant_variable] + independent_variables) if value is not None
        ]

        # Returns None if no dependant variable or no independent variable
        if not dependant_variable or len(independent_variables) == 0 or len(set(populated_values)) == 1:
            return None

        # Compares most recent entry with previous one
        absolute_difference_from_previous_bucket = dependant_variable - independent_variables[0]
        percentage_difference_from_previous_bucket = (
            ((dependant_variable - independent_variables[0]) / independent_variables[0]) * 100
            if dependant_variable != 0 and independent_variables[0] != 0
            else 0
        )
        absolute_difference_from_aggregated_buckets = dependant_variable - mean(independent_variables)
        percentage_difference_from_aggregated_buckets = (
            ((dependant_variable - mean(independent_variables)) / mean(independent_variables)) * 100
            if dependant_variable != 0 and mean(independent_variables) != 0
            else 0
        )

        # Attempts to find continuous trend
        assert populated_values
        reversed_references = sorted(populated_values, reverse=True)
        sorted_references = sorted(populated_values)

        trend = TrendDirection.NON_MONOTONOUS
        if populated_values == sorted_references:
            trend = TrendDirection.DECREASING
        if populated_values == reversed_references:
            trend = TrendDirection.INCREASING

        return AnalysisTrendOutputModel(
            consecutive_trend=trend,
            independent_variables_mean=mean(independent_variables),
            absolute_difference_from_previous_bucket=absolute_difference_from_previous_bucket,
            absolute_difference_from_aggregated_buckets=absolute_difference_from_aggregated_buckets,
            percentage_difference_from_aggregated_buckets=percentage_difference_from_aggregated_buckets,
            percentage_difference_from_previous_bucket=percentage_difference_from_previous_bucket,
        )

    @classmethod
    def calculate_statistics(cls, series: Series) -> StatisticsOutputModel:
        series = series.replace(0, numpy.nan)
        series = series.dropna()
        summary = series.describe()
        summary = summary.replace(numpy.nan, None)
        try:
            return StatisticsOutputModel(
                max=float(summary["max"]),
                min=float(summary["min"]),
                mean=float(summary["mean"]),
                std=0 if summary["std"] is None else float(summary["std"]),
                quartile_upper=float(summary["75%"]),
                quartile_lower=float(summary["25%"]),
                sum=series.sum(),
            )
        except ValidationError as error:
            logging.exception(
                f"Error {error} raised when calculating statistics, series: {series}, summary: {summary} "
            )
            raise error
