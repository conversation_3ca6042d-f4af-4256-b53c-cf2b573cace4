import asyncio
from typing import Any, Async<PERSON>enerator

import pytest

from services.serverless.apps.analytics_scheduler.dependency_bootstrapper import Dependency<PERSON>ootstrapper


@pytest.fixture(scope="session")
async def dependency_bootstrapper() -> AsyncGenerator[DependencyBootstrapper, Any]:
    bootstrapper = DependencyBootstrapper().build()
    yield bootstrapper
    await bootstrapper.cleanup()


@pytest.fixture(scope="session")
def event_loop():
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()
