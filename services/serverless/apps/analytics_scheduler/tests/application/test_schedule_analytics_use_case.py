from datetime import datetime

import pytest

from services.serverless.apps.analytics_scheduler.application.schedule_analytics_use_case import (
    ScheduleAnalyticsUseCase,
)

test_cases = [
    # Test cases for hourly
    (datetime(2022, 1, 1, 0, 0), "hourly", True),
    (datetime(2022, 1, 1, 1, 1), "hourly", False),
    (datetime(2022, 1, 1, 1, 59), "hourly", False),  # Close to the next hour but not exactly at the start
    # Test cases for daily
    (datetime(2022, 1, 1, 0, 0), "daily", True),
    (datetime(2022, 1, 1, 0, 1), "daily", False),
    (datetime(2022, 1, 1, 1, 0), "daily", False),
    (datetime(2022, 1, 1, 23, 59), "daily", False),  # Close to midnight but still part of the previous day
    # Test cases for weekly - runs on start of Tuesday
    (datetime(2022, 1, 4, 0, 0), "weekly", True),  # Tuesday midnight
    (datetime(2022, 1, 4, 0, 1), "weekly", False),  # Tuesday minute after midnight
    (datetime(2022, 1, 5, 0, 0), "weekly", False),  # Wednesday
    (datetime(2022, 1, 3, 23, 59), "weekly", False),  # End of Monday
    # Test cases for monthly - runs on start of 2nd day of month
    (datetime(2022, 1, 2, 0, 0), "monthly", True),  # 2nd day of the month
    (datetime(2022, 1, 2, 0, 1), "monthly", False),  # 2nd day of the month
    (datetime(2022, 1, 3, 0, 0), "monthly", False),  # 3rd day of the month
    (datetime(2022, 1, 1, 23, 59), "monthly", False),  # Close to the 2nd but still the 1st day of the month
]


@pytest.mark.parametrize("now, schedule, expected", test_cases)
def test_should_extension_run(now, schedule, expected, dependency_bootstrapper):
    result = dependency_bootstrapper.get(ScheduleAnalyticsUseCase)._should_extension_run(now=now, schedule=schedule)
    assert result == expected
