import asyncio
import itertools
import logging
from datetime import datetime, timedelta, timezone
from typing import Sequence
from uuid import UUID

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
from services.base.domain.constants.messaging import ATT_NAME_ANALYTICS_EVENT, MessageTopics
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNot<PERSON><PERSON><PERSON>ereException
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_subscriptions_repository import ExtensionSubscriptionsRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.wrappers import Range, ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_subscriptions import ExtensionSubscriptions
from services.base.message_queue.utils import create_string_message_attribute
from settings.app_config import settings
from settings.app_secrets import secrets
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


class ScheduleAnalyticsUseCase:
    SCHEDULE = {
        settings.TREND_INSIGHTS_EXTENSION_ID: settings.TREND_INSIGHTS_EXTENSION_SCHEDULE,
        settings.SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID: settings.SINGLE_CORRELATION_EVALUATOR_SCHEDULE,
    }

    def __init__(
        self,
        message_broker_client: AsyncMessageBrokerClient,
        member_user_repository: MemberUserRepository,
        extension_detail_repository: ExtensionDetailRepository,
        extension_subscriptions_repository: ExtensionSubscriptionsRepository,
    ):
        self._message_broker_client = message_broker_client
        self._member_user_repository = member_user_repository
        self._extension_detail_repository = extension_detail_repository
        self._extension_subscriptions_repository = extension_subscriptions_repository

    async def execute_async(self, now: datetime, active_considered_period: timedelta, force_run: bool):
        # Internal flow
        for extension_id in [SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID]:
            try:
                if force_run or self._should_extension_run(schedule=self.SCHEDULE[extension_id], now=now):
                    extension = await self._extension_detail_repository.get_by_extension_id(extension_id=extension_id)
                    if not extension:
                        raise ShouldNotReachHereException(f"Extension {extension_id} not found.")
                    await self.do_execute(
                        extension.provider_id,
                        users_subscriptions=await self._get_internal_users(extension_id=extension_id),
                    )
            except Exception as error:
                logging.exception(f"Unknown error: {error}")

        # Public flow
        for extension_id in [TREND_INSIGHTS_EXTENSION_ID]:
            try:
                if force_run or self._should_extension_run(schedule=self.SCHEDULE[extension_id], now=now):
                    extension = await self._extension_detail_repository.get_by_extension_id(extension_id=extension_id)
                    if not extension:
                        raise ShouldNotReachHereException(f"Extension {extension_id} not found.")
                    await self.do_execute(
                        extension.provider_id,
                        users_subscriptions=await self._get_subscribed_users(
                            extension_id=extension_id, active_considered_period=active_considered_period
                        ),
                    )
            except Exception as error:
                logging.exception(f"Unknown error: {error}")

    async def do_execute(self, provider_id: UUID, users_subscriptions: Sequence[ExtensionSubscriptions]):
        for subscriptions_slice in itertools.batched(users_subscriptions, 100):
            async with asyncio.TaskGroup() as task_group:
                [
                    task_group.create_task(self._publish(extension_subscription=subscription, provider_id=provider_id))
                    for subscription in subscriptions_slice
                ]

    async def _get_subscribed_users(
        self,
        active_considered_period: timedelta,
        extension_id: UUID,
    ) -> Sequence[ExtensionSubscriptions]:
        # TODO(jaja): Use extension subscription and its subscriptions once we have extensions management set in place
        now = datetime.now(timezone.utc)
        subscribed_users = await self._member_user_repository.get(
            wrapper=ReadFromDatabaseWrapper(
                search_keys={},
                range_filter=Range(field_name="last_logged_at", start_date=now - active_considered_period),
            )
        )

        return [ExtensionSubscriptions(extension_id=extension_id, user_id=user.user_uuid) for user in subscribed_users]

    async def _get_internal_users(self, extension_id: UUID):
        users = await self._member_user_repository.get(wrapper=ReadFromDatabaseWrapper(search_keys={}))
        internal_users = list(
            filter(
                lambda u: u.primary_email
                and (u.primary_email.endswith("@llif.org") or (u.primary_email in secrets.LOGIN_MAIL_WHITELIST)),
                users,
            )
        )

        return [ExtensionSubscriptions(extension_id=extension_id, user_id=user.user_uuid) for user in internal_users]

    async def _publish(self, extension_subscription: ExtensionSubscriptions, provider_id: UUID):
        try:
            await self._message_broker_client.publish_topic(
                topic_name=MessageTopics.TOPIC_ANALYTIC_SCHEDULED.value,
                message_body=AnalyticsScheduledEventModel(
                    extension_id=extension_subscription.extension_id,
                    user_uuid=extension_subscription.user_id,
                    timestamp=datetime.now(timezone.utc),
                    provider_id=provider_id,
                ).model_dump_json(),
                message_attributes=create_string_message_attribute(
                    ATT_NAME_ANALYTICS_EVENT, MessageTopics.TOPIC_ANALYTIC_SCHEDULED.value
                ),
            )
        except Exception as error:
            logging.exception(f"Error scheduling user analytics, subscription: {extension_subscription}, err: {error}")

    def _should_extension_run(self, now: datetime, schedule: str) -> bool:
        match schedule:
            case "hourly":
                return now.minute == 0
            case "daily":
                # Midnight
                return now.hour == 0 and now.minute == 0
            case "weekly":
                # End of monday
                return now.hour == 0 and now.minute == 0 and now.weekday() == 1
            case "monthly":
                # End of first day of month
                return now.hour == 0 and now.minute == 0 and now.day == 2
            case _:
                raise ShouldNotReachHereException(f"Unknown schedule: {schedule}")
