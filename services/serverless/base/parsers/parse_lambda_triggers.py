import json
from typing import Sequence

from aws_lambda_typing.events.sns import SNSMessage

from services.base.domain.schemas.shared import BaseDataModel


class MessagePayload(BaseDataModel):
    payload: dict
    message_attributes: dict


def parse_lambda_triggers(records: Sequence[dict]):
    return [*parse_sqs_trigger(records=records), *parse_sns_trigger(records=records)]


def parse_sqs_trigger(records: Sequence[dict]) -> Sequence[MessagePayload]:
    models = []
    for record in records:
        if record.get("eventSource", "") == "aws:sqs":
            message_payload = json.loads(record["body"])

            payload_to_append, message_attributes = (
                (json.loads(message_payload["Message"]), message_payload["MessageAttributes"])
                if all((message_payload.get("MessageId"), message_payload.get("TopicArn")))
                else (message_payload, record["messageAttributes"])
            )

            models.append(
                MessagePayload(
                    payload=payload_to_append,
                    message_attributes=message_attributes,
                )
            )
    return models


def parse_sns_trigger(records: Sequence[dict]) -> Sequence[MessagePayload]:
    models = []
    for record in records:
        if record.get("EventSource", "") == "aws:sns":
            sns_message: SNSMessage = record["Sns"]
            models.append(
                MessagePayload(
                    payload=json.loads(sns_message["Message"]),
                    message_attributes=sns_message["MessageAttributes"],
                )
            )
    return models
