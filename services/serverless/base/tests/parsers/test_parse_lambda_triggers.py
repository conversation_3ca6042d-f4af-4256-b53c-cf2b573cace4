import json

from services.serverless.base.parsers.parse_lambda_triggers import (
    MessagePayload,
    parse_sns_trigger,
    parse_sqs_trigger,
)


def test_parse_sqs_message_directly_sent_should_pass():
    # Arrange
    record = {
        "eventSource": "aws:sqs",
        "body": json.dumps({"field": "field content"}),
        "messageAttributes": {
            "user_event": {
                "Type": "String",
            }
        },
    }
    expected_output = MessagePayload(
        payload={"field": "field content"},
        message_attributes={
            "user_event": {
                "Type": "String",
            }
        },
    )
    # Act
    model = parse_sqs_trigger(records=[record])

    # Assert
    assert model == [expected_output]


def test_parse_sqs_sent_from_sns_should_pass():
    # Arrange
    record = {
        "eventSource": "aws:sqs",
        "body": json.dumps(
            {
                "Message": json.dumps({"field": "field content"}),
                "MessageId": "id",
                "TopicArn": "topic",
                "MessageAttributes": {
                    "user_event": {
                        "Type": "String",
                    }
                },
            }
        ),
        "messageAttributes": {},
    }
    expected_output = MessagePayload(
        payload={"field": "field content"},
        message_attributes={
            "user_event": {
                "Type": "String",
            }
        },
    )
    # Act
    model = parse_sqs_trigger(records=[record])

    # Assert
    assert model == [expected_output]


def test_parse_sns_should_pass():
    # Arrange
    record = {
        "EventSource": "aws:sns",
        "Sns": {
            "Message": json.dumps({"field": "field content"}),
            "MessageAttributes": {
                "user_event": {
                    "Type": "String",
                }
            },
        },
    }
    expected_output = MessagePayload(
        payload={"field": "field content"},
        message_attributes={
            "user_event": {
                "Type": "String",
            }
        },
    )
    # Act
    model = parse_sns_trigger(records=[record])

    # Assert
    assert model == [expected_output]
