# Deployment of lambda function to AWS Lambda

**NOTE, that AWS lambda function is a STANDALONE/SEPARATE application, it does NOT have (regular) access to the main application**

You need to create a standalone zip bundle containing the `lambda_function(.py)` file with all of the other source files, files used and imported from the project, including the whole dependency tree.

## Basic way - create & upload deploy zip
1. Create a folder, let's say, `deploy_lambda`, that will contain the set of files & folders to deploy (only its content will be deployed, not the folder itself)
2. Create & Activate a [virtual environment](https://github.com/livelearninnovate/foundation/tree/develop/services/serverless#installing--copying-requirements-from-virtualenv) with the Python version that you've activated on the AWS Lambda
3. Optionally, update the `requirements.txt` in the target lambda function folder if necessary
6. Copy the package folders and files from `sites-packages` in the virtualenv into the `deploy_lambda` (see below how)
7. Copy source files from the foundation lambda function folder (e.g. `services/serverless/alexa_skill_life_log`) into the `deploy_lambda` folder
8. Copy `services`, `settings` and `app_constants.py` from the foundation's root folder into the `deploy_lambda` folder
9. Note that all these are copied DIRECTLY into the `deploy_lambda`, so e.g. a `package_folder`, `lambda_function.py` and `modules` will be on the SAME (first) level
10. In `deploy_lambda` folder, create new empty file `settings.ini` and put the contents of target environment settings in there. For production, for example, it would be the contents of `settings/settings.prod.ini`.
11. **Make sure there are no syntax errors or missing dependencies** You can do so by executing `lambda_function(.py)` in the virtual environment and checking the output, if there is any.
12. Create a zip file out of `deploy_lambda` folder's contents. (NOT the folder itself)
13. Go to AWS Lambda's "Function code" section, click "Actions" and choose "Upload a .zip file", then choose the file and click "Save"
14. The lambda function should automatically deploy if it was uploaded correctly, please note, the effects can take from a few seconds up to 1-2 minutes.


## Installing & Copying requirements from virtualenv
- Let's say you have these folders:
- - `deploy_lambda` with lambda function files to be deployed
- - `services/serverless/alexa_skill_life_log` in foundation with the original source files
- - `pckgs_alexalifelogvirtualenv/virtualenv` in the same directory which is also a virtualenv based on Python 3.9
- Activate the virtualenv with `source pckgs_alexalifelogvirtualenv/virtualenv/bin/activate`
- Install requirements from the source files folder: `pip install -r services/serverless/alexa_skill_life_log/requirements.txt`
- copy everything from site-packages: `cp pckgs_alexalifelogvirtualenv/virtualenv/lib/python3.9/site-packages/. deploy_lambda --no-clobber --recursive`

### Creating a virtual environment
Command for python 3.9: `virtualenv --python=/usr/bin/python3.9 pckgs_alexalifelogvirtualenv/virtualenv`

## Advanced automated way
@TODO - there are frameworks that automate this whole process
