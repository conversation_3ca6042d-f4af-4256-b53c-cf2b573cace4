from typing import Sequence

from services.base.api.query.boolean_query_api import BooleanQueryType, CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPI
from services.base.api.query.mapper.leaf_query_api_mapper import LeafQueryAPIMapper
from services.base.domain.schemas.query.boolean_query import <PERSON><PERSON><PERSON><PERSON>, BooleanQuery, NotQuery, OrQuery
from services.base.domain.schemas.query.leaf_query import LeafQuery


class CompoundBooleanQueryAPIMapper:

    @staticmethod
    def map(boolean_query_api: CompoundBooleanQueryAPI) -> BooleanQuery:
        converted_queries = CompoundBooleanQueryAPIMapper._map_queries(queries=boolean_query_api.queries)
        if boolean_query_api.type == BooleanQueryType.AND:
            return AndQuery(queries=converted_queries)
        elif boolean_query_api.type == BooleanQueryType.OR:
            return OrQuery(queries=converted_queries)
        elif boolean_query_api.type == BooleanQueryType.NOT:
            return NotQuery(queries=converted_queries)
        else:
            raise TypeError(f"Unexpected CompoundBooleanQueryType: {boolean_query_api.type}")

    @staticmethod
    def _map_queries(queries: Sequence[LeafQueryAPI | CompoundBooleanQueryAPI]) -> Sequence[LeafQuery | BooleanQuery]:
        result: list[LeafQuery | BooleanQuery] = []
        for query in queries:
            if isinstance(query, LeafQueryAPI):
                result.append(LeafQueryAPIMapper.map(leaf_query_api=query))
            elif isinstance(query, CompoundBooleanQueryAPI):
                result.append(CompoundBooleanQueryAPIMapper.map(boolean_query_api=query))
            else:
                raise TypeError(f"Unexpected query type: {query.type}")

        return result
