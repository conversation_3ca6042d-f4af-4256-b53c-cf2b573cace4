"""
You can run this util script also from command line!
Just add "output_pkce_pair" as first arg, example:
python services/base/utils/authentication/oauth2_pkce.py output_pkce_pair
"""

import hashlib
from base64 import urlsafe_b64encode
from secrets import SystemRandom
from string import ascii_letters, digits


def create_pkce_pair():
    """Returns dictionary {"code_verifier": ..., "code_challenge": ...}"""

    chars = ascii_letters + digits + "-._~"
    code_verifier = "".join([SystemRandom().choice(chars) for _ in range(0, 128)])
    code_hash = hashlib.sha256()
    code_hash.update(str.encode(code_verifier))
    unencoded_challenge = code_hash.digest()  # gives the hash bytes
    b64_challenge = urlsafe_b64encode(unencoded_challenge)
    code_challenge = b64_challenge.decode().rstrip("=")

    return {
        "code_verifier": code_verifier,
        "code_challenge": code_challenge,
    }


# DOCS: https://docs.python.org/3/library/__main__.html
if __name__ == "__main__":
    import json
    import sys

    try:
        if sys.argv[1] == "output_pkce_pair":
            print(json.dumps(create_pkce_pair(), indent=4))
    except IndexError:
        pass
