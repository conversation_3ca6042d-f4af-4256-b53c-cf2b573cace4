from datetime import datetime, timedelta, timezone
from uuid import UUI<PERSON>

from jose import jwt

from services.base.application.authorization_encryption import encrypt
from services.base.application.constants import JwtAlgorithms, UserTokenKeys
from services.base.domain.constants.document_labels import DocumentLabels
from settings.app_constants import DEMO1_UUID, TEST1_UUID
from settings.app_secrets import secrets


def generate_long_term_access_token(user_uuid: UUID) -> str:
    """Generates long living demo1 user access token for local development and testing purposes."""
    now = datetime.now(timezone.utc)
    access_token_payload = {
        DocumentLabels.USER_UUID: encrypt(string=str(user_uuid)),
        UserTokenKeys.EXPIRATION_TIME: now + timedelta(days=30),
        UserTokenKeys.ISSUED_AT: now,
    }
    access_token: str = jwt.encode(
        access_token_payload, secrets.ACCESS_TOKEN_SECRET, algorithm=JwtAlgorithms.HS256.value
    )
    return access_token


# For usage as a standalone script, with Makefile command or similar.
if __name__ == "__main__":
    print(f"DEMO1 access token: {generate_long_term_access_token(DEMO1_UUID)}")
    print(f"TEST1 access_token: {generate_long_term_access_token(TEST1_UUID)}")
