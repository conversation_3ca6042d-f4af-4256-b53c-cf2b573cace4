import logging
from typing import Callable, Type

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.exceptions import HTTPException
from fastapi.responses import JSONResponse
from starlette import status

from services.base.api.authentication.exceptions import (
    InvalidCredentialsException,
)
from services.base.application.constants import UserTokenKeys
from services.base.application.exceptions import (
    BadRequestException,
    ConflictingActionInProgress,
    DefaultException,
    InvalidPrivilegesException,
    NoContentException,
    ReFetchException,
    RetryLater,
    RuntimeException,
)
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException


def default_exception_handler(_: Request, exc: DefaultException):
    logging.exception(f"Default exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"message": exc.message},
    )


def runtime_exception_handler(_: Request, exc: RuntimeException):
    logging.exception(f"Runtime exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"message": exc.message},
    )


def no_content_exception_handler(_: Request, exc: NoContentException):
    logging.info(f"No content exception: {exc}")
    return Response(
        status_code=status.HTTP_204_NO_CONTENT,
    )


def http_exception_handler(_: Request, exc: HTTPException):
    logging.info(f"HTTP Exception: {exc.detail}")
    return JSONResponse(
        headers=exc.headers,
        status_code=exc.status_code,
        content={"message": exc.detail},
    )


def invalid_credentials_exception_handler(_: Request, exc: InvalidCredentialsException):
    logging.info(f"HTTP Exception: {exc.message}")
    response = JSONResponse(
        status_code=401,
        content={"message": exc.message},
    )
    response.delete_cookie(key=UserTokenKeys.API_REFRESH_TOKEN)
    return response


def refetch_exception_handler(_: Request, exc: ReFetchException):
    logging.info(f"Refetch exception: {exc}")
    return Response(
        status_code=status.HTTP_204_NO_CONTENT,
    )


def retry_later_handler(_: Request, exc: RetryLater):
    logging.info(f"Retry later exception: {exc}")
    return Response(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={"message": exc.message},
        headers={"Retry-After": str(exc.retry_later)},
    )


def invalid_privileges_handler(_: Request, exc: InvalidPrivilegesException):
    logging.info(f"Invalid privileges exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content={"message": exc.message},
    )


def conflicting_action_in_progress(_: Request, exc: ConflictingActionInProgress):
    logging.info(f"Conflicting action in progress exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content={"message": "There is a conflicting action already in progress, please try again later."},
    )


def bad_request_handler(_: Request, exc: BadRequestException):
    logging.info(f"Bad request occurred {exc}")
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"message": exc.message},
    )


def query_validation_exception_handler(_: Request, exc: QueryValidationException):
    logging.info(f"Query Validation exception occurred {exc}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"message": exc.args[0]},
    )


exception_handlers: dict[Type[DefaultException] | Type[HTTPException] | Type[QueryValidationException], Callable] = {
    NoContentException: no_content_exception_handler,
    RuntimeException: runtime_exception_handler,
    HTTPException: http_exception_handler,
    InvalidCredentialsException: invalid_credentials_exception_handler,
    ReFetchException: refetch_exception_handler,
    ConflictingActionInProgress: conflicting_action_in_progress,
    BadRequestException: bad_request_handler,
    InvalidPrivilegesException: invalid_privileges_handler,
    QueryValidationException: query_validation_exception_handler,
    RetryLater: retry_later_handler,
    DefaultException: default_exception_handler,
}


def set_default_exception_handlers(app: FastAPI):
    for exc, handler in exception_handlers.items():
        app.exception_handler(exc)(handler)
