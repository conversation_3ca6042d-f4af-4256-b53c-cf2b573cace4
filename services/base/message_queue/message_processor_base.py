# pylint: disable=too-few-public-methods
import logging
from abc import ABC, abstractmethod
from typing import Dict, Generator, Type

from services.base.domain.constants.messaging import MessageTopics
from services.base.message_queue.message_handler_base import UseCaseHandlerBase
from services.base.message_queue.message_wrapper import MessageWrapper


class MessageProcessorBase(ABC):
    """Base handler for services listening to messages"""

    @property
    @abstractmethod
    def use_case_handlers(self) -> Dict[str, Type[UseCaseHandlerBase]]:
        pass

    def process_message(self, message_wrapper: MessageWrapper) -> Generator[Type[UseCaseHandlerBase], None, None]:
        """Defines how service handles incoming message.
        Should return list of handlers that fires specific use cases.
        These use cases will be later planned as tasks."""
        # Get all message_attribute values
        message_attribute_values = message_wrapper.message_attributes.values()
        # For every use case
        for handler_name in self.use_case_handlers.keys():
            try:
                handler_type = self.use_case_handlers[handler_name]
            except KeyError:
                logging.exception("Could not find message handling use case")
                return
            # For every message attribute
            for attribute_value in message_attribute_values:
                attributes_match = False
                possible_topic = MessageTopics(attribute_value.get("Value"))
                # For every attribute handler is listening to
                for subscribable_attribute in handler_type.listen_to():
                    if possible_topic == subscribable_attribute:
                        attributes_match = True
                if attributes_match:
                    yield handler_type
