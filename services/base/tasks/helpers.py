import asyncio
from typing import Awaitable, Callable

from services.base.dependency_bootstrapper import resource_cleanup


def run_and_block_coroutine(func: Callable[..., Awaitable], *args, **kwargs):
    loop = asyncio.get_event_loop_policy().get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
    return loop.run_until_complete(run_and_dispose(func, *args, **kwargs))


async def run_and_dispose(func: Callable[..., Awaitable], *args, **kwargs):
    result = await func(*args, **kwargs)
    await resource_cleanup()
    return result
