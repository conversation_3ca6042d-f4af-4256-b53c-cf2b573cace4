from asyncio.futures import Future as AsyncioFuture
from concurrent.futures import Future
from typing import Type

from pydantic import ConfigDict

from services.base.domain.schemas.shared import BaseDataModel
from services.base.message_queue.message_handler_base import UseCaseHandlerBase
from services.base.message_queue.message_wrapper import MessageWrapper


class FutureWrapper(BaseDataModel):
    message: MessageWrapper
    handler: Type[UseCaseHandlerBase]
    future: Future | AsyncioFuture
    model_config = ConfigDict(arbitrary_types_allowed=True)
