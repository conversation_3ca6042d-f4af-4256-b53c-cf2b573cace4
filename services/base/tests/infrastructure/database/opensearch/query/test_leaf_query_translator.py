from datetime import datetime

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.events.activity import Activity, ActivityFields
from services.base.domain.schemas.query.leaf_query import RangeQuery, ValuesQuery
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_refiner import LeafQueryRefiner
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_translator import LeafQueryTranslator


class TestLeafQueryTranslator:

    def test_values_query_translation_with_one_value_should_pass(self):
        value = PrimitiveTypesGenerator.generate_random_string(min_length=3, max_length=15)
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=[value])

        refined_query = LeafQueryRefiner.refine(leaf_query=values_query, domain_type=Activity)
        result = LeafQueryTranslator.translate(leaf_query=refined_query)

        expected_result = {"term": {f"{ActivityFields.NAME}.keyword": value}}

        assert result == expected_result

    def test_values_query_translation_with_multiple_values_should_pass(self):
        values = PrimitiveTypesGenerator.generate_n_random_strings(n=3)
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=values)

        refined_query = LeafQueryRefiner.refine(leaf_query=values_query, domain_type=Activity)
        result = LeafQueryTranslator.translate(leaf_query=refined_query)

        expected_result = {"terms": {f"{ActivityFields.NAME}.keyword": values}}

        assert result == expected_result

    def test_range_query_translation_with_int_values_should_pass(self):
        gte = PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=5)
        lte = PrimitiveTypesGenerator.generate_random_int(min_value=10, max_value=20)
        range_query = RangeQuery(field_name=ActivityFields.RATING, lte=lte, gte=gte)

        result = LeafQueryTranslator.translate(leaf_query=range_query)

        expected_result = {"range": {ActivityFields.RATING: {"gte": gte, "lte": lte}}}

        assert result == expected_result

    def test_range_query_translation_with_only_one_int_value_should_pass(self):
        gte = PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=5)
        range_query = RangeQuery(field_name=ActivityFields.RATING, gte=gte)

        result = LeafQueryTranslator.translate(leaf_query=range_query)

        expected_result = {"range": {ActivityFields.RATING: {"gte": gte}}}

        assert result == expected_result

    def test_range_query_translation_with_one_value_set_to_zero_should_pass(self):
        lte = PrimitiveTypesGenerator.generate_random_int(min_value=10, max_value=10)
        range_query = RangeQuery(field_name=ActivityFields.RATING, gte=0, lte=lte)

        result = LeafQueryTranslator.translate(leaf_query=range_query)

        expected_result = {"range": {ActivityFields.RATING: {"gte": 0, "lte": lte}}}

        assert result == expected_result

    def test_range_query_translation_with_datetime_values_should_pass(self):
        gte = datetime.fromisoformat("2023-10-16T00:00:00Z")
        lte = datetime.fromisoformat("2023-10-17T00:00:00Z")
        range_query = RangeQuery(field_name=ActivityFields.RATING, gte=gte, lte=lte)

        result = LeafQueryTranslator.translate(leaf_query=range_query)

        expected_result = {
            "range": {
                ActivityFields.RATING: {
                    "gte": gte.isoformat(timespec="milliseconds"),
                    "lte": lte.isoformat(timespec="milliseconds"),
                }
            }
        }

        assert result == expected_result
