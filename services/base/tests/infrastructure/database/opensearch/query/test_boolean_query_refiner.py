from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.events.activity import Activity, ActivityFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery, OrQuery
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.infrastructure.database.opensearch.query_translator.boolean_query_refiner import BooleanQueryRefiner


class TestBooleanQueryRefiner:

    def test_simple_boolean_and_query(self):
        values = PrimitiveTypesGenerator.generate_n_random_strings()
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=values)

        and_query = AndQuery(queries=[values_query])

        refined_bool_query = BooleanQueryRefiner.refine(boolean_query=and_query, domain_type=Activity)

        assert isinstance(refined_bool_query, AndQuery)
        assert len(refined_bool_query.queries) == 1
        refined_values_query = refined_bool_query.queries[0]
        assert isinstance(refined_values_query, ValuesQuery)
        assert refined_values_query.field_name == "name.keyword"
        assert refined_values_query.values == values_query.values

    def test_simple_boolean_or_query(self):
        values = PrimitiveTypesGenerator.generate_n_random_strings()
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=values)

        or_query = OrQuery(queries=[values_query])

        refined_bool_query = BooleanQueryRefiner.refine(boolean_query=or_query, domain_type=Activity)

        assert isinstance(refined_bool_query, OrQuery)
        assert len(refined_bool_query.queries) == 1
        refined_values_query = refined_bool_query.queries[0]
        assert isinstance(refined_values_query, ValuesQuery)
        assert refined_values_query.field_name == "name.keyword"
        assert refined_values_query.values == values_query.values

    def test_simple_boolean_not_query(self):
        values = PrimitiveTypesGenerator.generate_n_random_strings()
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=values)

        not_query = NotQuery(queries=[values_query])

        refined_bool_query = BooleanQueryRefiner.refine(boolean_query=not_query, domain_type=Activity)

        assert isinstance(refined_bool_query, NotQuery)
        assert len(refined_bool_query.queries) == 1
        refined_values_query = refined_bool_query.queries[0]
        assert isinstance(refined_values_query, ValuesQuery)
        assert refined_values_query.field_name == "name.keyword"
        assert refined_values_query.values == values_query.values
        assert refined_bool_query.operator == not_query.operator

    def test_nested_boolean_query(self):
        values = PrimitiveTypesGenerator.generate_n_random_strings()
        values_query = ValuesQuery(field_name=ActivityFields.NAME, values=values)

        and_query = AndQuery(queries=[values_query])
        or_query = OrQuery(queries=[and_query])

        refined_bool_query = BooleanQueryRefiner.refine(boolean_query=or_query, domain_type=Activity)

        assert isinstance(refined_bool_query, OrQuery)
        assert len(refined_bool_query.queries) == 1
        refined_and_query = refined_bool_query.queries[0]
        assert isinstance(refined_and_query, AndQuery)
        refined_values_query = refined_and_query.queries[0]
        assert isinstance(refined_values_query, ValuesQuery)
        assert refined_values_query.field_name == "name.keyword"
        assert refined_values_query.values == values_query.values
