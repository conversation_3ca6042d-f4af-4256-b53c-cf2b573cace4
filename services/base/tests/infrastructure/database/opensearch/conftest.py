from unittest.mock import Magic<PERSON>ock

import pytest
from opensearchpy import Async<PERSON><PERSON>Search, OpenSearch

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.infrastructure.database.opensearch.wrappers.client import (
    get_async_default_os_client,
    get_default_os_client,
)


@pytest.fixture
def requested_fields():
    return [DocumentLabels.TIMESTAMP]


@pytest.fixture(scope="module")
def vcr_config():
    return {
        "filter_headers": [("authorization", "DUMMY")],
        "match_on": ["method", "path", "query"],
    }


@pytest.fixture(scope="module")
def os_client() -> OpenSearch:
    return get_default_os_client()


@pytest.fixture(scope="module")
def async_os_client() -> AsyncOpenSearch:
    return get_async_default_os_client()


@pytest.fixture(scope="session")
def os_empty_index_response():
    return {
        "took": 0,
        "timed_out": False,
        "_shards": {"total": 2, "successful": 2, "skipped": 0, "failed": 0},
        "hits": {"total": {"value": 0, "relation": "eq"}, "max_score": None, "hits": []},
        # in future, we may also want to add dummy aggregations here
    }


@pytest.fixture(scope="module")
def os_client_mock(os_empty_index_response):  # pylint: disable=redefined-outer-name
    local_client_mock = MagicMock()
    local_client_mock.search = MagicMock()
    local_client_mock.search.return_value = os_empty_index_response
    return local_client_mock
