from uuid import UUID, uuid4

import pytest

from services.base.infrastructure.database.opensearch.doc_id_refiner import DocI<PERSON><PERSON><PERSON><PERSON>
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    ALIAS_LAST_INDEX_POINTER,
)


class TestDocIdRefiner:
    @pytest.mark.parametrize(
        "current_index,expected_first_chars",
        [
            (f"event_activity-{ALIAS_FIRST_INDEX_POINTER}", "0000"),
            ("event_exercise-000001", "0401"),
            ("event_note-000010", "060a"),
            (f"event_body_metric-{ALIAS_LAST_INDEX_POINTER}", "01ff"),
            (f"record_sleep-{ALIAS_FIRST_INDEX_POINTER}", "0c00"),
        ],
    )
    def test_refine_event_doc_id_passes(self, current_index: str, expected_first_chars: str):
        doc_id = uuid4()
        expected_uuid = UUID(f"{expected_first_chars}{doc_id.hex[4:]}")
        assert DocIdRefiner.refine_doc_id(index_name=current_index, doc_id=doc_id) == expected_uuid

    @pytest.mark.parametrize(
        "current_index",
        [
            ("event-000017",),
            ("event-1",),
            ("event-1000",),
            ("event-ASD234",),
            ("event-100000",),
        ],
    )
    def test_refine_doc_id_fails(self, current_index: str):
        doc_id = uuid4()
        with pytest.raises(ValueError):
            DocIdRefiner.refine_doc_id(index_name=current_index, doc_id=doc_id)

    @pytest.mark.parametrize(
        "current_index,expected",
        [
            (f"event_body_metric-{ALIAS_FIRST_INDEX_POINTER}", 0),
            ("event_exercise-000001", 1),
            ("event_note-000010", 10),
            (f"event_body_metric-{ALIAS_LAST_INDEX_POINTER}", 255),
        ],
    )
    def test_get_partition_from_index_name_passes(self, current_index: str, expected: str):
        assert DocIdRefiner.get_partition_from_index_name(index_name=current_index) == expected

    @pytest.mark.parametrize(
        "current_index,expected",
        [
            ("event_activity-000000", 0),
            ("event_exercise-000000", 4),
            ("event_note-000000", 6),
            ("event_group-000000", 11),
            ("record_sleep-000000", 12),
        ],
    )
    def test__get_collection_from_index_passes(self, current_index: str, expected: str):
        assert DocIdRefiner.get_collection_from_index(index_name=current_index) == expected
