from unittest.mock import AsyncMock

import pytest
from aioboto3 import Session
from mypy_boto3_sns.service_resource import Topic
from types_aiobotocore_sns import SNSClient, SNSServiceResource

from services.base.infrastructure.aws.async_sns_wrapper import AsyncSNSWrapper


@pytest.fixture(scope="module")
def session():
    return AsyncMock(spec=Session)


async def test_create_or_get_topic(session):
    # Arrange
    topic_name = "DummyTopic"

    resource = AsyncMock(spec=SNSServiceResource)
    session.resource.return_value.__aenter__.return_value = resource
    sns_wrapper = AsyncSNSWrapper(session=session)
    # Act
    await sns_wrapper.create_or_get_topic(topic_name=topic_name)
    # Assert
    resource.create_topic.assert_awaited_with(Name=topic_name)


async def test_publish_topic(session):
    # Arrange
    topic_name = "DummyTopic"
    topic_arn = "DummyTopicArn"
    message = "DummyMessage"
    message_attributes = {"DummyAttribute": "DummyValue"}

    topic = AsyncMock(spec=Topic)
    topic.arn = topic_arn
    client = AsyncMock(spec=SNSClient)
    resource = AsyncMock(spec=SNSServiceResource)

    resource.create_topic.return_value = topic
    session.client.return_value.__aenter__.return_value = client
    session.resource.return_value.__aenter__.return_value = resource
    sns_wrapper = AsyncSNSWrapper(session=session)
    # Act
    await sns_wrapper.publish_topic(topic_name=topic_name, message_body=message, message_attributes=message_attributes)
    # Assert
    client.publish.assert_awaited_with(Message=message, MessageAttributes=message_attributes, TopicArn=topic.arn)
