from unittest.mock import MagicMock

import pytest
from mypy_boto3_secretsmanager import SecretsManagerClient

from services.base.infrastructure.aws.secrets_manager_wrapper import get_secret


@pytest.fixture(scope="module")
def secrets_manager_client_mock():
    return MagicMock(spec=SecretsManagerClient)


def test_get_secret_value(secrets_manager_client_mock, monkeypatch):
    # Arrange
    secret_name = "DummySecret"
    secrets_manager_client_mock.get_secret_value = MagicMock()
    monkeypatch.setattr(
        "services.base.infrastructure.aws.secrets_manager_wrapper._manager_client", secrets_manager_client_mock
    )
    # Act
    get_secret(secret_name=secret_name)
    # Assert
    secrets_manager_client_mock.get_secret_value.assert_called_with(SecretId=secret_name)
