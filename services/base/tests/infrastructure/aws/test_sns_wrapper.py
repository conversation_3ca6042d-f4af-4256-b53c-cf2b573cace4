from unittest.mock import <PERSON><PERSON>ock

import pytest
from mypy_boto3_sns import SNSClient, SNSServiceResource
from mypy_boto3_sns.service_resource import Topic

from services.base.infrastructure.aws.sns_wrapper import SNSWrapper


@pytest.fixture(scope="module")
def sns_service_mock():
    return MagicMock(spec=SNSClient)


@pytest.fixture(scope="module")
def sns_client_mock():
    return MagicMock(spec=SNSServiceResource)


def test_create_or_get_topic(sns_service_mock, sns_client_mock):
    # Arrange
    sns_wrapper = SNSWrapper(sns_service=sns_service_mock, sns_client=sns_client_mock)
    topic_name = "DummyTopic"
    # Act
    sns_wrapper.create_or_get_topic(topic_name=topic_name)
    # Assert
    sns_service_mock.create_topic.assert_called_with(Name=topic_name)


def test_publish_topic(sns_service_mock, sns_client_mock):
    # Arrange
    topic_name = "DummyTopic"
    message = "DummyMessage"
    message_attributes = {"DummyAttribute": "DummyValue"}
    topic = MagicMock(spec=Topic)
    sns_service_mock.create_topic.return_value = topic
    sns_wrapper = SNSWrapper(sns_service=sns_service_mock, sns_client=sns_client_mock)
    # Act
    sns_wrapper.publish_topic(topic_name=topic_name, message_body=message, message_attributes=message_attributes)
    # Assert
    topic.publish.assert_called_with(Message=message, MessageAttributes=message_attributes)
