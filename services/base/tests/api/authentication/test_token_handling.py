from datetime import datetime, timedelta, timezone
from uuid import UUID, uuid4

import pytest
from jose.exceptions import J<PERSON><PERSON>rror

from services.base.api.authentication.exceptions import InvalidCredentialsException
from services.base.api.authentication.token_handling import (
    generate_access_token,
    generate_refresh_token,
    get_uuid_from_token,
    rotate_refresh_token,
)
from services.base.application.authorization_encryption import decrypt, encrypt
from services.base.application.constants import UserTokenKeys
from services.base.application.jwt_handling import (
    decode_jwt,
)
from services.base.domain.constants.time_constants import SECONDS_IN_DAY
from settings.app_config import settings
from settings.app_constants import DEMO1_UUID
from settings.app_secrets import secrets


def test_decode_token_should_pass():
    # Act
    user_uuid = uuid4()
    access_token = decode_jwt(
        token=generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)),
        key=secrets.ACCESS_TOKEN_SECRET,
    )
    refresh_token = decode_jwt(
        token=generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)),
        key=secrets.REFRESH_TOKEN_SECRET,
    )
    now = datetime.now(timezone.utc).timestamp()
    # Assert
    for token in (access_token, refresh_token):
        assert int(token[UserTokenKeys.EXPIRATION_TIME]) >= now
        assert int(token[UserTokenKeys.ISSUED_AT]) <= now
        assert get_uuid_from_token(token) == user_uuid


def test_decode_token_should_raise():
    # Act
    user_uuid = uuid4()
    with pytest.raises(JWTError):
        decode_jwt(
            token=generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)),
            key=secrets.REFRESH_TOKEN_SECRET,
        )
    with pytest.raises(JWTError):
        decode_jwt(
            token=generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)),
            key=secrets.ACCESS_TOKEN_SECRET,
        )

    with pytest.raises(JWTError):
        decode_jwt(
            token=generate_access_token(user_uuid=user_uuid, time_delta=timedelta(seconds=-10)),
            key=secrets.ACCESS_TOKEN_SECRET,
        )

    with pytest.raises(JWTError):
        decode_jwt(
            token=generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(seconds=-10)),
            key=secrets.REFRESH_TOKEN_SECRET,
        )


def test_get_uuid_from_token_should_pass():
    # Act
    user_uuid = uuid4()
    uuid = get_uuid_from_token(
        decoded_token=decode_jwt(
            token=generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)),
            key=secrets.ACCESS_TOKEN_SECRET,
        )
    )
    # Assert
    assert uuid is not None
    assert isinstance(uuid, UUID)


def test_get_uuid_from_token_no_uuid():
    with pytest.raises(InvalidCredentialsException):
        get_uuid_from_token(decoded_token={"test": "test"})


def test_rotate_refresh_token(mock_demo1_member_user):
    # Arrange
    expiration_timestamp = float(datetime.now(timezone.utc).timestamp())
    assert (
        rotate_refresh_token(
            user_id=mock_demo1_member_user.user_uuid, refresh_expiration_timestamp=expiration_timestamp
        )
        is not None
    )
    expiration_timestamp = float(
        datetime.now(timezone.utc).timestamp() + (SECONDS_IN_DAY * settings.API_REFRESH_TOKEN_ROTATION_TIME_DAYS * 2)
    )
    assert (
        rotate_refresh_token(
            user_id=mock_demo1_member_user.user_uuid, refresh_expiration_timestamp=expiration_timestamp
        )
        is None
    )


def test_uuid_encryption():
    encrypted_uuid = encrypt(string=str(DEMO1_UUID))
    assert DEMO1_UUID == UUID(decrypt(token=encrypted_uuid))
