from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.jwt_handling import (
    decode_jwt,
    encode_jwt,
)


def test_encode_jwt():
    payload = {PrimitiveTypesGenerator.generate_random_string(): PrimitiveTypesGenerator.generate_random_string()}
    key = PrimitiveTypesGenerator.generate_random_string()
    encoded_jwt = encode_jwt(payload=payload, key=key)
    assert payload == decode_jwt(token=encoded_jwt, key=key)
