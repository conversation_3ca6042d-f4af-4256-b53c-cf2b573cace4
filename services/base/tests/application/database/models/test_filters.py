from datetime import datetime

from services.base.application.database.models.filter_types import (
    ExistsFilter,
    RangeFilter,
    UserUUIDTermsFilter,
)
from services.base.application.database.models.filters import FilterBuilder


def test_filter_wrapper_with_filters():
    gte = datetime.fromisoformat("2023-10-16T00:00:00Z")
    lte = datetime.fromisoformat("2023-10-17T00:00:00Z")
    user_filter = UserUUIDTermsFilter(value=["ae6ab7d3-6089-4114-a4b9-48ed557d02cf"])
    range_filter = RangeFilter(name="test", gte=gte, lte=lte)
    exists_filter = ExistsFilter(name="test")
    input_filters = [user_filter, range_filter, exists_filter]

    filter_wrapper = FilterBuilder().with_filters(filters=input_filters)

    expected_output = FilterBuilder(
        terms_filters=[user_filter],
        range_filters=[range_filter],
        exists_filters=[exists_filter],
    )

    assert filter_wrapper == expected_output
