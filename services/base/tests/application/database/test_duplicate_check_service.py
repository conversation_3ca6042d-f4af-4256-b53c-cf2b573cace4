import pytest

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.exceptions import DuplicateDocumentsFound
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.query.query import Query
from services.base.tests.domain.builders.event_builder import Event<PERSON>uilder
from services.base.type_resolver import TypeResolver


class TestDuplicateCheckService:
    async def test_build_validation_query_n(
        self,
        duplicate_check_service: DuplicateCheckService,
    ):
        events = EventBuilder().build_all(n=2)
        query: Query = duplicate_check_service._build_validation_query(documents=events)
        unique_types = list({type(e) for e in events})

        assert len(query.type_queries) == len(unique_types)

        for type_query in query.type_queries:
            assert all([tp in unique_types for tp in type_query.domain_types])

    @pytest.mark.parametrize("builder", [b for b in TypeResolver.EVENT_BUILDERS])
    async def test_validate_no_document_duplicates_raises_error(
        self,
        builder,
        duplicate_check_service: DuplicateCheckService,
        event_repo: EventRepository,
    ):
        events = builder().build_n()
        await event_repo.insert(events=events, force_strong_consistency=True)
        with pytest.raises(DuplicateDocumentsFound):
            await duplicate_check_service.validate_no_document_duplicates(documents=events)
        await event_repo.delete_by_id(ids=[e.id for e in events])

    async def test_validate_no_document_duplicates_none_found(
        self,
        duplicate_check_service: DuplicateCheckService,
    ):
        events = EventBuilder().build_all()
        assert await duplicate_check_service.validate_no_document_duplicates(documents=events) is None
