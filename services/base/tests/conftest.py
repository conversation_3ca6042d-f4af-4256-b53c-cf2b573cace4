"""COMMON CONFTEST FOR *SEPARATE* SERVICE TESTS (services/service_name/tests/...)
(NOT to be confused with services/base/tests/...)
"""

import asyncio
import os
from datetime import datetime, timezone

import pytest
from opensearchpy._async.client import Async<PERSON>penSearch

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from settings.app_constants import DEMO1_UUID


@pytest.fixture
def mock_demo1_member_user():
    return MemberUser(
        user_uuid=DEMO1_UUID,
        first_name="demo",
        last_name="user",
        last_logged_at=datetime.now(timezone.utc),
        created_at=datetime.fromisoformat("2020-12-21T03:02:28.000Z"),
    )


@pytest.fixture(scope="session")
async def dependency_bootstrapper() -> DependencyBootstrapper:  # type: ignore
    bootstrapper = DependencyBootstrapper().build()
    yield bootstrapper  # type: ignore
    await bootstrapper.cleanup()


@pytest.fixture(scope="session")
def environment_repository(dependency_bootstrapper: DependencyBootstrapper) -> EnvironmentRepository:
    return dependency_bootstrapper.get(interface=EnvironmentRepository)


@pytest.fixture(scope="session")
def duplicate_check_service(dependency_bootstrapper: DependencyBootstrapper) -> DuplicateCheckService:
    return dependency_bootstrapper.get(interface=DuplicateCheckService)


@pytest.fixture(scope="session")
def event_repo(dependency_bootstrapper: DependencyBootstrapper) -> EventRepository:
    return dependency_bootstrapper.get(interface=EventRepository)


@pytest.fixture(scope="session")
def async_opensearch(dependency_bootstrapper: DependencyBootstrapper) -> AsyncOpenSearch:
    return dependency_bootstrapper.get(interface=AsyncOpenSearch)


# For some reason the loop is closed
# This overrides the default behaviour of the loop in pytest-asyncio
@pytest.fixture(scope="session")
def event_loop():
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Does not run integration test in normal pytest invocation
def pytest_collection_modifyitems(config, items):
    # Get the value of the environment variable and command-line option
    pipeline_run = os.getenv("PIPELINE_RUN")
    marks_option = config.getoption("-m", "")
    non_skipped_tests = []

    if pipeline_run:
        if "integration" not in marks_option:
            skip_integration = pytest.mark.skip(reason="skipped because it is an integration test")
            for item in items:
                if any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_integration)
                else:
                    non_skipped_tests.append(item)
        else:
            skip_non_integration = pytest.mark.skip(reason="skipped because it not an integration test")
            for item in items:
                if not any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_non_integration)
                else:
                    non_skipped_tests.append(item)
