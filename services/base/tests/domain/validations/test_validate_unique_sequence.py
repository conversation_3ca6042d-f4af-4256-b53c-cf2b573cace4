import pytest

from services.base.domain.validations.validate_unique_sequence import validate_unique_sequence


class TestValidateUniqueSequence:
    @pytest.mark.parametrize(
        "input_value",
        [
            ([1, 2, 3, 4]),
            ("abcd"),
            ((5, 6, 7)),
            ([]),
            (()),
            ("a"),
        ],
    )
    def test_validate_unique_sequence_passes(self, input_value):
        assert validate_unique_sequence(input_value) == input_value

    @pytest.mark.parametrize(
        "input_value",
        [
            ([1, 2, 2, 4]),
            ("aabc"),
            ((5, 5, 6, 7)),
            ([None, None]),
            ((1, 1)),
        ],
    )
    def test_validate_unique_sequence_raises(self, input_value):
        with pytest.raises(ValueError):
            validate_unique_sequence(input_value)
