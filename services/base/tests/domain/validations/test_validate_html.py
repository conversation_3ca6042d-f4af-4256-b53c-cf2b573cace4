import pytest

from services.base.domain.validations.validate_html import validate_html


class TestValidateHtml:

    def test_validate_html_should_pass(self):
        valid_html_as_string = "<p>test ahoj</p>"
        result = validate_html(html_as_str=valid_html_as_string)

        assert result == valid_html_as_string

    def test_validate_html_should_auto_correct(self):
        repairable_html = "<p>test ahoj"
        result = validate_html(html_as_str=repairable_html)

        assert result == "<p>test ahoj</p>"

    def test_validate_html_should_throw_an_exception(self):
        invalid_html = "<stron>test ahoj</stron>"

        with pytest.raises(ValueError):
            validate_html(html_as_str=invalid_html)
