import pytest

from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.node_query import NodeQuery
from services.base.domain.schemas.query.validators.node_query_validator import NodeQueryValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.type_tree.type_tree import TreeNode, TypeTree


class TestNodeQueryValidation:
    """Test the NodeQuery validation with TypeTree integration."""

    def test_node_query_validation_with_valid_field(self):
        """Test that validation passes for valid field names."""
        # Create a simple TypeTree for testing
        type_tree = TypeTree({"doc.event.nutrition.drink.tea"})
        validator = NodeQueryValidator(type_tree)
        
        # Create nodes
        nodes = [TreeNode.from_path("doc.event.nutrition.drink")]
        
        # Create a query with a valid field (this would need to be a field that exists in drink types)
        # Note: This test would need to be updated with actual valid field names from the data types
        query = ValuesQuery(field_name="timestamp", values=["test"])
        
        # Create NodeQuery
        node_query = NodeQuery(nodes=nodes, query=query)
        
        # This should not raise an exception if the field exists in the descendant data types
        try:
            node_query.validate_with_type_tree(validator)
        except QueryValidationException:
            # Expected if 'timestamp' doesn't exist in the actual data types
            # This test would need real field names to pass
            pass

    def test_node_query_validation_with_invalid_field(self):
        """Test that validation fails for invalid field names."""
        # Create a simple TypeTree for testing
        type_tree = TypeTree({"doc.event.nutrition.drink.tea"})
        validator = NodeQueryValidator(type_tree)
        
        # Create nodes
        nodes = [TreeNode.from_path("doc.event.nutrition.drink")]
        
        # Create a query with an invalid field name
        query = ValuesQuery(field_name="non_existent_field", values=["test"])
        
        # Create NodeQuery
        node_query = NodeQuery(nodes=nodes, query=query)
        
        # This should raise a QueryValidationException
        with pytest.raises(QueryValidationException):
            node_query.validate_with_type_tree(validator)

    def test_node_query_validation_with_no_query(self):
        """Test that validation passes when query is None."""
        # Create a simple TypeTree for testing
        type_tree = TypeTree({"doc.event.nutrition.drink.tea"})
        validator = NodeQueryValidator(type_tree)
        
        # Create nodes
        nodes = [TreeNode.from_path("doc.event.nutrition.drink")]
        
        # Create NodeQuery with no query
        node_query = NodeQuery(nodes=nodes, query=None)
        
        # This should not raise an exception
        node_query.validate_with_type_tree(validator)
