from datetime import datetime
from zoneinfo import ZoneInfo

import pytest
from dateutil.rrule import DAILY, HOURLY, MONTHLY, WEEKLY

from services.base.domain.custom_rrule import CustomRRule


@pytest.mark.parametrize(
    "rule, expected, next_expected",
    [
        (
            CustomRRule(HOURLY, dtstart=datetime(2021, 1, 30, tzinfo=ZoneInfo("UTC")), interval=2),
            datetime(2021, 1, 30, 2, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 1, 30, 4, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(DAILY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC")), interval=3),
            datetime(2021, 1, 18, 10, 30, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 1, 21, 10, 30, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(WEEKLY, dtstart=datetime(2021, 1, 31, tzinfo=ZoneInfo("UTC")), interval=1),
            datetime(2021, 2, 7, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 2, 14, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(WEEKLY, dtstart=datetime(2021, 1, 31, tzinfo=ZoneInfo("UTC")), interval=1, byweekday=[2, 3]),
            datetime(2021, 2, 3, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 2, 4, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(MONTHLY, dtstart=datetime(2021, 1, 15, tzinfo=ZoneInfo("UTC")), interval=1),
            datetime(2021, 2, 15, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 3, 15, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(MONTHLY, dtstart=datetime(2021, 1, 29, tzinfo=ZoneInfo("UTC")), interval=1),
            datetime(2021, 2, 28, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 3, 29, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(
                MONTHLY, dtstart=datetime(2021, 1, 15, tzinfo=ZoneInfo("UTC")), interval=1, bymonthday=[15, 16]
            ),
            datetime(2021, 1, 16, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 2, 15, tzinfo=ZoneInfo("UTC")),
        ),
        (
            CustomRRule(MONTHLY, dtstart=datetime(2021, 4, 30, tzinfo=ZoneInfo("UTC")), interval=2),
            datetime(2021, 6, 30, tzinfo=ZoneInfo("UTC")),
            datetime(2021, 8, 30, tzinfo=ZoneInfo("UTC")),
        ),
    ],
)
def test_after_passes(rule, expected, next_expected):
    next_occurrence = rule.after(dt=rule.started_at)
    assert next_occurrence == expected
    next_occurrence = rule.after(dt=next_occurrence)
    assert next_occurrence == next_expected


@pytest.mark.parametrize(
    "rule",
    [
        CustomRRule(WEEKLY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC"))),
        CustomRRule(DAILY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC"))),
        CustomRRule(DAILY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC")), byweekday=[1, 2]),
        CustomRRule(DAILY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC")), bymonthday=[1, 2]),
        CustomRRule(DAILY, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC")), bysetpos=[1, 2]),
    ],
)
def test_str_and_from_str_passes(rule):
    rule_str = str(rule)
    assert f"DTSTART:{rule.started_at.isoformat()}" in rule_str
    new_rule = CustomRRule.from_rrule(rule_str)
    assert new_rule.started_at.isoformat() == rule.started_at.isoformat()
    if rule.by_weekday:
        assert "BYDAY" in rule_str
        assert rule.by_weekday == new_rule.by_weekday
    else:
        assert "BYDAY" not in rule_str

    if rule.by_monthday:
        assert "BYMONTHDAY" in rule_str
        assert rule.by_monthday == new_rule.by_monthday
    else:
        assert "BYMONTHDAY" not in rule_str

    if rule.by_setpos:
        assert "BYSETPOS" in rule_str
        assert rule.by_setpos == new_rule.by_setpos
    else:
        assert "BYSETPOS" not in rule_str
