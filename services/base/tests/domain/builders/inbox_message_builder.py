from __future__ import annotations

from datetime import datetime, timezone
from typing import List
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.schemas.identity import Identity
from services.base.domain.schemas.inbox.context_models import Generic<PERSON>ontext
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.tests.domain.builders.identity_builder import IdentityBuilder


class InboxMessageBuilder:
    def __init__(self):
        self._destination: Identity | None = None
        self._title: str | None = None
        self._status: InboxMessageStatus | None = None

    def build(self) -> InboxMessage:
        return InboxMessage(
            timestamp=datetime.now(timezone.utc),
            destination=self._destination or IdentityBuilder().build(),
            sender=IdentityBuilder().build(),
            title=self._title or PrimitiveTypesGenerator.generate_random_string(),
            message=PrimitiveTypesGenerator.generate_random_string(),
            context=GenericContext(type=PrimitiveTypesGenerator.generate_random_string()),
            status=self._status or PrimitiveTypesGenerator.generate_random_enum(InboxMessageStatus),
            is_urgent=PrimitiveTypesGenerator.generate_random_bool(),
            id=uuid4(),
        )

    def build_n(self, n: int | None = None) -> List[InboxMessage]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_destination(self, destination: Identity) -> InboxMessageBuilder:
        self._destination = destination
        return self

    def with_title(self, title: str) -> InboxMessageBuilder:
        self._title = title
        return self

    def with_status(self, status: InboxMessageStatus) -> InboxMessageBuilder:
        self._status = status
        return self
