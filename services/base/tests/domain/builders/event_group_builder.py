from typing import Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import UniqueSequenceUUID
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event_group import EventGroup, EventGroupCategory, EventGroupIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class EventGroupBuilder(EventBuilderBase, EventGroupIdentifier):
    def __init__(self):
        super().__init__()
        self._child_ids: UniqueSequenceUUID = list()

    def build(self) -> EventGroup:
        return EventGroup(
            id=uuid4(),
            type=DataType.EventGroup,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            submission_id=self._submission_id or uuid4(),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=EventGroupCategory),
            template_id=None,
            asset_references=self._asset_references,
            plan_extension=None,
            group_id=None,
            end_time=None,
            tags=[],
            child_ids=self._child_ids,
        )

    def build_n(self, n: int | None = None) -> Sequence[EventGroup]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_child_ids(self, child_ids: Sequence[UUID]) -> "EventGroupBuilder":
        self._child_ids = child_ids
        return self
