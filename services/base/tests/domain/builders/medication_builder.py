from typing import Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.medication.medication import (
    Medication,
    MedicationCategory,
    MedicationIdentifier,
    SingleDoseInformation,
    WeightUnit,
)
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class MedicationBuilder(EventBuilderBase, MedicationIdentifier):

    def build(self) -> Medication:
        return Medication(
            type=DataType.Medication,
            template_id=None,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=MedicationCategory),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            submission_id=self._submission_id or uuid4(),
            group_id=self._group_id,
            id=uuid4(),
            asset_references=self._asset_references,
            single_dose_information=SingleDoseInformation(amount=1, amount_unit=VolumeUnit.ML, items_quantity=10),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=100),
            consume_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            note=PrimitiveTypesGenerator.generate_random_string(),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Medication]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
