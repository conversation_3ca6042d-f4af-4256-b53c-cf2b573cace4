from __future__ import annotations

from datetime import datetime
from uuid import UUID

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.value_limits import HeartRateValueLimit
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.tests.domain.builders.metadata_builder import MetadataBuilder


class HeartRateBuilder:
    def __init__(self):
        self._metadata: MetadataBuilder = MetadataBuilder()
        self._timestamp: datetime | None = None

    def build(self) -> HeartRate:
        bpm_max: float = PrimitiveTypesGenerator.generate_random_float(
            min_value=HeartRateValueLimit.MAXIMUM / 2, max_value=HeartRateValueLimit.MAXIMUM
        )
        bpm_min: float = PrimitiveTypesGenerator.generate_random_float(
            min_value=HeartRateValueLimit.MINIMUM, max_value=HeartRateValueLimit.MAXIMUM / 2
        )
        bpm_avg: float = (bpm_min + bpm_max) / 2
        metadata = self._metadata.build()
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return HeartRate(
            bpm_avg=bpm_avg,
            bpm_max=bpm_max,
            bpm_min=bpm_min,
            timestamp=timestamp,
            metadata=metadata,
        )

    def with_user_uuid(self, user_uuid: UUID) -> HeartRateBuilder:
        self._metadata = self._metadata.with_user_uuid(user_uuid)
        return self

    def with_organization(self, organization: Organization) -> HeartRateBuilder:
        self._metadata = self._metadata.with_organization(organization)
        return self

    def with_timestamp(self, timestamp: datetime) -> HeartRateBuilder:
        self._timestamp = timestamp
        return self
