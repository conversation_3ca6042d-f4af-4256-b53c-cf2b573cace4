from abc import ABC, abstractmethod
from typing import Self, Sequence

from services.base.domain.enums.metadata_v3 import InsertableOrigin
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase
from services.base.tests.domain.builders.builder_base import BuilderBase


class EventPayloadBuilderBase(BuilderBase, TypeIdentifier, ABC):

    def __init__(self):
        self._name: str | None = None
        self._tags: Sequence[str] | None = None
        self._origin: InsertableOrigin | None = None

    @abstractmethod
    def build(self) -> TemplatePayloadBase:
        raise NotImplementedError("Each builder must implement the build method.")

    def with_origin(self, origin: InsertableOrigin) -> Self:
        self._origin = origin
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_tags(self, tags: Sequence[str]) -> Self:
        self._tags = tags
        return self
