from typing import Self, Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.exercise.cardio import Cardio, CardioCategory, CardioIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class CardioBuilder(EventBuilderBase, CardioIdentifier):
    def __init__(self):
        super().__init__()
        self._category: CardioCategory | None = None
        self._distance: float | None = None
        self._elevation: float | None = None

    def build(self) -> Cardio:
        return Cardio(
            type=DataType.Cardio,
            template_id=None,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=CardioCategory),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            distance=self._distance or PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100_000),
            elevation=self._elevation or PrimitiveTypesGenerator.generate_random_float(min_value=-500, max_value=8848),
            rating=PrimitiveTypesGenerator.generate_random_int(max_value=10, min_value=0),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            group_id=self._group_id,
            id=uuid4(),
            asset_references=self._asset_references,
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Cardio]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_category(self, category: CardioCategory) -> Self:
        self._category = category
        return self

    def with_distance(self, distance: float) -> Self:
        self._distance = distance
        return self

    def with_elevation(self, elevation: float) -> Self:
        self._elevation = elevation
        return self
