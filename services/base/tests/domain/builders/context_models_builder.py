import random
from uuid import uuid4

from services.base.application.assets import Assets
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.context_type import ContextType
from services.base.domain.schemas.inbox.context_models import (
    AccountLinkedContext,
    ExportFinishedContext,
    ExtensionFinishedContext,
    GenericContext,
    MessageContext,
)


class ContextModelsBuilder:

    def build(self) -> MessageContext:
        return random.choice(
            (
                ExportFinishedContext(takeout_name=Assets.generate_asset_id(), type=ContextType.EXPORT_FINISHED),
                ExtensionFinishedContext(doc_id=uuid4(), type=ContextType.EXTENSION_FINISHED),
                AccountLinkedContext(type=ContextType.ACCOUNT_LINKED),
                GenericContext(type=PrimitiveTypesGenerator.generate_random_string()),
            )
        )
