import random
from datetime import datetime
from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.models.environment_inputs import (
    WeatherInput,
)
from services.base.domain.schemas.environment import EnvironmentMetadata, EnvironmentSystemProperties
from services.base.domain.schemas.shared import CoordinatesModel
from services.base.domain.schemas.weather import WeatherTemperature, WeatherWind


class WeatherInputBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None
        self._coordinates: CoordinatesModel | None = None

    def build(self) -> WeatherInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return WeatherInput(
            type=DataType.Weather,
            timestamp=timestamp,
            coordinates=self._coordinates or PrimitiveTypesGenerator.get_random_coordinates(),
            metadata=EnvironmentMetadata(provider="OpenMeteo"),
            system_properties=EnvironmentSystemProperties(
                created_at=PrimitiveTypesGenerator.generate_random_aware_datetime()
            ),
            temperature=WeatherTemperature(
                temperature=PrimitiveTypesGenerator.generate_random_float(min_value=-20, max_value=40),
                feels_like=PrimitiveTypesGenerator.generate_random_float(min_value=-25, max_value=45),
            ),
            wind=WeatherWind(
                speed=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=32),
                gust=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=32),
                degree=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=360),
                direction=random.choice(
                    ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE", "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
                ),
            ),
            humidity=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100),
            cloud_cover=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=100),
            uv=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=11),
            pressure=PrimitiveTypesGenerator.generate_random_float(min_value=980, max_value=1050),
            visibility=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100),
            precipitation=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100),
        )

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_coordinates(self, coordinates: CoordinatesModel) -> Self:
        self._coordinates = coordinates
        return self

    def build_n(self, n: int | None = None) -> Sequence[WeatherInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
