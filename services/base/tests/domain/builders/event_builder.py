import random
from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import AssetReference
from services.base.domain.schemas.events.event import Event
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.base.type_resolver import TypeResolver


class EventBuilder(BuilderBase):
    def __init__(self):
        self._submission_id: UUID | None = None
        self._owner_id: UUID | None = None
        self._name: str | None = None
        self._tags: Sequence[str] | None = None
        self._origin: Origin | None = None
        self._group_id: UUID | None = None
        self._asset_references: Sequence[AssetReference] = []
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> Event:
        builder_type = random.choice(TypeResolver.EVENT_BUILDERS)
        return (
            builder_type()
            .with_name(name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32))
            .with_timestamp(timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime())
            .with_end_time(end_time=self._end_time)
            .with_owner_id(owner_id=self._owner_id or uuid4())
            .with_group_id(group_id=self._group_id)
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .with_asset_references(asset_references=self._asset_references)
            .with_tags(tags=self._tags or list())
            .with_submission_id(submission_id=self._submission_id or uuid4())
            .build()
        )

    def build_n(self, n: int | None = None) -> Sequence[Event]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]

    def build_all(self, n: int | None = None) -> Sequence[Event]:
        out = []
        for builder_type in TypeResolver.EVENT_BUILDERS:
            out.extend(
                [
                    builder_type()
                    .with_name(name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32))
                    .with_timestamp(
                        timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
                    )
                    .with_end_time(end_time=self._end_time)
                    .with_owner_id(owner_id=self._owner_id or uuid4())
                    .with_group_id(group_id=self._group_id)
                    .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
                    .with_asset_references(asset_references=self._asset_references)
                    .with_tags(tags=self._tags or list())
                    .with_submission_id(submission_id=self._submission_id or uuid4())
                    .build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
                ]
            )
        return out

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_origin(self, origin: Origin) -> Self:
        self._origin = origin
        return self

    def with_tags(self, tags: Sequence[str]) -> Self:
        self._tags = tags
        return self

    def with_group_id(self, group_id: UUID | None) -> Self:
        self._group_id = group_id
        return self

    def with_asset_references(self, asset_references: Sequence[AssetReference]) -> Self:
        self._asset_references = asset_references
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_submission_id(self, submission_id: UUID) -> Self:
        self._submission_id = submission_id
        return self
