from datetime import datetime, timezone
from typing import Sequence
from uuid import uuid4

import pytest

from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.models.environment_inputs import (
    AirQualityInput,
    EnvironmentInputs,
    PollenInput,
    WeatherInput,
)
from services.base.domain.schemas.air_quality import AirQuality
from services.base.domain.schemas.environment import Environment
from services.base.domain.schemas.pollen import Pollen
from services.base.domain.schemas.weather import Weather
from services.base.infrastructure.database.opensearch.repository.os_environment_repository import (
    OSEnvironmentRepository,
)
from services.base.tests.domain.builders.air_quality_input_builder import AirQualityInputBuilder
from services.base.tests.domain.builders.pollen_input_builder import PollenInputBuilder
from services.base.tests.domain.builders.weather_input_builder import WeatherInputBuilder


class TestEnvironmentRepository:
    async def test_environment_repository(self, environment_repository: EnvironmentRepository):
        weather_docs = list(WeatherInputBuilder().build_n())
        air_quality_docs = list(AirQualityInputBuilder().build_n())
        pollen_docs = list(PollenInputBuilder().build_n())

        environment_input_docs: Sequence[EnvironmentInputs] = weather_docs + air_quality_docs + pollen_docs

        # Insert testing
        inserted_docs = await environment_repository.insert(environment_documents=environment_input_docs)

        assert len(inserted_docs) == len(environment_input_docs)

        self.assert_environment_docs(inserted_docs=inserted_docs, expected_docs=environment_input_docs)

        # Search testing
        searched_documents = await environment_repository.search_by_id(
            ids_and_types=[(document.id, type(document)) for document in inserted_docs]
        )

        self.assert_environment_docs(inserted_docs=searched_documents, expected_docs=inserted_docs)

        # Delete testing
        deleted_ids = await environment_repository.delete_by_id(
            ids_and_types=[(document.id, type(document)) for document in inserted_docs]
        )
        for deleted_id in deleted_ids:
            assert deleted_id in [doc.id for doc in inserted_docs]

        re_searched_documents = await environment_repository.search_by_id(
            ids_and_types=[(document.id, type(document)) for document in inserted_docs]
        )
        assert len(re_searched_documents) == 0

    @pytest.mark.parametrize(
        "env_doc,expected_index",
        [
            [
                AirQualityInputBuilder()
                .with_timestamp(datetime(year=2024, month=1, day=1, tzinfo=timezone.utc))
                .build(),
                "wx_air_quality-2024",
            ],
            [
                WeatherInputBuilder().with_timestamp(datetime(year=2024, month=1, day=1, tzinfo=timezone.utc)).build(),
                "wx_weather-2024",
            ],
            [
                PollenInputBuilder().with_timestamp(datetime(year=2024, month=1, day=1, tzinfo=timezone.utc)).build(),
                "wx_pollen-2024",
            ],
        ],
    )
    def test_get_index_from_env_input_boundary(self, env_doc: EnvironmentInputs, expected_index: str):
        index = OSEnvironmentRepository._get_index_from_env_input_boundary(env_input=env_doc)
        assert index == expected_index

    @pytest.mark.parametrize(
        "doc_id,domain_type,expected_index",
        [
            [
                str(uuid4()) + ".2024-01-01",
                AirQuality,
                "wx_air_quality-2024",
            ],
            [
                str(uuid4()) + ".2024-01-01",
                Weather,
                "wx_weather-2024",
            ],
            [
                str(uuid4()) + ".2024-01-01",
                Pollen,
                "wx_pollen-2024",
            ],
        ],
    )
    def test_get_index_from_id_and_type(self, doc_id: str, domain_type: type[Environment], expected_index: str):
        index = OSEnvironmentRepository._get_index_from_id_and_type(doc_id=doc_id, domain_type=domain_type)
        assert index == expected_index

    def assert_environment_docs(
        self, inserted_docs: Sequence[Environment], expected_docs: Sequence[EnvironmentInputs | Environment]
    ):
        for inserted_doc, expected_doc in zip(
            sorted(inserted_docs, key=lambda doc: doc.timestamp),
            sorted(expected_docs, key=lambda doc: doc.timestamp),
        ):
            if isinstance(expected_doc, Environment):
                assert inserted_doc.id == expected_doc.id
            assert inserted_doc.timestamp == expected_doc.timestamp
            assert inserted_doc.coordinates.latitude == expected_doc.coordinates.latitude
            assert inserted_doc.coordinates.longitude == expected_doc.coordinates.longitude

            if isinstance(expected_doc, WeatherInput) and isinstance(inserted_doc, Weather):
                self.assert_weather_fields(inserted_doc, expected_doc)
            elif isinstance(expected_doc, PollenInput) and isinstance(inserted_doc, Pollen):
                self.assert_pollen_fields(inserted_doc, expected_doc)
            elif isinstance(expected_doc, AirQualityInput) and isinstance(inserted_doc, AirQuality):
                self.assert_air_quality_fields(inserted_doc, expected_doc)

    @staticmethod
    def assert_weather_fields(returned_doc: Weather, expected_doc: WeatherInput):
        assert returned_doc.temperature == expected_doc.temperature
        assert returned_doc.wind == expected_doc.wind
        assert returned_doc.humidity == expected_doc.humidity
        assert returned_doc.cloud_cover == expected_doc.cloud_cover
        assert returned_doc.uv == expected_doc.uv
        assert returned_doc.pressure == expected_doc.pressure
        assert returned_doc.visibility == expected_doc.visibility
        assert returned_doc.precipitation == expected_doc.precipitation

    @staticmethod
    def assert_pollen_fields(returned_doc: Pollen, expected_doc: PollenInput):
        assert returned_doc.tree == expected_doc.tree
        assert returned_doc.weed == expected_doc.weed
        assert returned_doc.grass == expected_doc.grass

    @staticmethod
    def assert_air_quality_fields(returned_doc: AirQuality, expected_doc: AirQualityInput):
        assert returned_doc.pollutants == expected_doc.pollutants
        assert returned_doc.aqi == expected_doc.aqi
