from enum import StrEnum
from typing import Sequence

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel


class SortMode(StrEnum):
    MIN = "min"
    MAX = "max"
    SUM = "sum"
    AVG = "avg"
    MEDIAN = "median"


class SortOrder(StrEnum):
    ASCENDING = "ascending"
    DESCENDING = "descending"


class Sort(BaseDataModel):
    name: str = Field(min_length=1)
    order: SortOrder


class CommonSorts:
    @staticmethod
    def score() -> Sort:
        return Sort(name=DocumentLabels.SORT_SCORE, order=SortOrder.DESCENDING)

    @staticmethod
    def internal_id(order: SortOrder = SortOrder.DESCENDING) -> Sort:
        return Sort(name="_id", order=order)

    @staticmethod
    def timestamp(order: SortOrder) -> Sort:
        return Sort(name=DocumentLabels.TIMESTAMP, order=order)

    @staticmethod
    def created_at_and_internal_id() -> Sequence[Sort]:
        created_at = Sort(
            name=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}", order=SortOrder.DESCENDING
        )
        internal_id = CommonSorts.internal_id(order=SortOrder.DESCENDING)

        return [created_at, internal_id]
