import pytest

from services.base.application.utils.encoders import decode_base_64, encode_base_64


@pytest.mark.parametrize(
    "search_sort_input,output_token",
    [
        ("123", "MTIz"),
        (",".join(["123", "154"]), "MTIzLDE1NA=="),
    ],
)
def test_encode_base_64(search_sort_input: str, output_token: str):
    assert encode_base_64(data=search_sort_input) == output_token


@pytest.mark.parametrize(
    "continuation_token,output",
    [
        ("MTIz", "123"),
        ("MTIzLDE1NA==", "123,154"),
    ],
)
def test_decode_base_64(continuation_token: str, output: str):
    assert decode_base_64(encoded_token=continuation_token) == output
