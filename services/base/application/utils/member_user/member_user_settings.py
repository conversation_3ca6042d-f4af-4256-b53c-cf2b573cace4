from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings


async def get_user_timezone(uuid: UUID, member_user_settings_repo: MemberUserSettingsRepository) -> ZoneInfo:
    """Returns member_user_settings.general.timezone (can be None) see its schema"""
    member_user_settings: MemberUserSettings | None = await member_user_settings_repo.get_by_uuid(user_uuid=uuid)
    return member_user_settings.general.timezone if member_user_settings else DEFAULT_TIMEZONE_UTC
