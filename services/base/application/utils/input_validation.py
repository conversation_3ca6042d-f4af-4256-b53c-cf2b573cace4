from datetime import datetime
from typing import Dict, Optional, Protocol, Sequence, Set


def is_none_except_fields(values: Dict, fields_to_skip: Optional[Set] = None) -> bool:
    if not values:
        return True

    for key, value in values.items():
        # skip for fields_to_skip values
        if fields_to_skip and key in fields_to_skip:
            continue

        if value:
            # early return if non-excluded value is not none
            return False
    return True


class OverlappingIntervalsInputProtocol(Protocol):
    timestamp: datetime
    end_time: datetime | None


def are_sorted_entries_intervals_overlapping(
    entry_list: Sequence[OverlappingIntervalsInputProtocol],
) -> bool:
    """Only works with sorted list input"""
    # Iterate over the sorted entries
    for i in range(len(entry_list) - 1):
        current_entry = entry_list[i]
        if not current_entry.end_time:
            continue
        next_entry = entry_list[i + 1]
        if current_entry.end_time > next_entry.timestamp:
            return True

    return False
