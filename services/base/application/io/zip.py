import shutil
from pathlib import Path


class Zip:
    @staticmethod
    def zip_folder(src: Path, dst: Path, zip_name: str) -> Path:
        """Compresses a folder into a zip file.  Returns the result's filename."""
        file_dst = f"{dst}/{zip_name}"
        return Path(shutil.make_archive(root_dir=src, base_name=file_dst, format="zip"))

    @staticmethod
    def unzip(path: Path, dst: Path) -> None:
        """Unzips an archive into a `dst` location"""
        shutil.unpack_archive(filename=path, extract_dir=dst)
