from datetime import datetime, timedelta, timezone
from uuid import UUID, uuid5

from services.base.domain.annotated_types import AssetId
from services.base.domain.enums.assets_enums import AssetType
from settings.app_config import settings
from settings.app_secrets import secrets


class Assets:
    USER_CREDENTIALS_POLICY_NAME = "user_credentials"
    POLICY_EXPIRATION = timedelta(days=100)
    ACCESS_EXPIRATION = timedelta(days=1)
    VALID_MIME_TYPES = {
        AssetType.PDF: ["application/pdf"],
        AssetType.IMAGE: ["image/webp", "image/jpeg"],
        # AssetType.AUDIO: ["audio/mpeg", "audio/x-wav", "audio/aac"],
        # AssetType.VIDEO: ["video/mp4", "video/x-msvideo", "video/x-matroska"],
    }

    @staticmethod
    def generate_user_storage_container_name(user_uuid: UUID) -> str:
        return str(uuid5(user_uuid, secrets.PUBLIC_UUID_HASH_KEY))

    @staticmethod
    def generate_asset_id(name: str | None = None) -> AssetId:
        now = str(int(datetime.now(timezone.utc).timestamp() * 1000))
        return f"{now}-{name}" if name else now

    @staticmethod
    def generate_asset_path(asset_id: AssetId) -> str:
        return f"{settings.ASSETS_DIRECTORY}/{asset_id}"

    @staticmethod
    def generate_export_path(asset_id: AssetId) -> str:
        return f"{settings.EXPORTS_DIRECTORY}/{asset_id}"
