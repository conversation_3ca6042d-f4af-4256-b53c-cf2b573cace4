# -*- coding: utf-8 -*-
import json
from datetime import timedelta
from typing import List, Optional

from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.loaders.location_loader_utils import LocationLoaderUtils
from services.base.application.message_broker_client import Message<PERSON><PERSON>r<PERSON>lient
from services.base.domain.constants.time_constants import SECONDS_IN_HOUR
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.location import Location
from services.base.infrastructure.database.opensearch.opensearch_committer import OpenSearchCommitter


class LocationLoaderBase(LoaderBase):
    """Base loader for location loaders"""

    _source = "location_history"
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)
    COMMIT_ON_ENTRY_COUNT = 1000
    _message_broker_client: MessageBrokerClient

    @classmethod
    def _should_commit(cls, entries: List) -> bool:
        return len(entries) >= cls.COMMIT_ON_ENTRY_COUNT or super()._should_commit(entries)

    def _commit(self, entries: List[str], data_type: Optional[DataType] = None) -> None:
        if len(entries) > 0:
            if data_type is None:
                data_type = self.data_type
            OpenSearchCommitter.bulk_commit(client=self.client, data_type=data_type or self.data_type, entries=entries)
            location_entries = [Location(**json.loads(entry)) for entry in entries]

            LocationLoaderUtils.notify_location_loaded(
                user_uuid=self.user_uuid,
                entries=location_entries,
                message_broker_client=self._message_broker_client,
            )
            del entries[:]
