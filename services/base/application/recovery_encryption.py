from cryptography.fernet import Fernet

from settings.app_secrets import secrets

recovery_fernet = Fernet(secrets.RECOVERY_ENCRYPTION_KEY)


def encrypt(string: str) -> str:
    """Encrypt plain string into a token"""
    return recovery_fernet.encrypt(string.encode()).decode("utf-8")


def decrypt(token: str) -> str:
    """Decrypt encrypted token into a plain string"""
    return recovery_fernet.decrypt(token.encode()).decode("utf-8")
