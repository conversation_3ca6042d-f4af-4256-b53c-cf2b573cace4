from pydantic import Field, model_validator

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel


class TimeRangeInput(BaseDataModel):
    time_gte: SerializableAwareDatetime = Field(alias=DocumentLabels.TIME_GTE)
    time_lte: SerializableAwareDatetime = Field(alias=DocumentLabels.TIME_LTE)

    @model_validator(mode="after")
    def validate_timestamp(self):  # pylint:disable=no-self-argument
        if self.time_gte > self.time_lte:
            raise ValueError("time_gte has to be less than time_lte")

        return self


class TimeIntervalInput(BaseDataModel):
    # We accept patterns of XY where X is an integer and Y equals ms, s, m, h, d
    # Or 1X pattern where X is m, h, d, w, M, q, y
    interval: str = Field(
        ...,
        min_length=2,
        pattern=r"^(1[mhdwMqy]|\d+(ms|s|m|h|d))$",
        alias=DocumentLabels.INTERVAL,
        description="Allowed intervals are NX where N is an integer and X equals ms, s, m, h, d"
        + "OR 1X where X equals m, h, d, w, M, q, y",
    )


class TimeInput(TimeRangeInput, TimeIntervalInput):
    pass
