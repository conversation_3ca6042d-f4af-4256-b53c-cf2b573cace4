from typing import Sequence

from pydantic import AwareDatetime

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.schemas.shared import BaseDataModel


class FrequencyDistributionAggregate(BaseDataModel):
    aggregation_key: float | int | NonEmptyStr
    document_count: int


class CalendarHistogramAggregate(BaseDataModel):
    aggregation_key: NonEmptyStr
    agg_method: NonEmptyStr
    doc_count: int
    value: Rounded6Float | None


class FieldAggregate(BaseDataModel):
    field: NonEmptyStr
    agg_method: NonEmptyStr
    value: Rounded6Float | None


class DateHistogramAggregate(BaseDataModel):
    timestamp: AwareDatetime
    end_time: AwareDatetime
    doc_count: int
    aggregates: Sequence[FieldAggregate]
