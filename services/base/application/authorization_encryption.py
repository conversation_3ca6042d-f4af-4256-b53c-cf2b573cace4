from cryptography.fernet import Fernet

from settings.app_secrets import secrets

authorization_fernet = Fernet(secrets.AUTHORIZATION_ENCRYPTION_KEY)


def encrypt(string: str) -> str:
    """Encrypt plain string into a token"""
    return authorization_fernet.encrypt(string.encode()).decode("utf-8")


def decrypt(token: str) -> str:
    """Decrypt encrypted token into a plain string"""
    return authorization_fernet.decrypt(token.encode()).decode("utf-8")
