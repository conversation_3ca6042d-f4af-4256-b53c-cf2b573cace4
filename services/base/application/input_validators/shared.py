from typing import List, Optional

from pydantic import AwareDatetime, Field, model_validator

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel


class InputTimestampModel(BaseDataModel):
    timestamp: AwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)


class InputTagsModel(BaseDataModel):
    tags: Optional[List[NonEmptyStr]] = Field(default=None, min_length=1, max_length=64, alias=DocumentLabels.TAGS)


class InputAsset(BaseDataModel):
    # TODO: Consider further input asset validation
    content: bytes = Field(min_length=1)
    name: str | None = Field(min_length=1, default=None)


class InputAssetModel(BaseDataModel):
    assets: Optional[List[InputAsset]] = Field(default=None, alias=DocumentLabels.ASSETS, min_length=1)


class InputTimeIntervalModel(InputTimestampModel):
    end_time: Optional[AwareDatetime] = Field(alias=DocumentLabels.END_TIME, default=None)

    @model_validator(mode="after")
    def validate_datetime_range_filter(self):
        if self.end_time:
            if self.timestamp > self.end_time:
                raise ValueError("timestamp has to be less than end_time")

        return self
