from typing import Dict, Type

from services.base.domain.enums.provider import SupportedApiProviders
from services.user_service.application.authorizers.amazon import AmazonOAuth2Authorizer
from services.user_service.application.authorizers.amazon_alexa import AmazonAlexaOAuth2Authorizer
from services.user_service.application.authorizers.fitbit import FitbitOAuth2Authorizer
from services.user_service.application.authorizers.google import GoogleOAuth2Authorizer
from services.user_service.application.use_cases.auth_use_cases.api_auth import ProviderOAuth2ApiAuthorizer

oauth_providers_mapping: Dict[SupportedApiProviders, Type[ProviderOAuth2ApiAuthorizer]] = {
    SupportedApiProviders.GOOGLE: GoogleOAuth2Authorizer,
    SupportedApiProviders.AMAZON: AmazonOAuth2Authorizer,
    SupportedApiProviders.FITBIT: FitbitOAuth2Authorizer,
    SupportedApiProviders.AMAZON_ALEXA: AmazonAlexaOAuth2Authorizer,
}
