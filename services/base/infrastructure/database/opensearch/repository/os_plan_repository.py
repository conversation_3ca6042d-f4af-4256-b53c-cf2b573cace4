import logging
from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch, OpenSearchException
from pydantic import ValidationError

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.filter_types import Te<PERSON><PERSON>ilter
from services.base.application.database.models.filters import Filters
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.application.retry import retry
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNot<PERSON><PERSON><PERSON>ereException
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.goal import Goal
from services.base.domain.schemas.plan.plan import Plan, PlanFields
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.opensearch_index_constants import PLAN_INDEX
from services.base.infrastructure.database.opensearch.opensearch_query_builder import OpenSearchQueryBuilder
from services.base.infrastructure.database.opensearch.opensearch_request_builder import OpenSearchRequestBuilder
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSPlanRepository(PlanRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client: AsyncOpenSearch = client
        self._search_service: DocumentSearchService = search_service

    async def insert(self, plans: Sequence[Plan], force_strong_consistency: bool = False) -> Sequence[Plan]:
        insert_requests: list[dict] = []

        for plan in plans:
            action = {BulkOperation.Create.value: {"_index": PLAN_INDEX, "_id": str(plan.id)}}
            insert_requests.append(action)
            insert_requests.append(
                plan.model_dump(exclude={DocumentLabels.ID})
                | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in plan.tags]}
            )

        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        if bulk_response["errors"]:
            logging.error(f"Error inserting plans Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Create
        )

        return await self.search_by_id(ids=ids)

    async def update(self, plans: Sequence[Plan]) -> Sequence[Plan]:
        update_requests: list[dict] = []
        for plan in plans:
            plan.system_properties.updated_at = datetime.now(timezone.utc)
            action = {BulkOperation.Update.value: {"_index": PLAN_INDEX, "_id": str(plan.id), "retry_on_conflict": 2}}
            request = {
                "doc": {
                    **plan.model_dump(
                        by_alias=True,
                        exclude={
                            DocumentLabels.ID: True,
                            DocumentLabels.RBAC: True,
                            DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT, DocumentLabels.DELETED_AT},
                        },
                    )
                    | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in plan.tags]},
                }
            }

            update_requests.append(action)
            update_requests.append(request)

        bulk_response = await self._os_client.bulk(body=update_requests)
        if bulk_response["errors"]:
            logging.error(f"Error updating plans Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Update
        )
        return await self.search_by_id(ids=ids)

    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[Plan]:
        response = await self._os_client.mget(body={"ids": ids}, index=PLAN_INDEX)
        result: list = []
        for doc in response["docs"]:
            if err := doc.get("error"):
                logging.error(f"Mget search error: {err["reason"]}, id: {doc["_id"]}")
            if doc["found"]:
                source = doc["_source"]
                tags = source[DocumentLabels.TAGS]
                try:
                    if source.get(PlanFields.TYPE) == DataType.Plan:
                        result.append(
                            Plan(
                                **source | {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]},
                                id=doc["_id"],
                            )
                        )
                    elif source.get(PlanFields.TYPE) == DataType.Goal:
                        result.append(
                            Goal(
                                **source | {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]},
                                id=doc["_id"],
                            )
                        )
                    else:
                        raise ShouldNotReachHereException(f"Unsupported plan type: {source.get(PlanFields.TYPE)}")

                except ValidationError as err:
                    logging.error(
                        "failed to deserialize plan",
                        extra={
                            "document_id": doc["_id"],
                            "index": doc["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )

        return result

    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        for doc_id in ids:
            request = {BulkOperation.Delete.value: {"_index": PLAN_INDEX, "_id": str(doc_id)}}

            delete_requests.append(request)

        delete_result = await self._os_client.bulk(body=delete_requests)
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=delete_result, action=BulkOperation.Delete
        )

    @retry(exceptions=OpenSearchException)
    async def search_by_query(
        self,
        query: SingleDocumentTypeQuery[Plan],
        size: int = 1000,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Plan]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        return await self._search_service.search_documents_by_single_query(
            query=query, sorts=sorts, size=size, continuation_token=continuation_token
        )

    async def search_by_content_hash(
        self,
        content_hashes: Sequence[str],
    ) -> Sequence[Plan]:

        filters = Filters()
        filters.must_filters.with_filters([TermsFilter(name=DocumentLabels.CONTENT_HASH, value=content_hashes)])
        query = OpenSearchQueryBuilder().with_filters(filters=filters).build()
        request_builder = OpenSearchRequestBuilder().with_query(query=query).with_size(size=10000)

        response = await self._os_client.search(body=request_builder.build(), index=PLAN_INDEX)
        return await OSResponseParser.to_domain_from_search(data_schema=Plan, response=response)
