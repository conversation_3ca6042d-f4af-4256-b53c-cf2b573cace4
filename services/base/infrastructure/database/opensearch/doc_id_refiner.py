from typing import Sequence
from uuid import UUID

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.records.record import Record
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    ALIAS_LAST_INDEX_POINTER,
    COLLECTION_INDICES_TO_DIGIT_MAP,
    DIGIT_TO_COLLECTION_INDICES_MAP,
)


class DocIdRefiner:
    @classmethod
    def refine_event_ids(cls, events: Sequence[Event], type_to_index_map: dict[DataType, str]) -> Sequence[Event]:
        id_mapping: dict[UUID, UUID] = {}
        groups: list[EventGroup] = []
        linked_events: list[Event] = []

        for event in events:
            original_id = event.id
            refined_id = cls.refine_doc_id(doc_id=original_id, index_name=type_to_index_map[DataType(event.type_id())])
            id_mapping[original_id] = refined_id
            event.id = refined_id

            if isinstance(event, EventGroup):
                groups.append(event)
            if event.group_id:
                linked_events.append(event)

        # update all references using the mapping
        for event in linked_events:
            if event.group_id and event.group_id in id_mapping:
                event.group_id = id_mapping[event.group_id]

        for group in groups:
            if group.child_ids:
                group.child_ids = [id_mapping[child_id] for child_id in group.child_ids]

        return events

    @classmethod
    def refine_record_ids[T: Record](cls, records: Sequence[T], type_to_index_map: dict[DataType, str]) -> Sequence[T]:
        for record in records:
            record.id = cls.refine_doc_id(doc_id=record.id, index_name=type_to_index_map[DataType(record.type_id())])
        return records

    @staticmethod
    def get_collection_from_index(index_name: str) -> int:
        base_prefix = "-".join(index_name.split("-")[:-1])
        return COLLECTION_INDICES_TO_DIGIT_MAP[base_prefix]

    @staticmethod
    def get_partition_from_index_name(index_name: str) -> int:
        return int(index_name.split("-")[-1])

    @staticmethod
    def get_index_from_refined_id(doc_id: UUID) -> str:
        doc_id_str = doc_id.hex
        collection = int(doc_id_str[0:2], 16)
        partition = int(doc_id_str[2:4], 16)
        return f"{DIGIT_TO_COLLECTION_INDICES_MAP[collection]}-{partition:06d}"

    @classmethod
    def refine_doc_id(cls, index_name: str, doc_id: UUID) -> UUID:
        cls._validate_index_format(index_name=index_name)

        # Determine collection char and rollover char
        collection = cls.get_collection_from_index(index_name=index_name)
        partition = cls.get_partition_from_index_name(index_name=index_name)

        modified_uuid = f"{cls.to_hexadecimal(collection)}{cls.to_hexadecimal(partition)}{doc_id.hex[4:]}"
        return UUID(modified_uuid)

    @staticmethod
    def to_hexadecimal(current_pointer: int) -> str:
        hx = hex(current_pointer)[2:]
        return "0" + hx if len(hx) == 1 else hx

    @staticmethod
    def _validate_index_format(index_name: str):
        """
        Validate if the index is in the format 'string-0000xx' where xx is between 00 and 61.
        """
        try:
            suffix = int(index_name.split("-")[-1])
        except Exception as error:
            raise ValueError(f"Unexpected index format: {index_name}") from error
        assert int(ALIAS_FIRST_INDEX_POINTER) <= suffix <= int(ALIAS_LAST_INDEX_POINTER), ValueError(
            f"Unexpected index suffix: {suffix}"
        )
