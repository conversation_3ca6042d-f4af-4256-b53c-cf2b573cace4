from typing import Sequence

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.query.boolean_query import (
    And<PERSON><PERSON>y,
    BooleanQuery,
    NotQuery,
    OrQuery,
)
from services.base.domain.schemas.query.leaf_query import Leaf<PERSON><PERSON>y
from services.base.domain.schemas.query.query_operator import QueryOperator
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_translator import (
    LeafQueryTranslator,
)
from services.base.infrastructure.database.opensearch.query_translator.query_util import QueryUtil


class BooleanQueryTranslator:

    @staticmethod
    def translate(boolean_query: BooleanQuery) -> dict:
        if isinstance(boolean_query, AndQuery):
            q = {"bool": {}}
            return BooleanQueryTranslator._translate_and_query(and_query=boolean_query, q=q)
        elif isinstance(boolean_query, OrQuery):
            or_sub_queries: Sequence[dict] = BooleanQueryTranslator._parse_boolean_sub_queries(
                queries=boolean_query.queries
            )
            return {"bool": {"should": or_sub_queries, "minimum_should_match": 1}}
        elif isinstance(boolean_query, NotQuery):
            return BooleanQueryTranslator._translate_not_query(not_query=boolean_query)
        else:
            raise TypeError(
                f"Unexpected boolean query type {type(boolean_query)}. Expected AndQuery, OrQuery or NotQuery"
            )

    @staticmethod
    def _translate_not_query(not_query: NotQuery) -> dict:
        not_sub_queries: Sequence[dict] = BooleanQueryTranslator._parse_boolean_sub_queries(queries=not_query.queries)
        if not_query.operator == QueryOperator.OR:
            return {"bool": {"must_not": not_sub_queries}}
        elif not_query.operator == QueryOperator.AND:
            search_type = "must" if QueryUtil.contains_match_query(boolean_query=not_query) else "filter"
            return {"bool": {"must_not": [{"bool": {search_type: not_sub_queries}}]}}
        else:
            raise ShouldNotReachHereException("Only OR or AND operator supported")

    @staticmethod
    def _translate_and_query(and_query: AndQuery, q: dict) -> dict:
        for query in and_query.queries:
            if isinstance(query, LeafQuery):
                leaf_query_as_dict = LeafQueryTranslator.translate(leaf_query=query)
                clause = "must" if QueryUtil.is_must_clause_required(leaf_query=query) else "filter"
                if clause in q["bool"]:
                    q["bool"][clause].append(leaf_query_as_dict)
                else:
                    q["bool"][clause] = [leaf_query_as_dict]

            elif isinstance(query, BooleanQuery):

                sub_queries: Sequence[dict] = BooleanQueryTranslator._parse_boolean_sub_queries(queries=query.queries)
                if isinstance(query, OrQuery):
                    clause = "must" if '"type":"pattern"' in and_query.model_dump_json() else "filter"
                    sub_or = {"bool": {"should": sub_queries, "minimum_should_match": 1}}
                    if clause in q["bool"]:
                        q["bool"][clause].append(sub_or)
                    else:
                        q["bool"][clause] = [sub_or]
                elif isinstance(query, NotQuery):
                    if "must_not" not in q["bool"]:
                        q["bool"]["must_not"] = sub_queries
                    else:
                        q["bool"]["must_not"].extend(sub_queries)
                elif isinstance(query, AndQuery):
                    q = BooleanQueryTranslator._translate_and_query(and_query=query, q=q)
                else:
                    raise ShouldNotReachHereException("Shouldn't reach here. Internal error")
            else:
                raise ShouldNotReachHereException("Shouldn't reach here. Internal error")
        return q

    @staticmethod
    def _parse_boolean_sub_queries(queries: Sequence[LeafQuery | BooleanQuery]) -> Sequence[dict]:
        result = []
        for query in queries:
            if isinstance(query, LeafQuery):
                result.append(LeafQueryTranslator.translate(leaf_query=query))
            elif isinstance(query, BooleanQuery):
                result.append(BooleanQueryTranslator.translate(boolean_query=query))
            else:
                raise TypeError(
                    f"Unexpected query type {type(query)}. Expected LeafQuery, AndQuery, OrQuery or NotQuery"
                )

        return result
