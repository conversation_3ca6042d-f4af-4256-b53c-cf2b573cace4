from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReach<PERSON>ereException
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.boolean_query import And<PERSON><PERSON><PERSON>, BooleanQuery, NotQuery, OrQuery
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import LeafQ<PERSON>y
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_refiner import Leaf<PERSON><PERSON>yRefiner


class BooleanQueryRefiner:

    @staticmethod
    def refine(boolean_query: BooleanQuery, domain_type: type[Document]) -> BooleanQuery:
        bool_builder = BooleanQueryBuilder()
        for query in boolean_query.queries:
            if isinstance(query, LeafQuery):
                bool_builder.add_query(LeafQueryRefiner.refine(leaf_query=query, domain_type=domain_type))
            elif isinstance(query, BooleanQuery):
                bool_builder.add_query(BooleanQueryRefiner.refine(boolean_query=query, domain_type=domain_type))
            else:
                raise ShouldNotReachHereException(f"Unexpected query type {type(query)}")

        if isinstance(boolean_query, AndQuery):
            return bool_builder.build_and_query()
        elif isinstance(boolean_query, OrQuery):
            return bool_builder.build_or_query()
        elif isinstance(boolean_query, NotQuery):
            return bool_builder.build_not_query(operator=boolean_query.operator)
        else:
            raise ShouldNotReachHereException(f"Unexpected query type {type(boolean_query)}")
