import asyncio
import logging
from asyncio import Semaphore
from typing import Any, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON><PERSON>Search

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNot<PERSON><PERSON><PERSON><PERSON>Exception
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.plan.plan import Plan, PlanFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.type_resolver import TypeResolver

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def get_unique_user_uuid(client: AsyncOpenSearch) -> Sequence[dict[str, Any]]:
    aggs = {"unique_users": {"terms": {"field": "rbac.owner_id", "size": 1000000}}}
    q = {"bool": {"must_not": [{"exists": {"field": "archived_at"}}]}}

    body = {"aggs": aggs, "size": 0, "query": q}
    search_result = await client.search(index="template", body=body)
    return search_result["aggregations"]["unique_users"]["buckets"]


async def get_user_templates(user_uuid: UUID, template_repo: TemplateRepository) -> Sequence[EventTemplate]:
    owner_id_query = CommonLeafQueries.owner_id_value_query(user_uuid)
    not_archived_query = NotQuery(queries=[ExistsQuery(field_name="archived_at")])
    and_query = AndQuery(queries=[owner_id_query, not_archived_query])
    query = Query(type_queries=[TypeQuery(domain_types=[EventTemplate], query=and_query)])
    search_result = await template_repo.search_by_query(query=query, size=10000)

    result: list[EventTemplate] = []
    for template in search_result.documents:
        if isinstance(template, EventTemplate):
            result.append(template)
        else:
            raise ShouldNotReachHereException()

    return result


def find_duplicated_templates(
    template: EventTemplate, all_user_templates: Sequence[EventTemplate]
) -> list[EventTemplate]:
    duplicated_templates = []
    for user_template in all_user_templates:
        if template.id == user_template.id:
            # skipping the same template
            continue

        if (
            template.document_name == user_template.document_name
            and template.document_type == user_template.document_type
            and template.name == user_template.name
        ):
            duplicated_templates.append(user_template)

    return duplicated_templates


def find_the_latest_template(templates: Sequence[EventTemplate]) -> EventTemplate:
    assert len(templates) > 1
    latest = templates[0]
    for template in templates[1:]:
        if template.system_properties.created_at > latest.system_properties.created_at:
            latest = template

    return latest


async def change_template_id_for_plans(
    old_template: EventTemplate, new_template: EventTemplate, plan_repo: PlanRepository, dry_run: bool
):
    template_query = ValuesQuery(field_name=PlanFields.TEMPLATE_ID, values=[str(old_template.id)])
    single_query = SingleDocumentTypeQuery(domain_type=Plan, query=template_query)
    plans_to_change = (await plan_repo.search_by_query(query=single_query, size=10000)).documents

    if plans_to_change:
        for plan in plans_to_change:
            plan.template_id = new_template.id

        if dry_run:
            logger.info(f"Dry run would update {len(plans_to_change)} templates")
        else:
            await plan_repo.update(plans=plans_to_change)


async def change_template_id_for_events(
    old_template: EventTemplate,
    new_template: EventTemplate,
    document_search_service: DocumentSearchService,
    event_repo: EventRepository,
    dry_run: bool,
):
    template_query = ValuesQuery(field_name=PlanFields.TEMPLATE_ID, values=[str(old_template.id)])
    query = Query(type_queries=[TypeQuery(domain_types=TypeResolver.EVENTS_V3, query=template_query)])
    documents = (await document_search_service.search_documents_by_query(query=query, size=10000)).documents
    if documents:
        events: list[Event] = []
        for document in documents:
            if isinstance(document, Event):
                events.append(document)
            else:
                raise ShouldNotReachHereException()

        for event in events:
            event.template_id = new_template.id

        if dry_run:
            logger.info(f"Dry run would update {len(events)} events")
        else:
            await event_repo.update(events=events)


async def handle_user_bucket(
    user_bucket: dict,
    progress: tuple[int, int],
    template_repo: TemplateRepository,
    semaphore: Semaphore,
    plan_repo: PlanRepository,
    document_search_service: DocumentSearchService,
    event_repo: EventRepository,
    dry_run: bool,
) -> None:
    async with semaphore:
        user_uuid = UUID(user_bucket["key"])
        doc_count = user_bucket["doc_count"]
        user_templates = await get_user_templates(user_uuid=user_uuid, template_repo=template_repo)
        assert doc_count == len(user_templates)

        for current_template in user_templates:
            templates_to_remove: list[EventTemplate] = []
            duplicated_templates = find_duplicated_templates(current_template, user_templates)
            if duplicated_templates:
                duplicated_templates.append(current_template)
                latest = find_the_latest_template(duplicated_templates)
                duplicated_templates.remove(latest)
                templates_to_remove.extend(duplicated_templates)

                assert len(templates_to_remove) != 0
                for template in templates_to_remove:
                    await change_template_id_for_plans(
                        old_template=template, new_template=latest, plan_repo=plan_repo, dry_run=dry_run
                    )
                    await change_template_id_for_events(
                        old_template=template,
                        new_template=latest,
                        document_search_service=document_search_service,
                        event_repo=event_repo,
                        dry_run=dry_run,
                    )

                if dry_run:
                    logger.info(f"Dry run would remove {len(templates_to_remove)} templates")
                else:
                    ids = [t.id for t in templates_to_remove]
                    await template_repo.delete_by_id(ids=ids)

        logger.info(f"Finished processing [{progress[0]}/{progress[1]}]")


async def migrate(dry_run: bool):
    semaphore = asyncio.Semaphore(10)
    bootstrapper = DependencyBootstrapper().build()
    client: AsyncOpenSearch = bootstrapper.get(interface=AsyncOpenSearch)
    template_repo: TemplateRepository = bootstrapper.get(interface=TemplateRepository)
    plan_repo: PlanRepository = bootstrapper.get(interface=PlanRepository)
    document_search_service: DocumentSearchService = bootstrapper.get(interface=DocumentSearchService)
    event_repo: EventRepository = bootstrapper.get(interface=EventRepository)

    users_affected = await get_unique_user_uuid(client=client)
    user_size = len(users_affected)
    tasks = [
        asyncio.create_task(
            handle_user_bucket(
                user_bucket=user_bucket,
                progress=(index, user_size),
                template_repo=template_repo,
                semaphore=semaphore,
                event_repo=event_repo,
                document_search_service=document_search_service,
                plan_repo=plan_repo,
                dry_run=dry_run,
            )
        )
        for index, user_bucket in enumerate(users_affected)
    ]
    await asyncio.gather(*tasks)


if __name__ == "__main__":
    asyncio.run(
        migrate(
            dry_run=True,
        )
    )
