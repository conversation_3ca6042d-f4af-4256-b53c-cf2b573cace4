import asyncio
import logging
from typing import Sequence

from opensearchpy import Async<PERSON>penSearch

from services.base.infrastructure.database.opensearch.index_settings.activity import ActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.body_metric import BodyMetricIndexModel
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.index_settings.event_group import EventGroupIndexModel
from services.base.infrastructure.database.opensearch.index_settings.exercise import ExerciseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.medication import MedicationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.note import NoteIndexModel
from services.base.infrastructure.database.opensearch.index_settings.nutrition import NutritionIndexModel
from services.base.infrastructure.database.opensearch.index_settings.person import PersonIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import SleepV3IndexModel
from services.base.infrastructure.database.opensearch.index_settings.symptom import SymptomIndexModel
from services.base.infrastructure.database.opensearch.opensearch_index_constants import OpenSearchIndex
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import (
    update_by_query_by_index_pattern,
)
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

logger = logging.getLogger()
logger.setLevel(logging.INFO)


async def migrate(index_models: Sequence[OpenSearchIndex], dry_run: bool):
    client = get_async_default_os_client()
    for data_type in index_models:
        await add_asset_reference_list(client=client, index_model=data_type, dry_run=dry_run)


async def add_asset_reference_list(client: AsyncOpenSearch, index_model: OpenSearchIndex, dry_run: bool):
    q = {
        "bool": {
            "must_not": [{"exists": {"field": "asset_references"}}],
        }
    }
    if dry_run:
        count = await client.count(index=index_model.name, body={"query": q})
        logger.info(f"[DRY_RUN] Would update {count['count']} documents for {index_model.name}")
        return

    query = {
        "script": {
            "source": """
                    ctx._source.asset_references = [];
                """,
            "lang": "painless",
        },
        "query": q,
    }

    await update_by_query_by_index_pattern(client=client, index_pattern=index_model.name, query=query)

    logger.info("Refactored asset references field.")


if __name__ == "__main__":
    asyncio.run(
        migrate(
            index_models=[
                MedicationIndexModel,
                ActivityIndexModel,
                NoteIndexModel,
                SymptomIndexModel,
                FeelingIndexModel,
                SleepV3IndexModel,
                ExerciseIndexModel,
                PersonIndexModel,
                EventGroupIndexModel,
                NutritionIndexModel,
                BodyMetricIndexModel,
                ContentIndexModel,
            ],
            dry_run=True,
        )
    )
