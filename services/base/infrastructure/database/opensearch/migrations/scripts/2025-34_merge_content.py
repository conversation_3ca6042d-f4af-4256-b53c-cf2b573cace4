import asyncio
import logging

from opensearchpy import AsyncOpenSearch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def run_migration(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    index_name = ContentIndexModel.name

    original_count = (await client.count(index=index_name))["count"]
    logging.info(f"expected documents: {original_count}")

    if dry_run:
        count = (await client.count(index=index_name))["count"]
        logger.info(f"[DRY RUN] would update {count} content documents")
        return

    body = {
        "script": {
            "source": """
                ctx._source.type = "content";
                
                def cat = ctx._source.category;
            
                if (cat == 'audio') {
                    ctx._source.category = 'music';
                } else if (cat == 'podcast') {
                    ctx._source.category = 'podcast';
                } else if (cat == 'audiobook') {
                    ctx._source.category = 'audiobook';
                } else if (cat == 'music') {
                    ctx._source.category = 'music';
                } else if (cat == 'image') {
                    ctx._source.category = 'content';
                } else if (cat == 'text' || cat == 'blog' || cat == 'article') {
                    ctx._source.category = 'article';
                } else if (cat == 'book') {
                    ctx._source.category = 'book';
                } else if (cat == 'video') {
                    ctx._source.category = 'content';
                } else if (cat == 'movie') {
                    ctx._source.category = 'movie';
                } else if (cat == 'tv_show') {
                    ctx._source.category = 'series';
                } else if (cat == 'livestream') {
                    ctx._source.category = 'sport';
                } else if (cat == 'music_video') {
                    ctx._source.category = 'music';
                } else {
                    ctx._source.category = 'content';
                }
            """,
            "lang": "painless",
        }
    }
    await update_by_query_async(client=client, data_type=DataType.Content, query=body)
    final_count = (await client.count(index=index_name))["count"]
    logger.info(f"actual documents: {final_count}, diff {final_count - original_count}")
    await client.close()


if __name__ == "__main__":
    asyncio.run(run_migration(dry_run=True))
