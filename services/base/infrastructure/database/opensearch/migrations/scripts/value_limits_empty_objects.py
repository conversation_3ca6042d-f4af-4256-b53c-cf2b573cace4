"""The usage of this script is to migrate existing data to ensure all existing hashmap like objects are not empty."""

import asyncio
import logging

from opensearchpy import Async<PERSON>penSearch

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

logger = logging.getLogger()
logger.setLevel(logging.INFO)

TARGET_DATA_TYPES = [data_type for data_type in DataType]


async def run_migrations():
    client = get_async_default_os_client()
    for data_type in TARGET_DATA_TYPES:
        await run_migration(client=client, data_type=data_type)


async def run_migration(data_type: DataType, client: AsyncOpenSearch):
    await ensure_objects_not_empty(client=client, data_type=data_type)


async def ensure_objects_not_empty(client: AsyncOpenSearch, data_type: DataType):
    query = {
        "script": {
            "lang": "painless",
            "source": """
                def returnNullIfEmpty(def content) {
                    if (content instanceof HashMap) {
                        boolean allValuesNull = true;
                        for (key in content.keySet()) {
                            if (content[key] != null) {
                                allValuesNull = false;
                                content[key] = returnNullIfEmpty(content[key]);
                            }
                        }
                        if (allValuesNull) {
                            content = null;
                        }
                    }
                    else if (content instanceof ArrayList) {
                        def newList = new ArrayList();
                        for (item in content) {
                            def newObject = returnNullIfEmpty(item);
                            if (newObject == null) {
                                continue;
                            }
                            newList.add(newObject);
                        }
                        if (newList.isEmpty()) {
                            content = null;
                        }
                        else {
                            content = newList;
                        }
                    }
                    else if (content == "") {
                        content = null;
                    }
                    return content;
                }
                
                void updateValues(HashMap content) {
                    for (key in content.keySet()) {
                        content[key] = returnNullIfEmpty(content[key]);
                    }
                }
                
                updateValues(ctx._source);
            """,
        }
    }
    result = await update_by_query_async(
        client=client,
        data_type=data_type,
        requests_per_second=5000,
        query=query,
    )
    logging.info(f"Updated {data_type} data with result: {result}")


if __name__ == "__main__":
    asyncio.run(run_migrations())
