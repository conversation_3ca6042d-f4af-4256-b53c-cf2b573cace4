import asyncio
import json
import logging

from opensearchpy import Async<PERSON>penSearch
from opensearchpy.helpers import async_bulk

from services.base.dependency_bootstrapper import DependencyBootstrapper

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def migrate(dry_run: bool):
    """
    Migrates OpenSearch documents by changing 'analytic_type' from 'DiaryEvents' to 'Nutrition'.
    Uses `search_after` for pagination and performs updates in bulk.

    Args:
        dry_run (bool): If True, prints what would be updated without making actual changes.
                        If False, performs the updates in OpenSearch.
    """
    bootstrapper = DependencyBootstrapper().build()
    os_client = bootstrapper.get(interface=AsyncOpenSearch)

    documents_to_bulk_update = []
    bulk_size = 20
    page_size = 1000
    processed_docs_count = 0
    search_after = None

    logger.info(f"Starting migration (Dry Run: {dry_run})...")

    try:
        while True:
            body = {
                "size": page_size,
                "sort": [{"timestamp": "asc"}],
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"metadata.extension_id": {"value": "d26245a6-16fe-4a96-a4dc-bc31d9336e2a"}}},
                            {"term": {"type": {"value": "extension_result"}}},
                        ]
                    }
                },
            }

            if search_after:
                body["search_after"] = search_after

            response = await os_client.search(index="extension_results*", body=body)
            hits = response["hits"]["hits"]
            if not hits:
                break

            for doc in hits:
                processed_docs_count += 1
                source = doc["_source"]
                output = json.loads(source["output"])

                if output.get("analytic_type") == "DiaryEvents":
                    output["analytic_type"] = "Nutrition"
                    source["output"] = json.dumps(output)

                    if dry_run:
                        logger.info(f"Dry run would update: Document ID='{doc['_id']}', Index='{doc['_index']}'")
                    else:
                        documents_to_bulk_update.append(
                            {"_op_type": "update", "_index": doc["_index"], "_id": doc["_id"], "doc": source}
                        )

                        if len(documents_to_bulk_update) >= bulk_size:
                            logger.info(
                                f"Executing bulk update for {len(documents_to_bulk_update)} documents (Total processed: {processed_docs_count})..."
                            )
                            success_count, failed_items = await async_bulk(os_client, documents_to_bulk_update)
                            if failed_items:
                                logger.error(
                                    f"Bulk update failed for {len(failed_items)} documents. First few failures: {failed_items[:5]}"  # pyright: ignore
                                )
                            else:
                                logger.info(f"Successfully updated {success_count} documents in bulk.")
                            documents_to_bulk_update = []

            search_after = hits[-1]["sort"]

        if documents_to_bulk_update and not dry_run:
            logger.info(
                f"Executing final bulk update for {len(documents_to_bulk_update)} documents (Total processed: {processed_docs_count})..."
            )
            success_count, failed_items = await async_bulk(os_client, documents_to_bulk_update)
            if failed_items:
                logger.error(
                    f"Final bulk update failed for {len(failed_items)} documents. First few failures: {failed_items[:5]}"  # pyright: ignore
                )
            else:
                logger.info(f"Successfully updated {success_count} documents in final bulk.")

    except Exception as e:
        logger.exception(f"An error occurred during migration: {e}")

    logger.info(f"Migration complete. Total documents processed: {processed_docs_count}")
    if dry_run:
        logger.info("No actual changes were made as this was a dry run.")


if __name__ == "__main__":
    asyncio.run(
        migrate(
            dry_run=True,
        )
    )
