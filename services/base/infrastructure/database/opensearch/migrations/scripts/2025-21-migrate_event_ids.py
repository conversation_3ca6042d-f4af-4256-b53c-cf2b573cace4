import asyncio
import logging
from typing import Sequence

from opensearchpy import Async<PERSON><PERSON><PERSON>earch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocIdRefiner
from services.base.infrastructure.database.opensearch.index_settings.activity import ActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.body_metric import BodyMetricIndexModel
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.index_settings.event_group import EventGroupIndexModel
from services.base.infrastructure.database.opensearch.index_settings.exercise import ExerciseIndexModel
from services.base.infrastructure.database.opensearch.index_settings.feeling import FeelingIndexModel
from services.base.infrastructure.database.opensearch.index_settings.medication import MedicationIndexModel
from services.base.infrastructure.database.opensearch.index_settings.note import NoteIndexModel
from services.base.infrastructure.database.opensearch.index_settings.nutrition import NutritionIndexModel
from services.base.infrastructure.database.opensearch.index_settings.person import PersonIndexModel
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import SleepV3IndexModel
from services.base.infrastructure.database.opensearch.index_settings.symptom import SymptomIndexModel
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    COLLECTION_INDICES_TO_DIGIT_MAP,
    OpenSearchIndex,
)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


async def reindex_with_new_id(client: AsyncOpenSearch, index_model: OpenSearchIndex, dry_run: bool):
    partition_hex = "00"
    collection_name = index_model.name
    collection_digit = COLLECTION_INDICES_TO_DIGIT_MAP[collection_name]
    collection_hex = DocIdRefiner.to_hexadecimal(collection_digit)
    id_prefix = f"{collection_hex}{partition_hex}"

    # Let's assume that we only have one partition
    index_name = f"{collection_name}-{ALIAS_FIRST_INDEX_POINTER}"
    count = (await client.count(index=index_name))["count"]

    logging.info(f"Reindexing {count} documents from {index_name} with ID prefix {id_prefix}")
    script = {
        "lang": "painless",
        "source": """
            String uuid = java.util.UUID.randomUUID().toString();
            String newId = params.id_prefix + uuid.substring(4);
            ctx._id = newId;
        """,
        "params": {"id_prefix": id_prefix},
    }

    if dry_run:
        dummy_index = f"dummy-{collection_name}"
        await OSMigrationWrapper._re_create_index_with_mappings(
            index_model=index_model,
            target_index_name=dummy_index,
            client=client,
        )
        await OSMigrationWrapper.reindex_index_to(
            source=index_name,
            destination=dummy_index,
            client=client,
            script=script,
            pipeline=None,
            query=None,
        )

        new_count = (await client.count(index=dummy_index))["count"]
        if count != new_count:
            raise ValueError(f"Count mismatch: {count} != {new_count}")

        logging.info(
            f"[DRY RUN] reindexed {new_count} documents from {index_name} to {dummy_index}\n"
            f"-------------\n"
            f"COUNT_DIFF: {count - new_count}"
        )

        result = await client.indices.delete(index=dummy_index)
        logging.info(f"[DRY RUN] removed index {dummy_index} with result: {result}")
        return

    await OSMigrationWrapper.reindex_index_through_dummy_index(
        index_name=index_name, index_model=index_model, client=client, script=script
    )

    new_count = (await client.count(index=index_name))["count"]
    logging.info(f"reindexed {new_count} documents\n" f"-------------\n" f"COUNT_DIFF: {count - new_count}")


async def run_migrations(index_models: Sequence[OpenSearchIndex], dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)

    for index_model in index_models:
        try:
            await reindex_with_new_id(
                client=client,
                index_model=index_model,
                dry_run=dry_run,
            )
        finally:
            await client.close()


if __name__ == "__main__":
    asyncio.run(
        run_migrations(
            index_models=[
                MedicationIndexModel,
                ActivityIndexModel,
                NoteIndexModel,
                SymptomIndexModel,
                FeelingIndexModel,
                SleepV3IndexModel,
                ExerciseIndexModel,
                PersonIndexModel,
                EventGroupIndexModel,
                NutritionIndexModel,
                BodyMetricIndexModel,
                ContentIndexModel,
            ],
            dry_run=True,
        )
    )
