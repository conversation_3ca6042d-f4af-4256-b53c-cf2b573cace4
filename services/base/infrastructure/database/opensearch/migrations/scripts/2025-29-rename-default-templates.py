import asyncio
import logging

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.template import TemplateFields

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def migrate(dry_run: bool):
    bootstrapper = DependencyBootstrapper().build()
    template_repo: TemplateRepository = bootstrapper.get(interface=TemplateRepository)
    document_search_service: DocumentSearchService = bootstrapper.get(interface=DocumentSearchService)

    not_archived_query = NotQuery(queries=[ExistsQuery(field_name="archived_at")])
    name_query = ValuesQuery(field_name=TemplateFields.NAME, values=["default"])
    query = Query(
        type_queries=[TypeQuery(domain_types=[EventTemplate], query=AndQuery(queries=[not_archived_query, name_query]))]
    )
    expected_amount = await document_search_service.count_by_query(query=query)

    continuation_token = None
    templates_updated = 0
    while True:
        search_result = await template_repo.search_by_query(
            query=query, size=1000, continuation_token=continuation_token
        )
        if len(search_result.documents) == 0:
            break
        event_templates_to_rename = [EventTemplate.map(d) for d in search_result.documents]

        for template in event_templates_to_rename:
            template.name = template.document_name

        if dry_run:
            logger.info(f"Dry run would rename {len(event_templates_to_rename)} default templates")
        else:
            await template_repo.update(templates=event_templates_to_rename)

        templates_updated += len(event_templates_to_rename)
        continuation_token = search_result.continuation_token

    assert templates_updated == expected_amount


if __name__ == "__main__":
    asyncio.run(
        migrate(
            dry_run=True,
        )
    )
