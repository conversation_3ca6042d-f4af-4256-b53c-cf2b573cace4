import asyncio
import logging
from typing import Any, Optional

from opensearchpy import As<PERSON><PERSON><PERSON><PERSON>earch

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.opensearch_index_constants import OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_initializer import OpenSearchInitializer
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataTypeToIndexModelMapping
from services.base.infrastructure.database.opensearch.opensearch_rollover_initializer import (
    OpenSearchRolloverInitializer,
)
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_document_count_async,
)
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import (
    await_no_running_snapshot,
    reindex,
)


class OSMigrationWrapper:
    @staticmethod
    async def update_default_pipeline(
        data_type: DataType, client: AsyncOpenSearch, default_pipeline: Optional[str] = None
    ):
        index_name = DataTypeToIndexModelMapping[data_type].name
        logging.info(f"Updating pipeline to {default_pipeline} in indices for data type: {data_type}")
        try:
            await client.indices.put_settings(index=f"{index_name}*", body={"default_pipeline": default_pipeline})
        except Exception as error:
            logging.warning(f"{index_name}: {error}")

    @staticmethod
    async def get_document_count(data_type: DataType, client: AsyncOpenSearch):
        return await get_document_count_async(data_type=data_type, client=client)

    @staticmethod
    async def reindex_indices(
        data_type: DataType,
        client: AsyncOpenSearch,
        target_index_prefix: str,
        request_timeout: int = 3600,
        requests_per_second: int | None = None,
    ):
        """Reindexes given data type indexes to target index with prefix"""
        index_model = DataTypeToIndexModelMapping[data_type]
        index_name = index_model.name
        logging.info(f"Reindexing data type: {data_type}.")
        indices = await client.indices.get(index=f"{index_name}*")
        for index in indices:
            logging.info(f"Found index: {index}, reindexing.")
            try:
                target_index = f"{target_index_prefix}{index}"

                await await_no_running_snapshot(client=client)
                target_index = await OSMigrationWrapper._re_create_index_with_mappings(
                    target_index_name=target_index,
                    client=client,
                    index_model=index_model,
                )

                query = {"source": {"index": index}, "dest": {"index": target_index}}

                result = await client.reindex(
                    body=query,
                    slices="auto",  # pyright: ignore
                    wait_for_completion=True,  # pyright: ignore
                    refresh=True,  # pyright: ignore
                    request_timeout=request_timeout,  # pyright: ignore
                    requests_per_second=requests_per_second,  # pyright: ignore
                )
                logging.info(f"Reindexed data from {index} to {target_index} with result: {result}")
            except Exception as error:
                logging.exception(f"{index}: {error}")

    @staticmethod
    async def reindex_data_type(
        data_type: DataType,
        client: AsyncOpenSearch,
        request_timeout: int = 3600,
        requests_per_second: int | None = None,
    ):
        """Reindexes given data type indexes through a dummy index"""
        index_model = DataTypeToIndexModelMapping[data_type]
        index_name = index_model.name
        logging.info(f"Reindexing data type: {data_type} through dummy index.")
        index_names = list((await client.indices.get(index=f"{index_name}*")).keys())
        batch_size = 10
        if index_names:
            batches = [index_names[i : i + batch_size] for i in range(0, len(index_names), batch_size)]
            for batch in batches:
                async with asyncio.TaskGroup() as tg:
                    for index_name in batch:
                        tg.create_task(
                            OSMigrationWrapper.reindex_index_through_dummy_index(
                                client=client,
                                index_model=index_model,
                                index_name=index_name,
                                requests_per_second=requests_per_second,
                                request_timeout=request_timeout,
                            )
                        )

    @staticmethod
    async def reindex_index_through_dummy_index(
        index_name: str,
        index_model: OpenSearchIndex,
        client: AsyncOpenSearch,
        request_timeout: int = 3600,
        requests_per_second: int | None = None,
        script: dict | None = None,
        pipeline: str | None = None,
    ):
        """Reindexes given index through a dummy index"""
        try:
            logging.info(f"reindexing index: {index_name}")
            dummy_index_prefix = "dummy_index"
            dummy_index_name = f"{dummy_index_prefix}_{index_name}"

            await OSMigrationWrapper._re_create_index_with_mappings(
                index_model=index_model,
                target_index_name=dummy_index_name,
                client=client,
            )

            await OSMigrationWrapper.reindex_index_to(
                client=client,
                source=index_name,
                destination=dummy_index_name,
                request_timeout=request_timeout,
                requests_per_second=requests_per_second,
                script=script,
                pipeline=pipeline,
            )

            await await_no_running_snapshot(client=client)
            result = await client.indices.delete(index=index_name)
            logging.info(f"Removed index {index_name} with result: {result}")

            if index_model.aliases:
                await OpenSearchRolloverInitializer.initialize_index_model(index_model=index_model)
            elif not index_model.is_splittable:
                await OpenSearchInitializer.initialize_index_model(index_model=index_model)

            await OSMigrationWrapper.reindex_index_to(
                client=client,
                source=dummy_index_name,
                destination=index_model.name,
                request_timeout=request_timeout,
                requests_per_second=requests_per_second,
                pipeline=index_model.pipeline,
                script=None,
                query=None,
            )

            result = await client.indices.delete(index=dummy_index_name)
            logging.info(f"Removed index {dummy_index_name} with result: {result}")
        except Exception as error:
            logging.exception(f"{index_name}: {error}")

    @staticmethod
    async def reindex_index_to(
        client: AsyncOpenSearch,
        source: str,
        destination: str,
        script: dict | None,
        pipeline: str | None,
        query: dict | None = None,
        request_timeout: int = 3600,
        requests_per_second: int | None = None,
    ):
        await await_no_running_snapshot(client=client)
        body: dict[str, Any] = {"source": {"index": source}, "dest": {"index": destination}}
        if query:
            body["source"]["query"] = query
        if script:
            body["script"] = script
        if pipeline:
            body["dest"]["pipeline"] = pipeline
        await reindex(
            client=client,
            query=body,
            request_timeout=request_timeout,
            requests_per_second=requests_per_second,
        )

    @staticmethod
    async def _re_create_index_with_mappings(
        target_index_name: str,
        client: AsyncOpenSearch,
        index_model: OpenSearchIndex,
    ) -> str:
        """Creates target index with same mappings as the original index."""
        if await client.indices.exists(index=target_index_name):
            result = await client.indices.delete(index=target_index_name)
            logging.info(f"Removed index {target_index_name} with result: {result}")

        body = {}
        body["mappings"] = index_model.mappings
        body["settings"] = index_model.settings

        response = await client.indices.create(
            index=target_index_name,
            body=body if body else None,
        )
        while not await client.indices.exists(index=target_index_name):
            await asyncio.sleep(1)
            continue
        logging.info(f"created target index with response: {response}")
        return target_index_name
