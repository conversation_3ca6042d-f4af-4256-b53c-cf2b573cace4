from settings.app_config import settings


def depr_get_default_index_settings():
    return {
        "number_of_shards": 1,
        "number_of_replicas": settings.OS_REPLICA_SHARDS_COUNT,
    }


def get_default_event_index_settings():
    return {
        "number_of_shards": settings.OS_PRIMARY_SHARDS_COUNT,
        "number_of_replicas": settings.OS_REPLICA_SHARDS_COUNT,
    }


def get_default_event_index_rollover_conditions():
    return {
        "min_doc_count": settings.OS_MAXIMUM_DOCUMENT_COUNT,
        "min_size": settings.OS_MAXIMUM_INDEX_STORAGE,
        "copy_alias": False,
    }
