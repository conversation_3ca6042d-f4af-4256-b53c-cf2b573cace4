from opensearchpy import <PERSON><PERSON><PERSON>, Date, Keyword, Object

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.environment import EnvironmentMetadataLabels
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL


def get_environment_common_mapping():
    return {
        DocumentLabels.METADATA: Object(
            properties={
                EnvironmentMetadataLabels.PROVIDER: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            }
        ),
        DocumentLabels.SYSTEM_PROPERTIES: Object(
            properties={
                DocumentLabels.CREATED_AT: Date(),
                DocumentLabels.BACKFILL: Boolean(),
            }
        ),
    }
