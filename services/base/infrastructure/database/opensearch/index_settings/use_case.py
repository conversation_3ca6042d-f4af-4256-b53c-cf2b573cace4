from typing import Any, Dict

from opensearchpy import Date, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.use_case import UseCaseFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_document_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    USE_CASE_INDEX,
    OpenSearchIndex,
)


def get_use_case_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            DocumentLabels.RBAC: Object(properties={DocumentLabels.OWNER_ID: Keyword()}),
            UseCaseFields.NAME: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
            UseCaseFields.ARCHIVED_AT: Date(),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
        }
        | get_document_mapping()
    )


def get_use_case_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


UseCaseIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=USE_CASE_INDEX, mappings=get_use_case_mapping(), settings=get_use_case_settings(), is_splittable=False
)
