from typing import Any, Dict

from opensearchpy import Date, Keyword, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.user_action_log import UserActionLogFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import USER_LOGS_INDEX, OpenSearchIndex


def get_user_logs_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.END_TIME: Date(),
            UserActionLogFields.USER_ACTION: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            UserActionLogFields.FILENAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            UserActionLogFields.LOG_EVENTS: Text(),
            **get_common_mapping(),
            **get_metadata_mapping(),
        }
    )


def get_user_logs_settings() -> Dict[str, Any]:
    return {"default_pipeline": None, **depr_get_default_index_settings()}


UserLogsIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=USER_LOGS_INDEX, mappings=get_user_logs_mapping(), settings=get_user_logs_settings(), is_splittable=False
)
