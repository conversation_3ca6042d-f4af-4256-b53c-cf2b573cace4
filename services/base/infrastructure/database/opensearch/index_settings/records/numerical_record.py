from typing import Any, Dict

from opensearchpy import Double

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_record_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    NUMERICAL_RECORD_INDEX,
    OpenSearchIndex,
)

numerical_record_mapping = {
    DocumentLabels.VALUE: Double(),
}


def get_numerical_record_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(numerical_record_mapping | get_base_record_mapping(), strict_mapping=True)


def get_numerical_record_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": NUMERICAL_RECORD_INDEX,
    }


NumericalRecordIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=NUMERICAL_RECORD_INDEX,
    mappings=get_numerical_record_mapping(),
    settings=get_numerical_record_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[NUMERICAL_RECORD_INDEX],
)
