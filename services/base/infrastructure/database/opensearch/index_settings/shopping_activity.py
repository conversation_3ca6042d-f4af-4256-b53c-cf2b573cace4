from typing import Any, Dict

from opensearchpy import Date, Float, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shopping_activity import ShoppingActivityFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    SHOPPING_ACTIVITY_INDEX,
    OpenSearchIndex,
)


def get_shopping_activity_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            ShoppingActivityFields.ORDER_ID: Keyword(),
            ShoppingActivityFields.ITEM_DETAIL: Object(
                properties={
                    ShoppingActivityFields.CATEGORY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ASIN_ISBN: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.UNSPSC_CODE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.RELEASE_DATE: Date(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.CONDITION: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SELLER: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.LPPU: Float(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.PPPU: Float(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.QTY: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.PAY_TYPE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.PURCHASE_ORDER_NO: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.PO_LINE_NUMBER: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ORDERING_CUSTOMER_EMAIL: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_DATE: Date(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_STREET1: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_STREET2: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_CITY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_STATE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.SHIP_ADDRESS_ZIP: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ORDER_STATUS: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.CARRIER_NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.CARRIER_TRACKING_NO: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ITEM_SUBTOTAL: Float(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ITEM_SUBTOTAL_TAX: Float(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.ITEM_TOTAL: Float(copy_to=OS_LABEL_CATCH_ALL),
                    ShoppingActivityFields.CURRENCY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


def get_shopping_activity_settings() -> Dict[str, Any]:
    return {"default_pipeline": None, **depr_get_default_index_settings()}


ShoppingActivityIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=SHOPPING_ACTIVITY_INDEX,
    mappings=get_shopping_activity_mapping(),
    settings=get_shopping_activity_settings(),
    is_splittable=True,
)
