# DB & Migrations (PostgreSQL)

Currently using SQLAlchemy for ORM and for migrations tool for SQLAlechemy => "alembic"

SQLAlchemy documentation: [https://docs.sqlalchemy.org/](https://docs.sqlalchemy.org/)
Alembic documentation: [https://alembic.sqlalchemy.org/](https://alembic.sqlalchemy.org/)

**Paths and command(s) are described as _relative_ to the project root**

## Installing DB & migrations tools
- open/activate your project virtual environment in project root as a working directory
- `pip install -r ./services/base/infrastructure/database/sql_alchemy/README.md`

## Seeding data
### Automatically
You can tell migrations to load data with `-x` arg `load_data` like:
- `alembic -x load_data=yes upgrade head`

### Manually
- after the migrations are ran, you can run seeders manually
- open/activate your project virtual environment in project root as a working directory
- seed all specified seeders with
   - `python3 ./services/base/infrastructure/database/sql_alchemy/run_seeders.py`
- or if you only want to run some specific seeder, find appropriate class in ./modules/database/seeders e.g.:
   - `python3 ./services/base/infrastructure/database/sql_alchemy/seeders/member_user_seeder.py`

## Running migrations
### Automatic
Migrations are also ran automatically when **`user_service`** is built.

### Manual
First, make sure you have a virtual host record for value of PG_HOST for value of target host
   - most probably it's db01 => localhost(127.0.0.1) therefore in your hosts file:
   - `127.0.0.1	db01`
1. `cd ./services/base/infrastructure/database/sql_alchemy/migrations`
2. `alembic upgrade head`

### Manual downgrade one version down / relative migrations
Relative migrations ARE possible, they are possible for both upgrade && downgrade,
however the most common usage is probably for downgrade when developing the migration.

To dowgrade 1 migration/version back:
`alembic downgrade -1`

Relative upgrade is **not** recommended unless debugging!
(sice it can cause an overlook of the final migration stack effect e.g. 2 are tested, but 3 committed etc.)

More info on [https://alembic.sqlalchemy.org/en/latest/tutorial.html#relative-migration-identifiers](https://alembic.sqlalchemy.org/en/latest/tutorial.html#relative-migration-identifiers)


### Running migrations with additional arguments
`cd ./services/base/infrastructure/database/sql_alchemy/sql_alchemy/migrations`
`alembic -x my_setting=true upgrade head`
- if there are more extra arguments - just stack them like - `-x setting1=somesetting -x setting2=othersetting`
- useful when you want a migration to behave differently based on an argument (typically load/don't load data)

## Creating a migration file with Alembic
0. **ALWAYS PROVIDE REASONABLE MESSAGE! in -m param**
0. Navigate to `services/base/infrastructure/database/sql_alchemy/migrations`
1. `alembic revision -m "doing something with table x"`
2. this will pre-generate file called something like `123somehash456_doing_something_with_table_x.py`
3. use `--autogenerate` tag to automatically generate the upgrade and downgrade functions.
4. manually validate what was actually generated in the migration versions, `upgrade()` and `downgrade()` should always be defined.

Optionally you can react on an extra argument:
- `alembic -x some_extra_arg=some_extra_value upgrade head`
- needed import: `from alembic import context`
- to extract the value: `get_x_argument(as_dictionary=True).get("some_extra_arg", "default_value")`
- the input `-x` arg will always be a **string**
- to convert y, yes, t, true, on and 1 to `1`(int) and n, no, f, false, off and 0 to `0`(int),
you can use `strtobool` - **but be aware it really returns *int*, NOT bool** (regardless of how it's named)
- `from distutils.util import strtobool` - returns 0/1 **int** (**not** bool)


## Troubleshooting
`ValueError: dictionary update sequence element #0 has length 1; 2 is required`
- means you've added `-x` argument but not its value like `alembic -x load_data upgrade head`
- fix: add the value: `alembic -x load_data=yes upgrade head` or `alembic -x load_data=no upgrade head`

`ModuleNotFoundError: No module named 'modules'`
And possibly similar `ModuleNotFoundError` errors are most likely caused by "env.py" not "seeing" the target module,
to fix it add path to project root to the "PYTHONPATH". Quick solution for current shell/command line:
Linux/MacOS: `export PYTHONPATH="/path/to/projectroot:$PYTHONPATH"`
Windows: `set PYTHONPATH=%PYTHONPATH%;C:\path\to\projectroot\`
