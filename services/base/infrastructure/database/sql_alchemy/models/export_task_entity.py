from uuid import UUID

from sqlalchemy import DateTime, ForeignKey, String
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class ExportTaskEntity(BaseEntity):
    __tablename__ = "export_task"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, index=True, nullable=False)
    user_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False), ForeignKey("member_user.user_uuid", ondelete="CASCADE"), index=True, nullable=False
    )
    status: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    takeout_name: Mapped[str] = mapped_column(String(120), nullable=False, index=True)
    completed_at: Mapped[str] = mapped_column(DateTime(timezone=True), nullable=True)
    __mapper_args__ = {"eager_defaults": True}
