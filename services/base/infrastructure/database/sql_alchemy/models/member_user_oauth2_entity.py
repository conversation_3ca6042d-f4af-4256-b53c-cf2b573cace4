from uuid import UUID

from sqlalchemy import Foreign<PERSON><PERSON>, String, Text
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class MemberUserOAuth2Entity(BaseEntity):
    __tablename__ = "member_user_oauth2"
    user_uuid: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("member_user.user_uuid", ondelete="CASCADE"),
        index=True,
        nullable=False,
        primary_key=True,
    )
    provider: Mapped[str] = mapped_column(String(150), nullable=False, primary_key=True)
    provider_user_id: Mapped[str] = mapped_column(Text(), nullable=False)
    refresh_token: Mapped[str] = mapped_column(Text(), nullable=True)
    scope: Mapped[str] = mapped_column(Text(), nullable=True)
    user_data: Mapped[str] = mapped_column(Text(), nullable=True)
