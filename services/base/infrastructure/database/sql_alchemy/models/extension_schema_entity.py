from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, String
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class ExtensionSchemaEntity(BaseEntity):
    __tablename__ = "extension_schema"

    extension_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("extension_detail.extension_id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
        index=True,
    )

    version: Mapped[str] = mapped_column(
        String(length=32),
        ForeignKey("extension_detail.version", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
        index=True,
    )
    input_schema: Mapped[dict] = mapped_column(JSON, nullable=False)
    output_schema: Mapped[dict] = mapped_column(JSON, nullable=False)
