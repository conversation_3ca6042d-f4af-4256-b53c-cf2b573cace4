# type: ignore
from datetime import datetime, timezone
from typing import Any, Dict, Self, Sequence
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped, Session, mapped_column, validates
from sqlalchemy.sql import case, expression
from sqlalchemy_utils import UUIDType

from services.base.domain.constants.time_constants import SECONDS_IN_HOUR
from services.base.domain.enums.provider import SupportedDataProviders
from services.base.domain.enums.upload_states import UploadStates
from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity
from services.base.infrastructure.database.sql_alchemy.sql_alchemy_commons import SqlAlchemyCommons


class UploadStateEntity(BaseEntity):
    __tablename__ = "upload_state"

    user_uuid: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("member_user.user_uuid", ondelete="CASCADE"),
        nullable=False,
        primary_key=True,
    )
    provider: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        primary_key=True,
    )

    state: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        server_default=UploadStates.UNKNOWN.value,
    )

    loading_progress: Mapped[int] = mapped_column(
        Integer(),
        nullable=True,
    )

    internal_filename: Mapped[str] = mapped_column(Text(), nullable=False)
    user_filename: Mapped[str] = mapped_column(Text(), nullable=True)

    idle_mapping = {
        UploadStates.UPLOADED: SECONDS_IN_HOUR * 5,
        UploadStates.EXTRACTED: SECONDS_IN_HOUR * 5,
        UploadStates.EXTRACTING: SECONDS_IN_HOUR * 1,
        UploadStates.UPLOADING: SECONDS_IN_HOUR * 1,
        UploadStates.LOADING: SECONDS_IN_HOUR * 2,
    }

    @hybrid_property
    def in_progress(self):
        # In progress are currently considered states that are not finished and didn't fail
        return self.state not in (UploadStates.FINISHED.value, UploadStates.FAILED.value, UploadStates.CANCELLED.value)

    @in_progress.expression
    def in_progress(cls):  # pylint: disable=no-self-argument
        return case(
            [
                # If state is finished or failed, it's considered NOT in progress
                (cls.state == UploadStates.FINISHED.value, expression.false()),
                (cls.state == UploadStates.CANCELLED.value, expression.false()),
                (cls.state == UploadStates.FAILED.value, expression.false()),
            ],
            else_=expression.true(),
        )

    @hybrid_property
    def last_timestamp(self):
        return self.created_at if self.updated_at is None else self.updated_at

    @last_timestamp.expression
    def last_timestamp(cls):  # pylint: disable=no-self-argument
        return case([(cls.updated_at.is_(None), cls.created_at)], else_=cls.updated_at)

    @classmethod
    def get_upload_state(cls, db_session, user_uuid: UUID, provider: SupportedDataProviders) -> BaseEntity:
        return db_session.get(
            cls,
            {
                cls.user_uuid.name: user_uuid,
                cls.provider.name: provider.value,
            },
        )

    @classmethod
    def is_state_cancelled(cls, db_session, user_uuid: UUID, provider: SupportedDataProviders) -> bool:
        return (
            cls.get_upload_state(db_session=db_session, user_uuid=user_uuid, provider=provider).state
            == UploadStates.CANCELLED.value
        )

    @classmethod
    def get_all_upload_states_for_user_uuid(cls, db_session, user_uuid: UUID) -> Sequence[Self]:
        cls.validate_idle_progress(db_session, user_uuid=user_uuid)
        return db_session.query(cls).filter_by(user_uuid=user_uuid).all()

    @classmethod
    def validate_idle_progress(cls, db_session, user_uuid: UUID):
        """Validates whether given state is idle based on mapping defined."""
        upload_states = db_session.query(cls).filter_by(user_uuid=user_uuid).all()
        for upload_state in upload_states:
            state = UploadStates(upload_state.state)
            # Skip if loading is in final state
            if not upload_state.in_progress:
                continue
            if cls.is_progress_idle(updated_at=upload_state.last_timestamp, state=state):
                cls.invalidate_loading(
                    db_session,
                    user_uuid=user_uuid,
                    provider=SupportedDataProviders(upload_state.provider),
                    state=UploadStates.FAILED,
                )

    @classmethod
    def is_progress_idle(cls, updated_at: datetime, state: UploadStates) -> bool:
        return (datetime.now(timezone.utc) - updated_at).total_seconds() > cls.idle_mapping[UploadStates(state)]

    @classmethod
    def invalidate_loading(
        cls,
        db_session: Session,
        user_uuid: UUID,
        provider: SupportedDataProviders,
        state: UploadStates,
    ) -> bool:
        return UploadStateEntity.update_state_if_eligible(
            db_session,
            user_uuid=user_uuid,
            provider=provider,
            state=state,
        )

    @classmethod
    def update_fields_in_db(
        cls, db_session: Session, user_uuid: UUID, provider: SupportedDataProviders, fields: Dict[str, Any]
    ):
        SqlAlchemyCommons.update_in_database(
            db_session,
            db_class=cls,
            primary_key={
                cls.user_uuid.name: user_uuid,
                cls.provider.name: provider.value,
            },
            values=fields,
        )

    @classmethod
    def update_state_if_eligible(
        cls, db_session: Session, user_uuid: UUID, provider: SupportedDataProviders, state: UploadStates
    ) -> bool:
        if UploadStateEntity.is_state_cancelled(
            db_session=db_session,
            user_uuid=user_uuid,
            provider=provider,
        ):
            return False

        cls.update_fields_in_db(
            db_session,
            user_uuid=user_uuid,
            provider=provider,
            fields={cls.state.name: state.value},
        )
        return True

    @classmethod
    def update_loading_progress_to_db(
        cls, db_session: Session, user_uuid: UUID, provider: SupportedDataProviders, loading_progress: int
    ):
        cls.update_fields_in_db(
            db_session,
            user_uuid=user_uuid,
            provider=provider,
            fields={cls.loading_progress.name: loading_progress},
        )

    @classmethod
    def initialize_upload_state_to_db(cls, db_session: Session, upload_state: BaseEntity):
        SqlAlchemyCommons.insert_or_update(
            db_session=db_session,
            db_class=cls,
            values={
                cls.user_uuid: upload_state.user_uuid,
                cls.provider: upload_state.provider,
                cls.state: upload_state.state,
                cls.internal_filename: upload_state.internal_filename,
                cls.user_filename: upload_state.user_filename,
            },
            on_conflict_update={
                cls.state: upload_state.state,
                cls.internal_filename: upload_state.internal_filename,
                cls.user_filename: upload_state.user_filename,
                # Reset progress when there already ways one
                cls.loading_progress: None,
            },
        )

    @validates("provider")
    def validate_data_provider(self, key, value):  # pylint:disable=unused-argument
        try:
            provider = SupportedDataProviders(value)
        except ValueError:
            provider = None

        assert isinstance(provider, SupportedDataProviders)

        return value

    @validates("state")
    def validate_upload_state(self, key, value):  # pylint:disable=unused-argument
        assert (value is None) or (value in set(state_enum.value for state_enum in UploadStates))

        return value

    @validates("loading_progress")
    def validate_loading_progress(self, key, value):  # pylint:disable=unused-argument
        assert (value is None) or (0 <= value <= 100)  # yep, python supports interval comparison :)

        return value
