"""update user_oauth2_data to generic member user oauth2

Revision ID: 3abe8a22cb0e
Revises: 216a9e86d97b
Create Date: 2021-09-09 00:22:12.931788+00:00

"""

import logging

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from services.base.infrastructure.database.sql_alchemy.db_state_manager import db_state_manager

# revision identifiers, used by Alembic.
revision = "3abe8a22cb0e"
down_revision = "216a9e86d97b"
branch_labels = None
depends_on = None

OLD_OAUTH2_TABLE_NAME = "user_oauth2_data"
NEW_OAUTH2_TABLE_NAME = "member_user_oauth2"

COLUMN_NAME_USER_UUID = "user_uuid"
COLUMN_NAME_PROVIDER = "provider"
COLUMN_NAME_PROVIDER_USER_ID = "provider_user_id"
COLUMN_NAME_REFRESH_TOKEN = "refresh_token"
COLUMN_NAME_SCOPE = "scope"
COLUMN_NAME_USER_DATA = "user_data"
COLUMN_NAME_CREATED_AT = "created_at"
COLUMN_NAME_UPDATED_AT = "updated_at"

PROVIDER_GOOGLE = "google"

OLD_OAUTH2_TABLE = sa.Table(
    OLD_OAUTH2_TABLE_NAME,
    sa.MetaData(),
    sa.Column(COLUMN_NAME_USER_UUID, postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column("google_refresh_token", sa.Text()),
    sa.Column("google_scope", sa.Text()),
    sa.Column(COLUMN_NAME_CREATED_AT, sa.DateTime(timezone=True)),
    sa.Column(COLUMN_NAME_UPDATED_AT, sa.DateTime(timezone=True)),
)

OLD_TO_NEW_OAUTH2_COLUMNS = {
    OLD_OAUTH2_TABLE.columns.user_uuid.name: COLUMN_NAME_USER_UUID,
    OLD_OAUTH2_TABLE.columns.google_refresh_token.name: COLUMN_NAME_REFRESH_TOKEN,
    OLD_OAUTH2_TABLE.columns.google_scope.name: COLUMN_NAME_SCOPE,
    OLD_OAUTH2_TABLE.columns.created_at.name: COLUMN_NAME_CREATED_AT,
    OLD_OAUTH2_TABLE.columns.updated_at.name: COLUMN_NAME_UPDATED_AT,
}

LOGIN_GOOGLE_TABLE = sa.Table(
    "login_google",
    sa.MetaData(),
    sa.Column("google_id", sa.Text()),
    sa.Column(COLUMN_NAME_USER_UUID, postgresql.UUID(as_uuid=True)),
)


def upgrade():
    NEW_OAUTH2_TABLE = op.create_table(
        NEW_OAUTH2_TABLE_NAME,
        sa.Column(COLUMN_NAME_USER_UUID, postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint([COLUMN_NAME_USER_UUID], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.Column(COLUMN_NAME_PROVIDER, sa.String(length=150), nullable=False),
        sa.Column(COLUMN_NAME_PROVIDER_USER_ID, sa.Text(), nullable=False),
        sa.PrimaryKeyConstraint(COLUMN_NAME_USER_UUID, COLUMN_NAME_PROVIDER, COLUMN_NAME_PROVIDER_USER_ID),
        sa.Column(COLUMN_NAME_REFRESH_TOKEN, sa.Text(), nullable=True),
        sa.Column(COLUMN_NAME_SCOPE, sa.Text(), nullable=True),
        sa.Column(COLUMN_NAME_USER_DATA, sa.Text(), nullable=True),
        sa.Column(COLUMN_NAME_CREATED_AT, sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column(COLUMN_NAME_UPDATED_AT, sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(
        op.f("ix_" + NEW_OAUTH2_TABLE_NAME + "_" + COLUMN_NAME_USER_UUID),
        NEW_OAUTH2_TABLE_NAME,
        [COLUMN_NAME_USER_UUID],
        unique=False,
    )

    #
    # Before old table drop => transfer data
    #

    # 1) Get the data
    with db_state_manager.session_maker(bind=op.get_bind()) as db_session:  # must get alembic's session
        # get all the data from the old table + google_ids (previously not stored in oauth2 table, only in login table)
        results = (
            db_session.query(OLD_OAUTH2_TABLE)
            .join(
                LOGIN_GOOGLE_TABLE,
                LOGIN_GOOGLE_TABLE.columns.user_uuid == OLD_OAUTH2_TABLE.columns.user_uuid,
            )
            .with_entities(OLD_OAUTH2_TABLE, LOGIN_GOOGLE_TABLE.columns.google_id)
            .all()
        )

    # 2) Prepare data for new structure
    new_oauth2_rows = []
    for row in results:
        new_row = {}
        # set not-yet-existing provider
        new_row[NEW_OAUTH2_TABLE.columns.provider.name] = PROVIDER_GOOGLE
        # copy provider_id from old google_id
        new_row[NEW_OAUTH2_TABLE.columns.provider_user_id.name] = getattr(
            row, LOGIN_GOOGLE_TABLE.columns.google_id.name, ""
        )
        for old_column, new_column in OLD_TO_NEW_OAUTH2_COLUMNS.items():
            new_row[new_column] = getattr(row, old_column, "")

        new_oauth2_rows.append(new_row)

    # 3) Bulk insert the data into the new structure
    op.bulk_insert(NEW_OAUTH2_TABLE, new_oauth2_rows)

    # 4) Delete the original table
    op.drop_index("ix_" + OLD_OAUTH2_TABLE_NAME + "_user_uuid", table_name=OLD_OAUTH2_TABLE_NAME)
    op.drop_table(OLD_OAUTH2_TABLE_NAME)

    op.drop_index("ix_amazon_user_user_uuid", table_name="amazon_user")
    op.drop_table("amazon_user")


def downgrade():
    op.create_table(
        "amazon_user",
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("amazon_id", sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column("user_data", sa.TEXT(), server_default=sa.text("'{}'::text"), autoincrement=False, nullable=True),
        sa.Column("user_uuid", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["user_uuid"], ["member_user.user_uuid"], name="amazon_user_user_uuid_fkey", ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("amazon_id", name="amazon_user_pkey"),
    )
    op.create_index("ix_amazon_user_user_uuid", "amazon_user", ["user_uuid"], unique=False)

    op.create_table(
        OLD_OAUTH2_TABLE_NAME,
        sa.Column(COLUMN_NAME_USER_UUID, postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("google_refresh_token", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("google_scope", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            COLUMN_NAME_CREATED_AT,
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(COLUMN_NAME_UPDATED_AT, postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            [COLUMN_NAME_USER_UUID],
            ["member_user.user_uuid"],
            name=OLD_OAUTH2_TABLE_NAME + "_user_uuid_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint(COLUMN_NAME_USER_UUID, name=OLD_OAUTH2_TABLE_NAME + "_pkey"),
    )
    op.create_index(
        "ix_" + OLD_OAUTH2_TABLE_NAME + "_user_uuid", OLD_OAUTH2_TABLE_NAME, [COLUMN_NAME_USER_UUID], unique=False
    )

    logging.warning(
        "Create only: Downgrading to old user_oauth2_data is not data-compatible, this migration only transfers data upwards."
    )

    op.drop_index(op.f("ix_" + NEW_OAUTH2_TABLE_NAME + "_" + COLUMN_NAME_USER_UUID), table_name=NEW_OAUTH2_TABLE_NAME)
    op.drop_table(NEW_OAUTH2_TABLE_NAME)
