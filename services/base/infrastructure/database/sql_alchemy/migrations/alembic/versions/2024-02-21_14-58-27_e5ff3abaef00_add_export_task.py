"""Add export task

Revision ID: e5ff3abaef00
Revises: eb17f839c5e7
Create Date: 2024-02-21 14:58:27.320635+00:00

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e5ff3abaef00"
down_revision = "eb17f839c5e7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "export_task",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("takeout_name", sa.String(length=120), nullable=False),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_export_task_id"), "export_task", ["id"], unique=False)
    op.create_index(op.f("ix_export_task_status"), "export_task", ["status"], unique=False)
    op.create_index(op.f("ix_export_task_user_id"), "export_task", ["user_id"], unique=False)

    # Seems we forgot to add user type index as part of last migration
    op.create_index(op.f("ix_member_user_type"), "member_user", ["type"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_member_user_type"), table_name="member_user")

    op.drop_index(op.f("ix_export_task_user_id"), table_name="export_task")
    op.drop_index(op.f("ix_export_task_status"), table_name="export_task")
    op.drop_index(op.f("ix_export_task_id"), table_name="export_task")
    op.drop_table("export_task")
    # ### end Alembic commands ###
