"""add apple login table

Revision ID: 80967f11e816
Revises: 4fa60c29a24a
Create Date: 2021-06-03 13:49:05.329045+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "80967f11e816"
down_revision = "4fa60c29a24a"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "login_apple",
        sa.Column("apple_id", sa.Text(), nullable=False, primary_key=True),
        sa.Column("user_data", sa.Text(), server_default="{}", nullable=True),
        sa.Column("user_uuid", postgresql.UUID(as_uuid=True), nullable=False, unique=False),
        sa.ForeignKeyConstraint(["user_uuid"], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(op.f("ix_login_apple_user_uuid"), "login_apple", ["user_uuid"], unique=False)


def downgrade():
    op.drop_table("login_apple")
