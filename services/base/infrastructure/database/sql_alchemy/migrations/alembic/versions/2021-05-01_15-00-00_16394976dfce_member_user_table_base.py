"""member user table base

Revision ID: 16394976dfce
Revises:
Create Date: 2021-05-05 13:46:45.847926+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "16394976dfce"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(  # pylint:disable=no-member
        "member_user",
        sa.<PERSON>umn("user_uuid", UUID(as_uuid=True), nullable=False),
        sa.PrimaryKeyConstraint("user_uuid"),
        sa.Column("first_name", sa.String(length=50), nullable=True),
        sa.Column("last_name", sa.String(length=50), nullable=True),
        sa.Column("display_name", sa.String(length=100), nullable=True),
        sa.Column("primary_email", sa.String(length=100), nullable=True),
        sa.Column("picture_link", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("expiration_datetime", sa.DateTime(timezone=True), nullable=True, server_default=None),
    )


def downgrade():
    op.drop_table("member_user")  # pylint:disable=no-member
