from typing import AsyncIterator, Callable, Sequence, Type

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from services.base.domain.repository.wrappers import (
    ReadFromDatabaseWrapper,
)
from services.base.domain.schemas.shared import BaseDataModel
from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchRepositoriesHelper:
    @staticmethod
    async def get[TDomain: BaseDataModel, TEntity: BaseEntity](
        session_maker: Callable[..., AsyncSession],
        wrapper: ReadFromDatabaseWrapper,
        domain_type: Type[TDomain],
        entity_type: Type[TEntity],
    ) -> Sequence[TDomain]:
        query = SqlAlchemyAsyncCommons.build_query_from_wrapper(entity_type=entity_type, wrapper=wrapper)
        entities = await SqlAlchemyAsyncCommons.read_from_databases(
            session_maker=session_maker, query=query, entity_type=entity_type
        )
        return SqlAlchMapper.to_domain(entities=entities, domain_type=domain_type)

    @staticmethod
    async def yield_results[TDomain: BaseDataModel, TEntity: BaseEntity](
        engine: AsyncEngine,
        wrapper: ReadFromDatabaseWrapper,
        domain_type: Type[TDomain],
        entity_type: Type[TEntity],
        size: int = 1000,
    ) -> AsyncIterator[Sequence[TDomain]]:
        query = SqlAlchemyAsyncCommons.build_query_from_wrapper(entity_type=entity_type, wrapper=wrapper)
        async for chunk in SqlAlchemyAsyncCommons.get_streaming_results(
            engine=engine, query=query, entity_type=entity_type, size=size
        ):
            yield SqlAlchMapper.to_domain(entities=chunk, domain_type=domain_type)

    @staticmethod
    async def count[TEntity: BaseEntity](
        session_maker: Callable[..., AsyncSession],
        wrapper: ReadFromDatabaseWrapper,
        entity_type: Type[TEntity],
    ) -> int:
        # Build the count query using the wrapper
        count_query = SqlAlchemyAsyncCommons.build_query_from_wrapper(
            entity_type=entity_type,
            wrapper=wrapper,
        )

        async with session_maker() as db_session:
            result = await db_session.execute(count_query)
            count = result.scalar_one_or_none()
            return count if count is not None else 0
