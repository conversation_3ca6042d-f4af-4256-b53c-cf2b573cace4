from typing import Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from services.base.domain.repository.extension_schema_repository import ExtensionSchemaRepository
from services.base.domain.schemas.extensions.extension_schema import ExtensionSchema
from services.base.infrastructure.database.sql_alchemy.models import ExtensionSchemaEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchExtensionSchemaRepository(ExtensionSchemaRepository):

    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self._session_maker = session_maker

    async def get_by_extension_and_version(self, extension_id: UUID, version: str) -> Optional[ExtensionSchema]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker,
                entity_type=ExtensionSchemaEntity,
                primary_keys=[
                    {
                        ExtensionSchemaEntity.extension_id.name: extension_id,
                        ExtensionSchemaEntity.version.name: version,
                    },
                ],
            ),
            domain_type=ExtensionSchema,
        )
        return next(iter(models), None)

    async def list_extension_versions(self, extension_id: UUID) -> Sequence[ExtensionSchema]:
        return SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker,
                entity_type=ExtensionSchemaEntity,
                primary_keys=[
                    {
                        ExtensionSchemaEntity.extension_id.name: extension_id,
                    }
                ],
            ),
            domain_type=ExtensionSchema,
        )

    async def upsert(self, schemas: Sequence[ExtensionSchema]) -> Sequence[ExtensionSchema]:
        entities = SqlAlchMapper.to_entity(domain_models=schemas, entity_type=ExtensionSchemaEntity)
        return SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.merge(session_maker=self._session_maker, entities=entities),
            domain_type=ExtensionSchema,
        )
