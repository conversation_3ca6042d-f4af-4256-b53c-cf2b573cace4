from datetime import datetime, timedelta, timezone
from typing import AsyncIterator, Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.wrappers import (
    Range,
    ReadFromDatabaseWrapper,
)
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.infrastructure.database.sql_alchemy.models.member_user_entity import MemberUserEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchMemberUserRepository(MemberUserRepository):

    def __init__(self, session_maker: Callable[..., AsyncSession], engine: AsyncEngine):
        self._session_maker = session_maker
        self._engine = engine

    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUser]:
        return await SqlAlchRepositoriesHelper.get(
            session_maker=self._session_maker, wrapper=wrapper, domain_type=MemberUser, entity_type=MemberUserEntity
        )

    async def get_by_uuid(self, user_uuid: UUID) -> Optional[MemberUser]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker, entity_type=MemberUserEntity, primary_keys=[str(user_uuid)]
            ),
            domain_type=MemberUser,
        )
        return next(iter(models), None)

    async def insert_or_update(self, user: MemberUser) -> Optional[MemberUser]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.merge(
                session_maker=self._session_maker,
                entities=SqlAlchMapper.to_entity(domain_models=[user], entity_type=MemberUserEntity),
            ),
            domain_type=MemberUser,
        )
        return next(iter(models), None)

    def yield_results(self, wrapper: ReadFromDatabaseWrapper, size: int = 1000) -> AsyncIterator[Sequence[MemberUser]]:
        return SqlAlchRepositoriesHelper.yield_results(
            engine=self._engine, wrapper=wrapper, domain_type=MemberUser, entity_type=MemberUserEntity, size=size
        )

    async def delete(self, user: MemberUser) -> None:
        await SqlAlchemyAsyncCommons.delete_from_database(
            session_maker=self._session_maker,
            entities=SqlAlchMapper.to_entity(domain_models=[user], entity_type=MemberUserEntity),
        )

    async def count(self, wrapper: ReadFromDatabaseWrapper) -> int:
        return await SqlAlchRepositoriesHelper.count(
            session_maker=self._session_maker, entity_type=MemberUserEntity, wrapper=wrapper
        )

    def yield_active_users(
        self, delta: timedelta = timedelta(days=30), batch_size: int = 50
    ) -> AsyncIterator[Sequence[MemberUser]]:
        return self.yield_results(
            wrapper=ReadFromDatabaseWrapper(
                search_keys={},
                range_filter=Range(
                    field_name="last_logged_at",
                    start_date=datetime.now(timezone.utc) - delta,
                ),
            ),
            size=batch_size,
        )
