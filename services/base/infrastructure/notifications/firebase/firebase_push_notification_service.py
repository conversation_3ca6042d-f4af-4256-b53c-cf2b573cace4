import logging
from typing import Sequence

import firebase_admin
from firebase_admin import credentials
from firebase_admin.exceptions import InvalidArgumentError, NotFoundError
from firebase_admin.messaging import Message, Notification, send, send_each

from services.base.application.notifications.push_notification_models import (
    PushNotificationMessage,
    PushNotificationPublishResponse,
)
from services.base.application.notifications.push_notification_service import PushNotificationService


class FirebasePushNotificationService(PushNotificationService):
    def __init__(self, cred: dict):
        try:
            # initialize_app raises a ValueError if the app is already initialized
            firebase_admin.initialize_app(credential=credentials.Certificate(cert=cred))
        except ValueError:
            # we can ignore the error here, because it means that the Firebase client is already properly initialized
            ...

    def publish(
        self,
        messages: Sequence[PushNotificationMessage],
    ) -> PushNotificationPublishResponse:
        firebase_messages = self._process_messages(messages=messages)
        response = send_each(messages=firebase_messages)

        # collect all exceptions in nested response objects
        errors = [str(e.exception) for e in response.responses]

        return PushNotificationPublishResponse(
            successful=response.success_count,
            failed=response.failure_count,
            errors=errors,
        )

    def is_token_valid(self, token: str) -> bool:
        try:
            # dry_run=True => validate only, no delivery
            send(Message(token=token, data={"test": "1"}), dry_run=True)
            return True
        except (NotFoundError, InvalidArgumentError):
            return False
        except Exception as e:
            logging.error(f"Error validating push notification token {token}, err: {e}")
            return False

    def _process_messages(self, messages: Sequence[PushNotificationMessage]) -> list[Message]:
        result: list[Message] = []

        for message in messages:
            for device_token in message.device_tokens:
                result.append(
                    Message(
                        notification=Notification(
                            title=message.title,
                            body=message.body,
                        ),
                        token=device_token,
                    )
                )
        return result
