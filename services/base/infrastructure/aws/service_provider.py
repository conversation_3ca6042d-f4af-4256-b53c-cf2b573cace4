from typing import Any, Dict

import boto3
from mypy_boto3_lambda import Lambda<PERSON><PERSON>
from mypy_boto3_s3 import S3Client, S3ServiceResource
from mypy_boto3_secretsmanager import SecretsManagerClient
from mypy_boto3_sns import SNSClient, SNSServiceResource
from mypy_boto3_sqs import SQSClient, SQSServiceResource

from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL


class AWSServiceProvider:
    @staticmethod
    def get_common_args() -> Dict[str, Any]:
        return {
            "region_name": settings.AWS_REGION_NAME,
            "use_ssl": not settings.AWS_SELF_HOSTED,
            "endpoint_url": settings.AWS_URL if settings.AWS_URL else None,
            "aws_access_key_id": "x" if settings.RUN_ENV == RUN_ENV_LOCAL else None,
            "aws_secret_access_key": "x" if settings.RUN_ENV == RUN_ENV_LOCAL else None,
        }

    @staticmethod
    def get_sqs_service() -> SQSServiceResource:
        """
        Get SQS service abstraction
        """
        return _sqs_resource

    @staticmethod
    def get_sns_service() -> SNSServiceResource:
        """
        Get SNS service abstraction
        """
        return _sns_resource

    @staticmethod
    def get_sqs_client() -> SQSClient:
        """Get SQS client abstraction"""
        return _sqs_client

    @staticmethod
    def get_sns_client() -> SNSClient:
        """Get SNS client abstraction"""
        return _sns_client

    @staticmethod
    def get_secret_manager_client() -> SecretsManagerClient:
        """Get Secret manager client abstraction."""
        return _secrets_manager_client

    @staticmethod
    def get_lambda_client() -> LambdaClient:
        """Get Secret manager client abstraction."""
        return _lambda_client

    @staticmethod
    def get_s3_client() -> S3Client:
        """Get s3 client abstraction."""
        return _s3_client

    @staticmethod
    def get_s3_service() -> S3ServiceResource:
        """Get s3 service abstraction."""
        return _s3_resource


_sqs_client = boto3.client(service_name="sqs", **AWSServiceProvider.get_common_args())
_sqs_resource = boto3.resource(service_name="sqs", **AWSServiceProvider.get_common_args())

_sns_client = boto3.client(service_name="sns", **AWSServiceProvider.get_common_args())
_sns_resource = boto3.resource(service_name="sns", **AWSServiceProvider.get_common_args())

_secrets_manager_client = boto3.client(
    service_name="secretsmanager",
    region_name=settings.AWS_REGION_NAME,
    use_ssl=True,
)
_lambda_client = boto3.client(service_name="lambda", **AWSServiceProvider.get_common_args())

_s3_client = boto3.client(service_name="s3", **AWSServiceProvider.get_common_args())
_s3_resource = boto3.resource(service_name="s3", **AWSServiceProvider.get_common_args())
