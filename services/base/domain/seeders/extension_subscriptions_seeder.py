from typing import Sequence

from services.base.domain.schemas.extensions.extension_subscriptions import ExtensionSubscriptions
from services.base.domain.seeders.seeder_base import SeederBase
from settings.app_constants import (
    DEMO1_UUID,
)
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


class ExtensionSubscriptionsSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[ExtensionSubscriptions]:
        return [
            ExtensionSubscriptions(extension_id=TREND_INSIGHTS_EXTENSION_ID, user_id=DEMO1_UUID),
            ExtensionSubscriptions(extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, user_id=DEMO1_UUID),
        ]
