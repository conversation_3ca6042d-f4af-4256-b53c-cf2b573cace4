from typing import Sequence

from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings
from services.base.domain.seeders.seeder_base import SeederBase
from settings.app_constants import (
    DEMO1_UUID,
    TEST1_UUID,
    TEST2_UUID,
    TEST3_UUID,
)


class MemberUserSettingsSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[MemberUserSettings]:
        return [
            MemberUserSettings(user_uuid=DEMO1_UUID),
            MemberUserSettings(user_uuid=TEST1_UUID),
            MemberUserSettings(user_uuid=TEST2_UUID),
            MemberUserSettings(user_uuid=TEST3_UUID),
        ]
