import json
from typing import Sequence

from services.base.domain.schemas.member_user.login_google import LoginGoogle
from services.base.domain.seeders.seeder_base import SeederBase
from settings.app_constants import (
    DEMO1_GOOGLE_ID,
    DEMO1_UUID,
    TEST1_GOOGLE_ID,
    TEST1_UUID,
    TEST2_GOOGLE_ID,
    TEST2_UUID,
    TEST3_GOOGLE_ID,
    TEST3_UUID,
)


class LoginGoogleSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[LoginGoogle]:
        demo1_model = LoginGoogle(
            google_id=DEMO1_GOOGLE_ID,
            user_uuid=DEMO1_UUID,
        )
        demo1_model.user_data = json.dumps(
            {
                "iss": "https://accounts.google.com",
                "azp": "************-itutl8v3oj6h971ib2406iaii3ns00o3.apps.googleusercontent.com",
                "aud": "************-itutl8v3oj6h971ib2406iaii3ns00o3.apps.googleusercontent.com",
                "sub": DEMO1_GOOGLE_ID,
                "hd": "llif.org",
                "email": "<EMAIL>",
                "email_verified": True,
                "at_hash": "xFg6xCCYdpwC6oVeqSCsVA",
                "name": "test1 llif",
                "picture": "https://lh3.googleusercontent.com/a/AATXAJwCsrvggO7jeqnQFOt6UpxrbDfWO3iQAxihC6dC=s96-c",
                "given_name": "test1",
                "family_name": "llif",
                "locale": "en",
                "iat": **********,
                "exp": **********,
            }
        )

        test1_model = LoginGoogle(
            google_id=TEST1_GOOGLE_ID,
            user_uuid=TEST1_UUID,
        )

        test1_model.user_data = json.dumps(
            {
                "iss": "https://accounts.google.com",
                "azp": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "aud": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "sub": TEST1_GOOGLE_ID,
                "hd": "llif.org",
                "email": "<EMAIL>",
                "email_verified": True,
                "at_hash": "o4K9j8cgx7qxIWmuTqsTyw",
                "name": "Test 1",
                "picture": "https://lh3.googleusercontent.com/a/AATXAJzeBomjhv4a-WcdpTGJQ81xH-J2c5i4-YCO4H_t=s96-c",
                "given_name": "Test",
                "family_name": "1",
                "locale": "en",
                "iat": **********,
                "exp": **********,
            }
        )
        test2_model = LoginGoogle(
            google_id=TEST2_GOOGLE_ID,
            user_uuid=TEST2_UUID,
        )

        test2_model.user_data = json.dumps(
            {
                "iss": "https://accounts.google.com",
                "azp": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "aud": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "sub": TEST2_GOOGLE_ID,
                "hd": "llif.org",
                "email": "<EMAIL>",
                "email_verified": True,
                "at_hash": "Nl2XT0Ib0XnY6X3W0P6KUg",
                "name": "Test 2",
                "picture": "https://lh3.googleusercontent.com/a/AATXAJxV5ubxyKjO2c492grFZw-yZJ8zwgRJ2sNNKN8b=s96-c",
                "given_name": "Test",
                "family_name": "2",
                "locale": "en",
                "iat": **********,
                "exp": **********,
            }
        )

        test3_model = LoginGoogle(
            google_id=TEST3_GOOGLE_ID,
            user_uuid=TEST3_UUID,
        )

        test3_model.user_data = json.dumps(
            {
                "iss": "https://accounts.google.com",
                "azp": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "aud": "************-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com",
                "sub": TEST3_GOOGLE_ID,
                "hd": "llif.org",
                "email": "<EMAIL>",
                "email_verified": True,
                "at_hash": "0zlhp6ScDifAteS9N1vvQg",
                "name": "Test 3",
                "picture": "https://lh3.googleusercontent.com/a/AATXAJzjY2mFSsenOiiDsgpDjgra2LG5S511zcxQHCM3=s96-c",
                "given_name": "Test",
                "family_name": "3",
                "locale": "en",
                "iat": **********,
                "exp": **********,
            }
        )

        return [demo1_model, test1_model, test2_model, test3_model]
