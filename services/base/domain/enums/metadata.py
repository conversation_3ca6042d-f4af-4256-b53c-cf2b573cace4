from enum import IntEnum, StrEnum


class Organization(StrEnum):
    """
    Currently supported organisations which we can derive data from
    """

    AMAZON = "amazon"
    APPLE = "apple"
    FACEBOOK = "facebook"
    FITBIT = "fitbit"
    GARMIN = "garmin"
    GOOGLE = "google"
    LLIF = "llif"
    NETFLIX = "netflix"
    OURA = "oura"
    BEST_LIFE = "best_life"
    WALMART = "walmart"
    THIRD_PARTY = "third_party"  # TODO Remove when events v2 are gone, right now v2 events can have this org
    UNKNOWN = "unknown"  # Origin has this option so Organization has to have it too


class DataProxy(StrEnum):
    """
    Indentifies the intermediate source of the data
    if it is different from the original company
    """

    APPLE_HEALTH_KIT = "apple_health_kit"
    GOOGLE_FIT = "google_fit"
    AMAZON_ALEXA = "amazon_alexa"


class DataIntegrity(IntEnum):
    """
    Data integrity captures the quality of the data

    VERY HIGH: Cloud to cloud, or mobile to cloud
    HIGH: Data through intermediate proxy like AHK or Google Fit
    MEDIUM: File upload
    LOW: Manual entry
    VERY LOW: Interpolated
    """

    VERY_HIGH = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    VERY_LOW = 1
    UNSET = 0


class DataQuality(IntEnum):
    """Curates the quality of the sensor data"""

    VERY_HIGH = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    VERY_LOW = 1


class Service(StrEnum):
    """Supported third party services"""

    APPLE_HEALTH_KIT = "apple_health_kit"
    ALEXA = "alexa"
    VOICE = "voice"
    DIARY = "diary"
    MEASURE = "measure"
    SEARCH = "search"
    GOOGLE_FIT = "google_fit"


class ServiceConstants:
    VOICE = "voice"
