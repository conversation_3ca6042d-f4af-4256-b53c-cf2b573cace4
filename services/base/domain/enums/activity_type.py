from enum import StrEnum


class ActivityType(StrEnum):
    WALKING = "walking"
    ON_FOOT = "on_foot"
    RUNNING = "running"
    CYCLING = "cycling"
    BOATING = "boating"
    ON_BICYCLE = "on_bicycle"
    SKIING = "skiing"
    IN_VEHICLE = "in_vehicle"
    IN_ROAD_VEHICLE = "in_road_vehicle"
    IN_FOUR_WHEELER_VEHICLE = "in_four_wheeler_vehicle"
    MOTORCYCLING = "motorcycling"
    IN_PASSENGER_VEHICLE = "in_passenger_vehicle"
    IN_BUS = "in_bus"
    IN_RAIL_VEHICLE = "in_rail_vehicle"
    IN_TRAIN = "in_train"
    IN_SUBWAY = "in_subway"
    IN_TRAM = "in_tram"
    SAILING = "sailing"
    IN_FERRY = "in_ferry"
    FLYING = "flying"
    STILL = "still"
    UNKNOWN = "unknown_activity_type"


class ActivityTypeInput(StrEnum):
    WALKING = "WALKING"
    ON_FOOT = "ON_FOOT"
    RUNNING = "RUNNING"
    CYCLING = "CYCLING"
    ON_BICYCLE = "ON_BICYCLE"
    SKIING = "SKIING"
    IN_VEHICLE = "IN_VEHICLE"
    IN_ROAD_VEHICLE = "IN_ROAD_VEHICLE"
    IN_FOUR_WHEELER_VEHICLE = "IN_FOUR_WHEELER_VEHICLE"
    MOTORCYCLING = "MOTORCYCLING"
    IN_PASSENGER_VEHICLE = "IN_PASSENGER_VEHICLE"
    IN_BUS = "IN_BUS"
    IN_RAIL_VEHICLE = "IN_RAIL_VEHICLE"
    IN_TRAIN = "IN_TRAIN"
    IN_SUBWAY = "IN_SUBWAY"
    IN_TRAM = "IN_TRAM"
    SAILING = "SAILING"
    IN_FERRY = "IN_FERRY"
    FLYING = "FLYING"
    STILL = "STILL"
    UNKNOWN = "UNKNOWN_ACTIVITY_TYPE"
