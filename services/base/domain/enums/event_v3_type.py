from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event


class EventV3Type(StrEnum):
    # Content
    Content = DataType.Content

    # Body Metric
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric

    # Feeling
    Emotion = DataType.Emotion
    Stress = DataType.Stress

    # Exercise
    Exercise = DataType.Exercise
    Cardio = DataType.Cardio
    Strength = DataType.Strength

    # Nutrition
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Other
    Activity = DataType.Activity
    Note = DataType.Note
    Symptom = DataType.Symptom
    EventGroup = DataType.EventGroup
    Medication = DataType.Medication
    Sleep = DataType.SleepV3
    Person = DataType.Person
    PlaceVisit = DataType.PlaceVisit

    def to_event_model(self) -> type[Event]:
        from services.base.type_resolver import TypeResolver

        return TypeResolver.get_event(type_id=self)
