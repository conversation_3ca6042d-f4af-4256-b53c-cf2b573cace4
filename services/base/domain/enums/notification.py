# TODO(jaja): Delete with notifications
from enum import StrEnum


class NotificationPriority(StrEnum):
    LOW = "low"
    DEFAULT = "default"
    HIGH = "high"


class NotificationStatus(StrEnum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"
    PINNED = "pinned"


class NotificationType(StrEnum):
    HEALTH = "health"
    LIFESTYLE = "lifestyle"
    SYSTEM = "system"
    WELCOME = "welcome"
