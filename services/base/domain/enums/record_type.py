from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.type_resolver import TypeResolver


class RecordType(StrEnum):
    SleepRecord = DataType.SleepRecord
    StepsRecord = DataType.StepsRecord
    BodyMetricRecord = DataType.BodyMetricRecord

    def to_record_model(self) -> type[TypeResolver.RECORD_UNION]:
        return TypeResolver.get_record(type_id=self)
