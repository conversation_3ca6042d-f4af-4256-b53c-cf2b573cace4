from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.enums.os_task_states import OSTaskStates
from services.base.domain.enums.os_task_types import OSTaskTypes
from services.base.domain.schemas.member_user.member_user_os_tasks import MemberUserOSTasks


class MemberUserOSTasksRepository(ABC):
    @abstractmethod
    async def get_by_task_id(self, task_id: str) -> Optional[MemberUserOSTasks]:
        pass

    @abstractmethod
    async def get_user_tasks(
        self, user_uuid: UUID, task_type: OSTaskTypes, state: Optional[OSTaskStates] = None
    ) -> Sequence[MemberUserOSTasks]:
        pass

    @abstractmethod
    async def insert_or_update(self, user_os_tasks: Sequence[MemberUserOSTasks]) -> Sequence[MemberUserOSTasks]:
        pass

    @abstractmethod
    async def delete(self, user_os_tasks: Sequence[MemberUserOSTasks]):
        pass
