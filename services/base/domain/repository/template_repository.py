from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.application.database.models.sorts import Sort
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.templates.template import Template


class TemplateRepository(ABC):
    @abstractmethod
    async def insert(self, templates: Sequence[Template], force_strong_consistency: bool = False) -> Sequence[Template]:
        pass

    @abstractmethod
    async def update(self, templates: Sequence[Template]) -> Sequence[Template]:
        pass

    @abstractmethod
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[Template]:
        pass

    @abstractmethod
    async def search_by_query(
        self,
        query: Query,
        size: int,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Template]:
        pass

    @abstractmethod
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        pass
