from abc import ABC, abstractmethod
from typing import Optional, Sequence

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.login_google import LoginGoogle


class LoginGoogleRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[LoginGoogle]:
        pass

    @abstractmethod
    async def get_by_google_id(self, google_id: str) -> Optional[LoginGoogle]:
        pass

    @abstractmethod
    async def insert_or_update(self, login_google: LoginGoogle) -> Optional[LoginGoogle]:
        pass

    @abstractmethod
    async def delete(self, login_google: LoginGoogle) -> None:
        pass
