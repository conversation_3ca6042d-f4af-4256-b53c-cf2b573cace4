from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail


class ExtensionDetailRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[ExtensionDetail]:
        pass

    @abstractmethod
    async def delete(self, extension_details: Sequence[ExtensionDetail]) -> None:
        pass

    @abstractmethod
    async def get_by_extension_id(self, extension_id: UUID) -> Optional[ExtensionDetail]:
        pass

    @abstractmethod
    async def upsert(self, extension_details: Sequence[ExtensionDetail]) -> Sequence[ExtensionDetail]:
        pass
