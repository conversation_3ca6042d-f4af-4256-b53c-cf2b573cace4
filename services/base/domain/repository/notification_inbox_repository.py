from abc import ABC, abstractmethod
from typing import Sequence

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.inbox.member_user_notification_inbox import (
    MemberUserNotificationInbox,
)


class NotificationInboxRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserNotificationInbox]:
        pass

    @abstractmethod
    async def upsert(
        self, notifications: Sequence[MemberUserNotificationInbox]
    ) -> Sequence[MemberUserNotificationInbox]:
        pass

    @abstractmethod
    async def get_notifications_by_uuid(
        self, notification_uuids: Sequence[str]
    ) -> Sequence[MemberUserNotificationInbox]:
        pass

    @abstractmethod
    async def get_notification_list(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserNotificationInbox]:
        pass

    @abstractmethod
    async def delete(self, notifications: Sequence[MemberUserNotificationInbox]):
        pass
