from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.application.database.models.sorts import Sort
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery


class UseCaseRepository(ABC):
    @abstractmethod
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]: ...

    @abstractmethod
    async def insert(
        self, use_cases: Sequence[UseCase], force_strong_consistency: bool = False
    ) -> Sequence[UseCase]: ...

    @abstractmethod
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[UseCase]: ...

    @abstractmethod
    async def search_by_query(
        self,
        query: SingleDocumentTypeQuery[UseCase],
        size: int = 1000,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[UseCase]: ...

    @abstractmethod
    async def update(self, use_cases: Sequence[UseCase]) -> Sequence[UseCase]: ...
