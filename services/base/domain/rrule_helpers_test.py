from datetime import datetime
from zoneinfo import ZoneInfo

import pytest
from dateutil.relativedelta import relativedelta
from dateutil.rrule import DAILY, HOURLY, MINUTELY, WEEKLY, rrule

from services.base.domain.rrule_helpers import RRuleHelpers


@pytest.mark.parametrize(
    "freq, interval, expected_rd",
    [
        (MINUTELY, 1, relativedelta(minutes=1)),
        (MINUTELY, 3, relativedelta(minutes=3)),
        (HOURLY, 1, relativedelta(hours=1)),
        (HOURLY, 5, relativedelta(hours=5)),
        (DAILY, 2, relativedelta(days=2)),
        (WEEKLY, 4, relativedelta(weeks=4)),
    ],
)
def test_get_relativedelta_from_rrule_passes(freq, interval, expected_rd):
    rule = rrule(freq, interval=interval, dtstart=datetime(2021, 1, 15, 10, 30, tzinfo=ZoneInfo("UTC")))
    assert rule._interval == interval  # pyright: ignore

    rd = RRuleHelpers.get_relativedelta_from_rrule(rule)
    assert isinstance(rd, relativedelta)
    assert rd == expected_rd
