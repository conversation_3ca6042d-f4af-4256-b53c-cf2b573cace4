from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Literal
from uuid import UUID

from pydantic.fields import Field, computed_field

from services.base.domain.annotated_types import (
    NonEmptyStr,
    SerializableAwareDatetime,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.priority import Priority
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.plan.plan_base import PlanBase, PlanBaseFields, PlanBaseValueLimits


@dataclass(frozen=True)
class PlanFields(PlanBaseFields):
    TYPE = DocumentLabels.TYPE
    TEMPLATE_ID = "template_id"
    PRIORITY = "priority"
    PROMPT = "prompt"
    IS_ABSOLUTE_SCHEDULE = "is_absolute_schedule"
    IS_CONFIRMATION_REQUIRED = "is_confirmation_required"
    IS_URGENT = "is_urgent"
    TOTAL_COMPLETED_ON_TIME = "total_completed_on_time"
    FIRST_COMPLETED_AT = "first_completed_at"


class PlanIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Plan


class Plan(PlanBase, PlanIdentifier):
    type: Literal[DataType.Plan] = Field(alias=PlanFields.TYPE)
    template_id: UUID = Field(alias=PlanFields.TEMPLATE_ID)
    prompt: NonEmptyStr | None = Field(alias=PlanFields.PROMPT, max_length=PlanBaseValueLimits.MAX_PROMPT_LENGTH)
    priority: Priority = Field(alias=PlanFields.PRIORITY)
    is_absolute_schedule: bool = Field(alias=PlanFields.IS_ABSOLUTE_SCHEDULE)
    is_urgent: bool = Field(alias=PlanFields.IS_URGENT)
    is_confirmation_required: bool = Field(alias=PlanFields.IS_CONFIRMATION_REQUIRED)
    total_completed_on_time: int = Field(alias=PlanFields.TOTAL_COMPLETED_ON_TIME, ge=0)
    first_completed_at: SerializableAwareDatetime | None = Field(alias=PlanFields.FIRST_COMPLETED_AT)

    @computed_field
    def total_cycles(self) -> int:
        if not self.recurrence:
            return 1
        return self.calculate_total_plan_cycles(
            recurrence=self.recurrence,
            first_completed_at=self.first_completed_at,
            now=datetime.now(timezone.utc),
        )
