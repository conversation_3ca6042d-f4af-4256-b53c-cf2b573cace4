from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class StressValueLimits:
    STRESS_MINIMUM_VALUE = -5
    STRESS_MAXIMUM_VALUE = 5


@dataclass(frozen=True)
class StressFields(EventFields):
    RATING = "rating"


class StressCategory(StrEnum):
    FOOD_CONSUMPTION = "food_consumption"
    LIQUID_CONSUMPTION = "liquid_consumption"
    MENTAL_ACTIVITY = "mental_activity"
    PHYSICAL_ACTIVITY = "physical_activity"
    SCREEN_TIME = "screen_time"
    SLEEP = "sleep"
    SOCIAL_ACTIVITY = "social_activity"
    STRESS = DataType.Stress
    SUN_EXPOSURE = "sun_exposure"


class StressIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Stress


class Stress(Event, StressIdentifier):
    type: Literal[DataType.Stress] = Field(alias=StressFields.TYPE)
    category: StressCategory = Field(alias=StressFields.CATEGORY, default=StressCategory.STRESS)
    rating: int = Field(
        alias=StressFields.RATING,
        ge=StressValueLimits.STRESS_MINIMUM_VALUE,
        le=StressValueLimits.STRESS_MAXIMUM_VALUE,
    )
