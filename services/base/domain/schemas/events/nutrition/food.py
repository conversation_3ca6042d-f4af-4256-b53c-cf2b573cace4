from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class FoodCategory(StrEnum):
    MEAT = "meat"
    FISH = "fish"
    EGGS = "eggs"
    DAIRY = "dairy"
    FRUITS = "fruits"
    POULTRY = "poultry"
    SEAFOOD = "seafood"
    LEGUMES = "legumes"
    READY_MEAL = "ready_meal"
    VEGETABLES = "vegetables"
    WHOLE_GRAIN = "whole_grain"
    FATS_AND_OILS = "fats_and_oils"
    HOMEMADE_MEAL = "homemade_meal"
    FAST_FOOD_MEAL = "fast_food_meal"
    REFINED_GRAINS = "refined_grains"
    RESTAURANT_MEAL = "restaurant_meal"
    SWEETS_AND_DESSERTS = "sweets_and_desserts"
    CONDIMENTS_SEASONINGS = "condiments_seasonings"
    PLANT_BASED_ALTERNATIVES = "plant_based_alternatives"
    SNACKS = "snacks"
    OTHER = "other"


class FoodIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Food


class Food(NutritionCollection, Event, FoodIdentifier):
    type: Literal[DataType.Food] = Field(alias=NutritionFields.TYPE)
    category: FoodCategory = Field(alias=NutritionFields.CATEGORY, default=FoodCategory.OTHER)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
