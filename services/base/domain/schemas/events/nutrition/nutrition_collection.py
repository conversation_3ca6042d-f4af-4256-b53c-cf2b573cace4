from dataclasses import dataclass

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class NutritionFields(EventFields):
    NOTE = "note"
    UNIT = "unit"
    BRAND = "brand"
    RATING = "rating"
    CALORIES = "calories"
    NUTRIENTS = "nutrients"
    CONSUMED_TYPE = "consumed_type"
    CONSUMED_AMOUNT = "consumed_amount"
    FLAVOR = "flavor"


@dataclass(frozen=True)
class NutritionValueLimits:
    MAX_UNIT_LENGTH = 32
    MAX_ITEMS_CONSUMED = 1000
    MIN_ITEMS_CONSUMED = 0
    MAX_ITEMS_PER_SERVING = 1000
    MIN_ITEMS_PER_SERVING = 0
    MAX_CONSUMED_AMOUNT = 25000
    MIN_CONSUMED_AMOUNT = 0
    MAX_CALORIES = 10000
    MIN_CALORIES = 0
    MAX_AMOUNT = 25000
    MIN_AMOUNT = 0
    MAX_BRAND_NAME = 64
    MAX_FLAVOUR_LENGTH = 128


class NutritionCollection(BaseDataModel):
    brand: NonEmptyStr | None = Field(
        alias=NutritionFields.BRAND,
        max_length=NutritionValueLimits.MAX_BRAND_NAME,
    )
    rating: int | None = Field(
        alias=NutritionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=NutritionFields.NOTE,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
    consumed_amount: Rounded6Float = Field(
        alias=NutritionFields.CONSUMED_AMOUNT,
        ge=NutritionValueLimits.MIN_CONSUMED_AMOUNT,
        le=NutritionValueLimits.MAX_CONSUMED_AMOUNT,
    )
    calories: Rounded6Float | None = Field(
        alias=NutritionFields.CALORIES,
        ge=NutritionValueLimits.MIN_CALORIES,
        le=NutritionValueLimits.MAX_CALORIES,
    )
    flavor: NonEmptyStr | None = Field(
        alias=NutritionFields.FLAVOR,
        max_length=NutritionValueLimits.MAX_FLAVOUR_LENGTH,
    )
    nutrients: Nutrients | None = Field(alias=NutritionFields.NUTRIENTS)
