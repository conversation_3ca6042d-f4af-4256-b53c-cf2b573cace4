from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class DrinkCategory(StrEnum):
    TEA = "tea"
    MILK = "milk"
    WINE = "wine"
    BEER = "beer"
    WATER = "water"
    COFFEE = "coffee"
    SMOOTHIE = "smoothie"
    SPIRITS = "spirits"
    PLANT_BASED = "plant_based"
    SOFT_DRINKS = "soft_drinks"
    ENERGY_DRINKS = "energy_drinks"
    PROTEIN_SHAKE = "protein_shake"
    JUICES_FROM_FRUIT = "juices_from_fruit"
    JUICES_FROM_VEGETABLE = "juices_from_vegetable"
    OTHER = "other"


class DrinkIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Drink


class Drink(NutritionCollection, Event, DrinkIdentifier):
    type: Literal[DataType.Drink] = Field(alias=NutritionFields.TYPE)
    category: DrinkCategory = Field(alias=NutritionFields.CATEGORY, default=DrinkCategory.OTHER)
    consumed_type: VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
