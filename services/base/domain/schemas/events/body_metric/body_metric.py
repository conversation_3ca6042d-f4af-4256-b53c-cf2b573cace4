from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class BodyMetricLimits:
    MINIMUM_VALUE = -(10**9)
    MAXIMUM_VALUE = 10**9


@dataclass(frozen=True)
class BodyMetricFields(EventFields):
    VALUE = "value"


class BodyMetricCategory(StrEnum):
    BODY_METRIC = DataType.BodyMetric
    BODY_FAT = "body_fat"
    BODY_TEMPERATURE = "body_temperature"
    PULSE_OXYGEN = "pulse_oxygen"
    RESPIRATORY_RATE = "respiratory_rate"
    WEIGHT = "weight"


class BodyMetricIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.BodyMetric


class BodyMetric(Event, BodyMetricIdentifier):
    type: Literal[DataType.BodyMetric] = Field(alias=BodyMetricFields.TYPE)
    category: BodyMetricCategory = Field(alias=BodyMetricFields.CATEGORY, default=BodyMetricCategory.BODY_METRIC)
    value: RoundedFloat = Field(
        alias=BodyMetricFields.VALUE,
        ge=BodyMetricLimits.MINIMUM_VALUE,
        le=BodyMetricLimits.MAXIMUM_VALUE,
    )
