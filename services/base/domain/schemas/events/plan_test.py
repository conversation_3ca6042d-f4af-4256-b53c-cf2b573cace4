from datetime import datetime, timedelta, timezone

import pytest
from dateutil.rrule import DAILY, MONTHLY, WEEKLY

from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.schemas.plan.plan import Plan


@pytest.mark.parametrize(
    "freq, start, interval, first_completed_at, now, expected_cycles",
    [
        # DAILY, not completed yet, now = 6 days after start
        pytest.param(
            DAILY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            1,
            None,
            datetime(2024, 1, 7, tzinfo=timezone.utc),
            7,
            id="daily__not_completed__now_6_days_after_start",
        ),
        # DAILY, completed after 2 days, now = 9 days after start
        pytest.param(
            DAILY,
            datetime(2024, 1, 1, hour=2, tzinfo=timezone.utc),
            1,
            datetime(2024, 1, 3, tzinfo=timezone.utc),
            datetime(2024, 1, 10, hour=1, tzinfo=timezone.utc),
            9,
            id="daily__completed_after_2_days__now_9_days_after_start",
        ),
        # WEEKLY, not completed, now = 3 weeks after start
        pytest.param(
            WEEKLY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            1,
            None,
            datetime(2024, 1, 22, tzinfo=timezone.utc),
            4,
            id="weekly__not_completed__now_3_weeks_after_start",
        ),
        # WEEKLY, completed after 2 weeks, now = 5 weeks after start
        pytest.param(
            WEEKLY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            1,
            datetime(2024, 1, 15, tzinfo=timezone.utc),
            datetime(2024, 2, 5, tzinfo=timezone.utc),
            6,
            id="weekly__completed_after_2_weeks__now_5_weeks_after_start",
        ),
        # MONTHLY, not completed, now = 3 months after start
        pytest.param(
            MONTHLY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            1,
            None,
            datetime(2024, 4, 1, tzinfo=timezone.utc),
            4,
            id="monthly__not_completed__now_3_months_after_start",
        ),
        # MONTHLY, completed after 1 month, now = 4 months after start
        pytest.param(
            MONTHLY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            1,
            datetime(2024, 2, 1, tzinfo=timezone.utc),
            datetime(2024, 5, 1, tzinfo=timezone.utc),
            5,
            id="monthly__completed_after_1_month__now_4_months_after_start",
        ),
        # Every 2 days, completed after 4 days, now = 10 days after start
        pytest.param(
            DAILY,
            datetime(2024, 1, 1, tzinfo=timezone.utc),
            2,
            datetime(2024, 1, 5, tzinfo=timezone.utc),
            datetime(2024, 1, 11, tzinfo=timezone.utc),
            6,
            id="every_2_days__completed_after_4_days__now_10_days_after_start",
        ),
    ],
)
def test_calculate_total_plan_cycles(freq, start, interval, first_completed_at, now, expected_cycles):
    rule = CustomRRule(freq=freq, dtstart=start, interval=interval, until=now + timedelta(days=31))
    total_cycles = Plan.calculate_total_plan_cycles(
        recurrence=rule,
        first_completed_at=first_completed_at,
        now=now,
    )
    assert total_cycles == expected_cycles
