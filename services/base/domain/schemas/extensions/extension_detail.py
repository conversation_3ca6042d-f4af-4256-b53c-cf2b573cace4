from typing import List, Optional
from uuid import UUID, uuid4

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.value_limits.extension_detail import ExtensionDetailValueLimit


class ExtensionDetail(BaseDataModel):
    extension_id: UUID = Field(description="Immutable extension identifier", default_factory=lambda: uuid4())
    name: str = Field(
        min_length=1, max_length=ExtensionDetailValueLimit.NAME_MAX_LENGTH, description="Readable extension name."
    )
    domain: str = Field(
        min_length=1,
        max_length=ExtensionDetailValueLimit.DOMAIN_MAX_LENGTH,
        description="Extension domain in format org.extension.provider",
    )
    description: str = Field(
        min_length=1,
        max_length=ExtensionDetailValueLimit.DESCRIPTION_MAX_LENGTH,
        description="Detailed app description.",
    )
    logo_url: Optional[str] = Field(min_length=1, description="Extension logo URL from S3.", default=None)
    version: str = Field(
        min_length=1,
        max_length=ExtensionDetailValueLimit.VERSION_MAX_LENGTH,
        description="Extension versioning major.minor.maintenance.",
        pattern=r"^\d+\.\d+\.\d+$",
    )
    provider_id: UUID = Field(description="Foreign key to extension owner id.")
    tags: List[str] = Field(
        min_length=1,
        max_length=ExtensionDetailValueLimit.TAGS_MAX_LENGTH,
        description="An extension may belong to multiple categories.",
    )
    archived_at: Optional[SerializableAwareDatetime] = Field(description="Time provider was archived at.", default=None)
