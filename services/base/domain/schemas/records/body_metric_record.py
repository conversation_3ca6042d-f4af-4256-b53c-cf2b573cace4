from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.records.record import Record


class BodyMetricRecordFields:
    TYPE = DocumentLabels.TYPE
    VALUE = DocumentLabels.VALUE
    METADATA = DocumentLabels.METADATA
    CATEGORY = DocumentLabels.CATEGORY


class BodyMetricRecordCategory(StrEnum):
    OTHER = auto()


class BodyMetricRecordIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.BodyMetricRecord


class BodyMetricRecord(Record, BodyMetricRecordIdentifier):
    type: Literal[DataType.BodyMetricRecord] = Field(alias=BodyMetricRecordFields.TYPE)
    category: BodyMetricRecordCategory = Field(alias=BodyMetricRecordFields.CATEGORY)
    value: float = Field(alias=BodyMetricRecordFields.VALUE)
