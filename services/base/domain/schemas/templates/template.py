from abc import ABC
from dataclasses import dataclass

from pydantic.fields import Field

from services.base.domain.annotated_types import (
    NonEmptyStr,
    SerializableAwareDatetime,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import (
    Document,
    RBACDocument,
    SystemPropertiesDocument,
    TagsDocument,
)


@dataclass
class TemplateFields:
    ID = DocumentLabels.ID
    DOCUMENT_NAME = "document_name"
    TYPE = DocumentLabels.TYPE
    NAME = "name"
    RBAC = DocumentLabels.RBAC
    DOCUMENT = "document"
    TEMPLATE_IDS = "template_ids"
    ARCHIVED_AT = "archived_at"
    DOCUMENT_TYPE = "document_type"


class Template(Document, SystemPropertiesDocument, RBACDocument, TagsDocument, ABC):
    name: NonEmptyStr = Field(alias=TemplateFields.NAME)
    archived_at: SerializableAwareDatetime | None = Field(alias=TemplateFields.ARCHIVED_AT)
