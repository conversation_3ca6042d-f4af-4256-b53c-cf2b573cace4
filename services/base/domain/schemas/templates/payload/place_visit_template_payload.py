from typing import Literal
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.place_visit import (
    PlaceVisitFields,
    PlaceVisitIdentifier,
)
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class PlaceVisitTemplatePayload(TemplatePayloadBase, PlaceVisitIdentifier):
    type: Literal[DataType.PlaceVisit] = Field(alias=PlaceVisitFields.TYPE)
    category: PlaceCategory = Field(alias=PlaceVisitFields.CATEGORY)
    place_id: UUID | None = Field(alias=PlaceVisitFields.PLACE_ID)
    rating: int | None = Field(
        alias=PlaceVisitFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
