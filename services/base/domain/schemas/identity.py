from dataclasses import dataclass

from pydantic import Field

from services.base.domain.annotated_types import SerializableUUID
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.identity_type import IdentityType
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class IdentityFields:
    TYPE: str = "type"


class Identity(BaseDataModel):
    id: SerializableUUID = Field(..., alias=DocumentLabels.ID)
    type: IdentityType = Field(..., alias=IdentityFields.TYPE)
