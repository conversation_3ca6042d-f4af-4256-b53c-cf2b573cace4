from dataclasses import dataclass
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import (
    AssetId,
    SerializableAwareDatetime,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.schemas.events.document_base import Document, SystemPropertiesDocument
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class ExportTaskSchemaFields:
    USER_ID = "user_id"
    STATUS = "status"
    COMPLETED_AT = "completed_at"
    TAKEOUT_NAME = "takeout_name"


class ExportTaskSchema(BaseDataModel):
    status: TaskStatus = Field(..., alias=ExportTaskSchemaFields.STATUS)
    completed_at: SerializableAwareDatetime | None = Field(None, alias=ExportTaskSchemaFields.COMPLETED_AT)
    user_id: UUID = Field(..., alias=ExportTaskSchemaFields.USER_ID)
    takeout_name: AssetId = Field(..., alias=ExportTaskSchemaFields.TAKEOUT_NAME)


class ExportTask(Document, ExportTaskSchema, SystemPropertiesDocument):
    # TODO it is not in DataTypes, probably shouldn't inherit from Document
    @classmethod
    def type_id(cls) -> DataType:
        raise NotImplementedError()
