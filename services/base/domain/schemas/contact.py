from dataclasses import dataclass
from datetime import date
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.person_relationship import ContactRelationship
from services.base.domain.schemas.events.document_base import (
    Document,
    RBACDocument,
    SystemPropertiesDocument,
    TagsDocument,
)
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class ContactFields:
    LAST_NAME = "last_name"
    FIRST_NAME = "first_name"
    COMPANY = "company"
    STREET = "street"
    ADDRESS = "address"
    CITY = "city"
    STATE = "state"
    COUNTRY = "country"
    ZIP = "zip"
    NOTE = "note"
    BIRTHDAY = "birthday"
    RELATIONSHIP = "relationship"
    ARCHIVED_AT = DocumentLabels.ARCHIVED_AT


class ContactAddress(BaseDataModel):
    street: NonEmptyStr | None = Field(alias=ContactFields.STREET)
    zip: NonEmptyStr | None = Field(alias=ContactFields.ZIP)
    city: NonEmptyStr | None = Field(alias=ContactFields.CITY)
    state: NonEmptyStr | None = Field(alias=ContactFields.STATE)
    country: NonEmptyStr = Field(alias=ContactFields.COUNTRY)


class ContactIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Contact


class Contact(Document, SystemPropertiesDocument, RBACDocument, TagsDocument, ContactIdentifier):
    type: Literal[DataType.Contact] = Field(alias=DocumentLabels.TYPE)
    relationship: ContactRelationship = Field(alias=ContactFields.RELATIONSHIP)
    first_name: NonEmptyStr = Field(alias=ContactFields.FIRST_NAME)
    last_name: NonEmptyStr | None = Field(alias=ContactFields.LAST_NAME)
    company: NonEmptyStr | None = Field(alias=ContactFields.COMPANY)
    address: ContactAddress | None = Field(alias=ContactFields.ADDRESS)
    note: NonEmptyStr | None = Field(alias=ContactFields.NOTE)
    birthday: date | None = Field(alias=ContactFields.BIRTHDAY)
    archived_at: SerializableAwareDatetime | None = Field(alias=DocumentLabels.ARCHIVED_AT)
