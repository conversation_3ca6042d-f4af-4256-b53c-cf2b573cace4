from typing import Sequence

from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.boolean_query import (
    BooleanQuery,
)
from services.base.domain.schemas.query.leaf_query import LeafQuery
from services.base.domain.schemas.query.validators.leaf_query_validator import LeafQueryValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.type_tree.type_tree import TreeNode


class BooleanQueryValidator:

    @staticmethod
    def validate(boolean_query: BooleanQuery, domain_types: Sequence[type[Document]]):
        for query in boolean_query.queries:
            if isinstance(query, LeafQuery):
                LeafQueryValidator.validate(leaf_query=query, domain_types=domain_types)
            elif isinstance(query, BooleanQuery):
                BooleanQueryValidator.validate(boolean_query=query, domain_types=domain_types)
            else:
                raise QueryValidationException(f"Invalid query type: {type(query)}")

    @staticmethod
    def validate_nodes(boolean_query: BooleanQuery, nodes: Sequence[TreeNode], type_tree: 'TypeTree'):
        for query in boolean_query.queries:
            if isinstance(query, LeafQuery):
                LeafQueryValidator.validate_nodes(leaf_query=query, nodes=nodes)
            elif isinstance(query, BooleanQuery):
                BooleanQueryValidator.validate_nodes(boolean_query=query, nodes=nodes, type_tree=type_tree)
            else:
                raise QueryValidationException(f"Invalid query type: {type(query)}")
