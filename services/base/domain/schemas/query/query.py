from __future__ import annotations

from typing import List, Sequence

from pydantic import Field

from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode


class Query(BaseDataModel):
    type_queries: Sequence[TypeQuery] = Field(min_length=1)

class NQuery(BaseDataModel):
    nodes: Sequence[TreeNode] = Field(min_length=1)
