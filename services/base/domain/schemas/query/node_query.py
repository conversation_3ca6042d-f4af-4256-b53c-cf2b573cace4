from typing import Sequence

from pydantic import Field, model_validator

from services.base.domain.schemas.query.boolean_query import BooleanQuery
from services.base.domain.schemas.query.leaf_query import LeafQuery
from services.base.domain.schemas.query.validators.boolean_query_validator import BooleanQueryValidator
from services.base.domain.schemas.query.validators.leaf_query_validator import LeafQ<PERSON>yValidator
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode


class NodeQuery(BaseDataModel):
    nodes: Sequence[TreeNode] = Field(min_length=1)
    query: LeafQuery | BooleanQuery | None = Field(union_mode="left_to_right")

    @model_validator(mode="after")
    def _query_validation(self):
        if self.query is None:
            # Nothing to validate, filtering only based on the document type
            return self
        elif isinstance(self.query, LeafQuery):
            LeafQueryValidator.validate_nodes(leaf_query=self.query, nodes=self.nodes)
            return self
        elif isinstance(self.query, BooleanQuery):
            BooleanQueryValidator.validate_nodes(boolean_query=self.query, nodes=self.nodes)
            return self
        else:
            raise TypeError(f"Expected LeafQuery or BooleanQuery, but got {type(self.query)}]")
