from pydantic import model_validator

from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.boolean_query import BooleanQuery
from services.base.domain.schemas.query.leaf_query import LeafQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.query.validators.boolean_query_validator import BooleanQueryValidator
from services.base.domain.schemas.query.validators.leaf_query_validator import LeafQueryValidator
from services.base.domain.schemas.shared import BaseDataModel


class SingleDocumentTypeQuery[T: Document](BaseDataModel):
    query: LeafQuery | BooleanQuery | None
    domain_type: type[T]

    @model_validator(mode="after")
    def query_validation(self):
        if self.query is None:
            # Nothing to validate, filtering only based on the document type
            return self
        elif isinstance(self.query, LeafQuery):
            LeafQueryValidator.validate(leaf_query=self.query, domain_types=[self.domain_type])
            return self
        elif isinstance(self.query, BooleanQuery):
            BooleanQueryValidator.validate(boolean_query=self.query, domain_types=[self.domain_type])
            return self
        else:
            raise TypeError(f"Expected LeafQuery or BooleanQuery, but got {type(self.query)}]")

    def to_query(self) -> Query:
        type_query = TypeQuery(domain_types=[self.domain_type], query=self.query)
        return Query(type_queries=[type_query])
