from dataclasses import dataclass

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.environment import Environment
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class WeatherFields:
    # Temperature
    TEMPERATURE: str = "temperature"
    FEELS_LIKE: str = "feels_like"

    # Wind
    SPEED: str = "speed"
    GUST: str = "gust"
    DEGREE: str = "degree"
    DIRECTION: str = "direction"

    # Top-level fields
    WIND: str = "wind"
    HUMIDITY: str = "humidity"
    CLOUD_COVER: str = "cloud_cover"
    UV: str = "uv"
    PRESSURE: str = "pressure"
    VISIBILITY: str = "visibility"
    PRECIPITATION: str = "precipitation"
    COORDINATES: str = "coordinates"
    METADATA: str = DocumentLabels.METADATA
    SYSTEM_PROPERTIES: str = DocumentLabels.SYSTEM_PROPERTIES


class WeatherTemperature(BaseDataModel):
    temperature: float | None = Field(alias=WeatherFields.TEMPERATURE, default=None)
    feels_like: float | None = Field(alias=WeatherFields.FEELS_LIKE, default=None)


class WeatherWind(BaseDataModel):
    speed: float | None = Field(alias=WeatherFields.SPEED, default=None)
    gust: float | None = Field(alias=WeatherFields.GUST, default=None)
    degree: float | None = Field(alias=WeatherFields.DEGREE, default=None)
    direction: str | None = Field(alias=WeatherFields.DIRECTION, default=None)


class WeatherIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Weather


class Weather(Environment, WeatherIdentifier):
    temperature: WeatherTemperature = Field(alias=WeatherFields.TEMPERATURE)
    wind: WeatherWind = Field(alias=WeatherFields.WIND)
    humidity: float | None = Field(alias=WeatherFields.HUMIDITY, default=None)
    cloud_cover: int | None = Field(alias=WeatherFields.CLOUD_COVER, default=None)
    uv: int | None = Field(alias=WeatherFields.UV, default=None)
    pressure: float | None = Field(alias=WeatherFields.PRESSURE, default=None)
    visibility: float | None = Field(alias=WeatherFields.VISIBILITY, default=None)
    precipitation: float | None = Field(alias=WeatherFields.PRECIPITATION, default=None)
