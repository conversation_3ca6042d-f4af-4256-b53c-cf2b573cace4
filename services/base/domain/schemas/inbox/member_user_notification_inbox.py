import uuid
from typing import Optional

from pydantic import UUID4, Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.enums.notification import NotificationPriority, NotificationStatus, NotificationType
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.value_limits.notification_inbox import NotificationInboxValueLimit


class MemberUserNotificationInbox(BaseDataModel):
    notification_uuid: UUID4 = Field(default_factory=uuid.uuid4)
    user_uuid: UUID4
    timestamp: SerializableAwareDatetime
    end_time: Optional[SerializableAwareDatetime] = None
    type: NotificationType
    status: NotificationStatus = NotificationStatus.UNREAD
    duration: Optional[int] = Field(default=None, ge=0, le=SECONDS_IN_365_DAYS)
    priority: NotificationPriority = NotificationPriority.DEFAULT
    urgent: bool = Field(default=False)
    important: bool = Field(default=False)
    title: str = Field(
        min_length=NotificationInboxValueLimit.MIN_TITLE, max_length=NotificationInboxValueLimit.MAX_TITLE
    )
    description: Optional[str] = Field(
        default=None,
        min_length=NotificationInboxValueLimit.MIN_DESCRIPTION,
        max_length=NotificationInboxValueLimit.MAX_DESCRIPTION,
    )
    message: str = Field(
        min_length=NotificationInboxValueLimit.MIN_MESSAGE, max_length=NotificationInboxValueLimit.MAX_MESSAGE
    )
    image_url: Optional[str] = Field(
        default=None,
        min_length=NotificationInboxValueLimit.MIN_IMAGE_URL,
        max_length=NotificationInboxValueLimit.MAX_IMAGE_URL,
    )
    action: Optional[str] = Field(
        default=None,
        min_length=NotificationInboxValueLimit.MIN_ACTION,
        max_length=NotificationInboxValueLimit.MAX_ACTION,
    )
