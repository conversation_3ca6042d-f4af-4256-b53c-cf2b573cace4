from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.schemas.shared import BaseDataModel


class MemberUserDevice(BaseDataModel):
    user_uuid: UUID = Field()
    device_token: str = Field()
    device_name: Optional[str] = Field(default=None)
    refreshed_at: SerializableAwareDatetime = Field(default_factory=lambda: datetime.now(tz=timezone.utc))
