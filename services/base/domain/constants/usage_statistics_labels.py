from dataclasses import dataclass


@dataclass(frozen=True)
class UsageStatisticsLabels:
    LABEL_USAGE_STATISTICS_RESULTS = "usage_statistics_results"
    LABEL_REPORT_TIMEFRAME = "report_timeframe"
    LABEL_AGGREGATION_INTERVAL_START = "aggregation_interval_start"
    LABEL_TIME_OF_EXECUTION = "time_of_execution"

    LABEL_NEW_USERS = "new_users"
    LABEL_NEW_ANONYMOUS_USERS = "new_anonymous_users"
    LABEL_TOTAL_USERS = "total_users"
    LABEL_TOTAL_ANONYMOUS_USERS = "total_anonymous_users"
    LABEL_ACTIVE_USERS = "active_users"
    LABEL_ACTIVE_ANONYMOUS_USERS = "active_anonymous_users"
    LABEL_STALE_USERS = "stale_users"
    LABEL_MULTIPLE_DEVICE_USERS = "multiple_device_users"
    LABEL_USERS_WITH_NOTES = "users_with_notes"
    LABEL_USERS_WITH_LOCATION = "users_with_location"
    LABEL_USERS_WITH_HEARTRATE = "users_with_heartrate"
    LABEL_RATING_STATISTICS = "rating_statistics"
    LABEL_EVENT_STATISTICS = "events_statistics"
    LABEL_DOCUMENT_COUNT_CSV_STRING = "document_count_csv_string"

    LABEL_USERS_WITH_EVENTS = "users_with_events"
    LABEL_AVERAGE_EVENTS_PER_ACTIVE_USER = "average_events_per_active_user"
    LABEL_AVERAGE_EVENTS_PER_NEW_USER = "average_events_per_new_user"

    LABEL_USERS_WITH_RATING = "users_with_rating"
    LABEL_USERS_WITH_MOOD_RATING = "users_with_mood_rating"
    LABEL_USERS_WITH_PHYSICAL_RATING = "users_with_physical_rating"
    LABEL_USERS_WITH_INTELLECTUAL_RATING = "users_with_intellectual_rating"
    LABEL_USERS_WITH_SOCIAL_RATING = "users_with_social_rating"
    LABEL_RATINGS_MEDIAN = "ratings_median"
