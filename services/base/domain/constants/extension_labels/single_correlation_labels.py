from dataclasses import dataclass


@dataclass
class SingleCorrelationLabels:
    OUTCOME_NAME = "outcome_name"
    TRIGGER_NAME = "trigger_name"
    OUTCOME_DOCUMENT_COUNT = "outcome_document_count"
    TRIGGER_DOCUMENT_COUNT = "trigger_document_count"
    CORRELATION = "correlation"
    IMMEDIATE_TERM = "immediate_term"
    SHORT_TERM = "short_term"
    MEDIUM_TERM = "medium_term"
    LONG_TERM = "long_term"
    MAX_CORRELATION = "max_correlation"
    CONFIDENCE = "confidence"
    RESULT_IMPLICATION = "result_implication"

    SINGLE_CORRELATION_RESULTS = "single_correlation_results"
