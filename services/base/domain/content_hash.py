import hashlib
from typing import Protocol, Sequence


class _Serializable(Protocol):
    def __str__(self) -> str: ...


class Hasher:
    @staticmethod
    def content_sha256(content: str) -> str:
        hash_obj = hashlib.sha256()
        hash_obj.update(content.encode("utf-8"))
        return hash_obj.hexdigest()

    @staticmethod
    def fields_sha256(fields: Sequence[_Serializable]) -> str:
        hash_obj = hashlib.sha256()
        content = "".join([str(arg) for arg in fields if arg is not None])
        hash_obj.update(content.encode("utf-8"))
        return hash_obj.hexdigest()
