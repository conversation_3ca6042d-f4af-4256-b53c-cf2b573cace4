import logging

import logfire
from fastapi import FastAPI

from settings.app_config import AppSettings
from settings.app_constants import RUN_ENV_LOCAL


def instrument_app(app: FastAPI, settings: AppSettings):
    if settings.RUN_ENV == RUN_ENV_LOCAL or not settings.IS_CONTAINERIZED:
        logging.info("skipping app instrumentation on local")
        return
    # OTEL
    logfire.instrument_fastapi(app=app)  # uses FastAPIInstrumentor under the hood
