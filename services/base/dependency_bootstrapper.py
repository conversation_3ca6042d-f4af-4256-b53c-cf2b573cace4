from typing import Self

from injector import Injector
from opensearchpy import As<PERSON><PERSON><PERSON><PERSON>earch
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import As<PERSON><PERSON><PERSON><PERSON>, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.use_cases.cleanup_missing_references_use_case import CleanupMissingReferencesUseCase
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.extension_schema_repository import ExtensionSchemaRepository
from services.base.domain.repository.extension_subscriptions_repository import ExtensionSubscriptionsRepository
from services.base.domain.repository.login_google_repository import LoginGoogleRepository
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_aggregation_service import OSAggregationService
from services.base.infrastructure.database.opensearch.os_depr_event_repository import (
    OSDeprEventRepository,
)
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_environment_repository import (
    OSEnvironmentRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_plan_repository import OSPlanRepository
from services.base.infrastructure.database.opensearch.repository.os_template_repository import OSTemplateRepository
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_detail_repository import (
    SqlAlchExtensionDetailRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_provider_repository import (
    SqlAlchExtensionProviderRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_schema_repository import (
    SqlAlchExtensionSchemaRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_subscriptions_repository import (
    SqlAlchExtensionSubscriptionsRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_login_google_repository import (
    SqlAlchLoginGoogleRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_os_tasks_repository import (
    SqlAlchMemberUserOSTasksRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_settings_repository import (
    SqlAlchMemberUserSettingsRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alchemy_notification_inbox_repository import (
    SqlAlchNotificationInboxRepository,
)
from settings.app_config import settings
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: type[T]) -> T:
        return self.injector.get(interface=interface)

    def _bind_singleton[T](self, interface: type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_repositories()
        self._bind_services()

        return self

    async def cleanup(self):
        await self.get(AsyncOpenSearch).close()

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=DuplicateCheckService,
            to=DuplicateCheckService(search_service=self.get(interface=DocumentSearchService)),
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=AggregationService,
            to=OSAggregationService(client=self.get(interface=AsyncOpenSearch)),
        )
        self._bind_singleton(
            interface=TemplateRepository,
            to=OSTemplateRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=PlanRepository,
            to=OSPlanRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=EnvironmentRepository, to=OSEnvironmentRepository(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserOSTasksRepository,
            to=SqlAlchMemberUserOSTasksRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=LoginGoogleRepository,
            to=SqlAlchLoginGoogleRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserSettingsRepository,
            to=SqlAlchMemberUserSettingsRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=NotificationInboxRepository,
            to=SqlAlchNotificationInboxRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionSubscriptionsRepository,
            to=SqlAlchExtensionSubscriptionsRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionDetailRepository,
            to=SqlAlchExtensionDetailRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionSchemaRepository,
            to=SqlAlchExtensionSchemaRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionProviderRepository,
            to=SqlAlchExtensionProviderRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=CleanupMissingReferencesUseCase,
            to=CleanupMissingReferencesUseCase(
                agg_service=self.get(interface=AggregationService),
                search_service=self.get(interface=DocumentSearchService),
                template_repo=self.get(interface=TemplateRepository),
                plan_repo=self.get(interface=PlanRepository),
                client=self.get(interface=AsyncOpenSearch),
            ),
        )


bootstrapper = DependencyBootstrapper().build()


async def resource_cleanup():
    await bootstrapper.cleanup()
