import logging
from typing import List, Optional
from uuid import UUID
from zoneinfo import ZoneInfo

from pydantic import Field, model_validator

from services.base.application.use_case_base import UseCaseBase
from services.base.application.utils.input_validation import (
    are_sorted_entries_intervals_overlapping,
    is_none_except_fields,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.provider import Provider
from services.base.infrastructure.database.opensearch.wrappers.client import get_default_os_client
from services.mobile_service.application.loaders.heart_rate_loader import HeartRateData, HeartRateLoader
from services.mobile_service.application.loaders.metadata_input import MetadataInputBoundary
from services.mobile_service.application.loaders.resting_heart_rate_loader import (
    RestingHeartRateData,
    RestingHeartRateLoader,
)
from services.mobile_service.application.loaders.sleep_loader import SleepData, SleepLoader
from services.mobile_service.application.loaders.steps_loader import StepsData, StepsLoader

_client = get_default_os_client()


class FitLoaderRunnerInput(MetadataInputBoundary):
    heart_rate: Optional[List[HeartRateData]] = Field(min_length=1, default=None)
    resting_heart_rate: Optional[List[RestingHeartRateData]] = Field(min_length=1, default=None)
    sleep: Optional[List[SleepData]] = Field(min_length=1, default=None)
    steps: Optional[List[StepsData]] = Field(min_length=1, default=None)
    provider: Optional[Provider] = None

    @model_validator(mode="after")
    def validate_input(self):
        if is_none_except_fields(self.model_dump(), {DocumentLabels.METADATA}):
            raise ValueError("You must provide one of the supported loading data type fields.")

        for field in ("heart_rate", "steps", "sleep", "resting_heart_rate"):
            if values := getattr(self, field):
                sorted_entries = sorted(values, key=lambda entry: entry.timestamp)
                are_overlapping = are_sorted_entries_intervals_overlapping(entry_list=sorted_entries)
                if are_overlapping:
                    raise ValueError(f"Overlapping entries found in {field} data.")
                setattr(self, field, sorted_entries)
        return self


class FitLoaderRunner(UseCaseBase):
    def execute(
        self,
        input_data: FitLoaderRunnerInput,
        user_uuid: UUID,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
        **kwargs,
    ):
        try:
            HeartRateLoader(
                user_uuid=user_uuid,
                data=input_data.heart_rate,
                metadata=input_data.metadata,
                client=_client,
                fallback_timezone=fallback_timezone,
            ).process_data()
        except Exception as error:
            logging.exception("Error while processing %s", error)

        try:
            RestingHeartRateLoader(
                user_uuid=user_uuid,
                data=input_data.resting_heart_rate,
                metadata=input_data.metadata,
                client=_client,
                fallback_timezone=fallback_timezone,
            ).process_data()
        except Exception as error:
            logging.exception("Error while processing %s", error)

        try:
            SleepLoader(
                user_uuid=user_uuid,
                data=input_data.sleep,
                metadata=input_data.metadata,
                client=_client,
                fallback_timezone=fallback_timezone,
            ).process_data()
        except Exception as error:
            logging.exception("Error while processing %s", error)

        try:
            StepsLoader(
                user_uuid=user_uuid,
                data=input_data.steps,
                metadata=input_data.metadata,
                client=_client,
                fallback_timezone=fallback_timezone,
            ).process_data()

        except Exception as error:
            logging.exception("Error while processing %s", error)
