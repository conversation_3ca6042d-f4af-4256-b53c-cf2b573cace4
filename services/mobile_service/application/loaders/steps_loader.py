import logging
from datetime import timed<PERSON><PERSON>
from typing import List, Optional
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch
from pydantic import Field

from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.steps import Steps, StepsDetail
from services.mobile_service.application.loaders.metadata_input import MetadataInputModel


class StepsData(InputTimeIntervalModel):
    steps: int = Field(ge=0)


class StepsLoader(LoaderBase):
    _source = "steps"
    PROVIDER: Provider = None  # to be determinated upon construction
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)

    def __init__(
        self,
        user_uuid: UUID,
        metadata: MetadataInputModel,
        data_type: DataType = DataType.Steps,
        data: Optional[List[StepsData]] = None,
        client: Optional[OpenSearch] = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.PROVIDER = metadata.organization
        self.input_data = data
        self.metadata = metadata

    def process_data(self) -> None:
        if self.input_data:
            self._process_steps_data(self.input_data)

    def _parse_data_to_entry(
        self, metadata: Metadata, entry_list: List[StepsDetail], total_duration: int, total_steps: int
    ) -> Steps:
        """Parses data to prepared entry for DB"""
        starting_datetime = entry_list[0].timestamp
        ending_entry = entry_list[-1]

        ending_datetime = ending_entry.end_time or ending_entry.timestamp

        return Steps(
            timestamp=starting_datetime,
            end_time=ending_datetime,
            duration=total_duration,
            steps=total_steps,
            step_details=entry_list,
            metadata=metadata,
        )

    def _process_steps_data(self, input_data: List[StepsData]):
        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            **self.metadata.model_dump(),
        )
        input_data.sort(key=lambda entry: entry.timestamp)
        data = (entry for entry in input_data)
        to_be_committed_entries: List[str] = []
        # Defines list of entries to be parsed into single database entry
        bulk_entries: List[StepsDetail] = []
        cached_entry: Optional[StepsDetail] = None
        total_duration: int = 0
        total_steps: int = 0
        try:
            for data_entry in data:
                try:
                    # this must be done BEFORE the unpacking
                    data_entry: StepsDetail = StepsDetail(
                        **data_entry.model_dump(),
                        duration=(
                            int((data_entry.end_time - data_entry.timestamp).total_seconds())
                            if data_entry.end_time
                            else None
                        ),
                    )

                    # Keep the reference for the starting entry
                    if not cached_entry:
                        cached_entry = data_entry

                    time_difference = get_datetime_difference(cached_entry.timestamp, data_entry.timestamp)
                    if time_difference >= self.LOAD_AGGREGATION_INTERVAL:
                        load_data: Steps = self._parse_data_to_entry(
                            entry_list=bulk_entries,
                            total_duration=total_duration,
                            total_steps=total_steps,
                            metadata=metadata,
                        )
                        # Append to be committed entries
                        to_be_committed_entries.append(load_data.model_dump_json(by_alias=True))
                        # clear entries
                        del bulk_entries[:]
                        # Sets current data entry as new cached starting one
                        cached_entry = data_entry

                        # appends the starting entry & resets totals
                        bulk_entries.append(cached_entry)
                        total_duration = data_entry.duration if data_entry.duration else 0
                        total_steps = data_entry.steps
                        continue
                    # Append entry & add count to totals
                    bulk_entries.append(data_entry)
                    total_duration += data_entry.duration if data_entry.duration else 0
                    total_steps += data_entry.steps
                except Exception as error:  # pylint:disable=broad-except
                    logging.exception("Error processing entry: %s, %s", data_entry, repr(error))
                    del bulk_entries[:]
                    cached_entry = None

                self._commit_if_limit_reached(to_be_committed_entries)
            if bulk_entries:
                load_data: Steps = self._parse_data_to_entry(
                    entry_list=bulk_entries, total_duration=total_duration, total_steps=total_steps, metadata=metadata
                )
                to_be_committed_entries.append(load_data.model_dump_json(by_alias=True))
            self._commit(entries=to_be_committed_entries)
        except Exception as error:  # pylint:disable=broad-except
            logging.exception("Error processing entries: %s", repr(error))
