import logging
from datetime import timed<PERSON><PERSON>
from typing import List, Optional, <PERSON>ple
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch
from pydantic import Field

from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.constants.value_limits import HeartRateValueLimit
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.heart_rate import HeartRate, HeartRateBpmDetail
from services.base.domain.schemas.metadata import Metadata
from services.mobile_service.application.loaders.metadata_input import MetadataInputModel


class HeartRateData(InputTimeIntervalModel):
    value: float = Field(ge=0, le=HeartRateValueLimit.MAXIMUM)


class HeartRateLoader(LoaderBase):
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)
    _source = "heart_rate"

    def __init__(
        self,
        user_uuid: UUID,
        metadata: MetadataInputModel,
        data_type: DataType = DataType.HeartRate,
        data: Optional[List[HeartRateData]] = None,
        client: Optional[OpenSearch] = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.PROVIDER = metadata.organization
        self.input_data = data
        self.metadata = metadata

    def process_data(self) -> None:
        if not self.input_data:
            return

        self._process_heart_rate_data(self.input_data)

    def _parse_data_to_entry(self, metadata: Metadata, entry_list: List[HeartRateData]) -> HeartRate:
        """Parses data to output heart rate entry"""
        starting_entry = entry_list[0]
        ending_entry = entry_list[-1]
        starting_datetime = starting_entry.timestamp
        ending_datetime = ending_entry.end_time or ending_entry.timestamp
        bpm_avg, bpm_min, bpm_max = self._parse_bpm_value(entry_list)
        output_bpm_detail: List[HeartRateBpmDetail] = self._parse_bpm_detail(entry_list)

        output_heart_rate = HeartRate(
            timestamp=starting_datetime,
            bpm_avg=bpm_avg,
            bpm_min=bpm_min,
            bpm_max=bpm_max,
            duration=int((ending_datetime - starting_datetime).total_seconds()) if ending_datetime else None,
            bpm_detail=output_bpm_detail,
            end_time=ending_datetime,
            metadata=metadata,
        )

        return output_heart_rate

    def _process_heart_rate_data(self, input_data: List[HeartRateData]):
        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            **self.metadata.model_dump(),
        )
        input_data.sort(key=lambda entry: entry.timestamp)

        data = (entry for entry in input_data)
        to_be_committed_entries: List[str] = []
        # Defines list of entries to be parsed into single database entry
        bulk_entries: List[HeartRateData] = []
        cached_entry: Optional[HeartRateData] = None
        for entry in data:
            try:
                entry.timestamp = entry.timestamp
                # Copies and appends the starting entry
                if not cached_entry:
                    cached_entry = entry

                time_difference = get_datetime_difference(cached_entry.timestamp, entry.timestamp)

                if time_difference < self.LOAD_AGGREGATION_INTERVAL:
                    bulk_entries.append(entry)
                    continue

                output_heart_rate: HeartRate = self._parse_data_to_entry(entry_list=bulk_entries, metadata=metadata)
                # Append to be committed entries
                to_be_committed_entries.append(output_heart_rate.model_dump_json(by_alias=True))
                # clear entries
                del bulk_entries[:]
                # Sets current data entry as new cached starting one
                cached_entry = entry
                bulk_entries.append(cached_entry)
            except Exception as error:  # pylint:disable=broad-except
                logging.exception("Error processing entry: %s, %s", entry, repr(error))
                del bulk_entries[:]
                cached_entry = None
            self._commit_if_limit_reached(to_be_committed_entries)
        try:
            if bulk_entries:
                output_heart_rate: HeartRate = self._parse_data_to_entry(entry_list=bulk_entries, metadata=metadata)
                to_be_committed_entries.append(output_heart_rate.model_dump_json(by_alias=True))
            self._commit(entries=to_be_committed_entries)
        except Exception as error:  # pylint:disable=broad-except
            logging.exception("Error processing entries: %s", repr(error))

    @staticmethod
    def _parse_bpm_value(entry_list: List[HeartRateData]) -> Tuple[float, float, float]:
        output_list = []
        for entry in entry_list:
            output_list.append(float(entry.value))
        # returns average
        return sum(output_list) / len(output_list), min(output_list), max(output_list)

    def _parse_bpm_detail(self, entry_list: List[HeartRateData]) -> List[HeartRateBpmDetail]:
        output_list: List[HeartRateBpmDetail] = []
        for entry in entry_list:
            try:
                output_list.append(
                    HeartRateBpmDetail(
                        value=float(entry.value),
                        timestamp=entry.timestamp,
                        confidence=None,
                    )
                )
            except (KeyError, ValueError):
                logging.warning("Could not process bpm detail entry %s, skipping.", entry)

        return output_list
