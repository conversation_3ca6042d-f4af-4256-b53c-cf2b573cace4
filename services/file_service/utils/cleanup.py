from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.file_handlers import delete_directory_contents
from services.file_service.utils.paths import get_user_provider_folder, get_user_root_folder
from services.file_service.utils.task_cancellation import cancel_concurrent_file_tasks


def delete_user_storage_data_from_provider(user_uuid, provider: SupportedDataProviders) -> None:
    """cancels all concurrent file tasks for this provider
    and then deletes all data for that provider ONLY
    """
    cancel_concurrent_file_tasks(user_uuid=user_uuid, provider=provider)
    folder_path = get_user_provider_folder(user_uuid, provider)
    delete_directory_contents(folder_path)


def delete_all_user_storage_data(user_uuid) -> None:
    delete_directory_contents(get_user_root_folder(user_uuid))
