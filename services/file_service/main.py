from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.encoders import ENCODERS_BY_TYPE
from fastapi_health import health
from fastapi_injector import attach_injector
from pydantic_core import PydanticUndefinedType

from services.base.api.exception_handlers import set_default_exception_handlers
from services.base.api.health_endpoints import broken_executor, server_run_time
from services.base.api.middleware.cors_middleware import add_cors_middleware
from services.base.api.set_logging import set_uvicorn_logging
from services.base.application.message_queue_client import MessageQueueClient
from services.base.dependency_bootstrapper import resource_cleanup
from services.base.domain.constants.messaging import ATT_NAME_LOADING_EVENT
from services.base.tasks.task_manager import TaskManager
from services.base.tasks.worker_orchestrator import WorkerOrchestrator
from services.base.telemetry.fastapi_instrumentor import instrument_app
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.file_service.api.export_data_endpoints import export_data_router
from services.file_service.api.file_endpoints import file_router
from services.file_service.application.message_processor import FileServiceMessageProcessor
from services.file_service.constants import (
    FILE_SERVICE_QUEUE_POLLING_TIME,
    FILE_SERVICE_QUEUE_WAIT_TIME,
    FILE_SERVICE_QUEUES,
)
from services.file_service.dependency_bootstrapper import DependencyBootstrapper
from services.file_service.scheduler import Scheduler
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL, RUN_ENV_PRODUCTION
from settings.app_secrets import secrets

# TODO: Fixes issues with list query params until [https://github.com/tiangolo/fastapi/discussions/10331] is resolved
ENCODERS_BY_TYPE[PydanticUndefinedType] = lambda o: None

# telemetry
set_uvicorn_logging()
TelemetryInstrumentor.initialize(service_name="file_service", settings=settings, secrets=secrets)


@asynccontextmanager
async def lifespan(_: FastAPI):
    # startup
    scheduler.start()
    task_manager.start()

    yield

    # shutdown
    task_manager.abort()
    scheduler.shutdown()
    await resource_cleanup()
    await bootstrapper.cleanup()


app = FastAPI(
    root_path=None if settings.RUN_ENV == RUN_ENV_LOCAL else "/file",
    openapi_url="/openapi.json" if not settings.RUN_ENV == RUN_ENV_PRODUCTION else None,
    title="File service",
    version="0.2",
    lifespan=lifespan,
)
instrument_app(app=app, settings=settings)

set_default_exception_handlers(app)
add_cors_middleware(app=app)
# routes
app.add_api_route("/health", health([server_run_time, broken_executor]))
app.include_router(file_router)
app.include_router(export_data_router)

bootstrapper = DependencyBootstrapper().build()
attach_injector(app=app, injector=bootstrapper.injector)
scheduler = Scheduler(bootstrapper=bootstrapper)
task_manager = TaskManager(
    service_queues=FILE_SERVICE_QUEUES,
    service_message_processor=FileServiceMessageProcessor(),
    service_message_attributes=[ATT_NAME_LOADING_EVENT],
    wait_time=FILE_SERVICE_QUEUE_WAIT_TIME,
    polling_time=FILE_SERVICE_QUEUE_POLLING_TIME,
    message_queue_client=bootstrapper.get(MessageQueueClient),
    worker_orchestrator=bootstrapper.get(WorkerOrchestrator),
)
