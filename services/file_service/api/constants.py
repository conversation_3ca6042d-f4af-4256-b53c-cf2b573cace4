from dataclasses import dataclass


@dataclass(frozen=True)
class FileEndpointRoutes:
    CANCEL_FILE_UPLOAD = "/cancel_file_upload/"
    DELETE_ALL_DATA = "/delete_all_user_data/"
    UPLOAD_PROVIDER_FILE = "/upload_provider_file/"
    SUPPORTED_DATA_PROVIDERS = "/supported_data_providers/"


@dataclass(frozen=True)
class ExportDataEndpointRoutes:
    SCHEDULE_DATA_EXPORT = "/schedule/"
    DATA_EXPORT_URL = "/url/"


@dataclass(frozen=True)
class FileServicePrefixes:
    VERSION2_PREFIX = "/api/v0.2"


@dataclass(frozen=True)
class DataExportServicePrefixes:
    VERSION1_PREFIX = "/api/v1.0"
    DATA_EXPORT_PREFIX = "/data_export"
