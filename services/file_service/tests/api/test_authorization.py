from starlette import status

from services.file_service.api.export_data_endpoints import export_data_router
from services.file_service.api.file_endpoints import file_router
from services.file_service.tests.api.conftest import get_path_operation


def test_file_endpoints_unauthorized(test_client):
    for route in (
        *file_router.routes,
        *export_data_router.routes,
    ):
        for method in route.methods:
            response = get_path_operation(method=method, test_client=test_client)(route.path)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
