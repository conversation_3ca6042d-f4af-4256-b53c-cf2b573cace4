from typing import Tuple, Union

import pytest

from services.file_service.application.converters import (
    coordinates_to_string,
    e7_to_coordinates,
    timestampMs_to_timestampS,
)
from services.file_service.application.utils import InvalidE7Geo


@pytest.mark.parametrize(
    "input,expected_output", (("123456.7890", 123), (123456.7890, 123), ("123456", 123), (123456, 123))
)
def test_timestampMs_to_timestampS(input: Union[str, int, float], expected_output: int):
    """Tests various cases of ms input"""
    assert timestampMs_to_timestampS(input) == expected_output


@pytest.mark.parametrize(
    "input_lat_long, expected_lat_long",
    (((1.0, 2.0), "1.0, 2.0"), ((0.0, 0.0), "0.0, 0.0"), ((10.5, 20.7), "10.5, 20.7")),
)
def test_coordinates_to_string_float_numbers(input_lat_long: <PERSON><PERSON>[int, int], expected_lat_long: str):
    assert coordinates_to_string(latitude=input_lat_long[0], longitude=input_lat_long[1]) == expected_lat_long


@pytest.mark.parametrize(
    "input_lat_long, expected_lat_long",
    (
        ((80 * 10**7, -170 * 10**7), (80.0, -170.0)),
        ((-80 * 10**7, 170 * 10**7), (-80.0, 170.0)),
        ((89.9 * 10**7, 179.9 * 10**7), (89.9, 179.9)),
        ((0, 0), (0.0, 0.0)),
    ),
)
def test_e7_to_coordinates_large_integers_valid_input_should_pass(
    input_lat_long: Tuple[int, int], expected_lat_long: Tuple[int, int]
):
    assert e7_to_coordinates(latitudeE7=input_lat_long[0], longitudeE7=input_lat_long[1]) == expected_lat_long


@pytest.mark.parametrize(
    "input_lat_long",
    (
        (90 * 10**7, 100 * 10**7),
        (80 * 10**7, 180 * 10**7),
        (20 * 10**7, -180 * 10**7),
        (-90 * 10**7, 80 * 10**7),
    ),
)
def test_e7_to_coordinates_large_integers_invalid_input_should_raise(input_lat_long: Tuple[int, int]):
    with pytest.raises(InvalidE7Geo):
        e7_to_coordinates(latitudeE7=input_lat_long[0], longitudeE7=input_lat_long[1])
