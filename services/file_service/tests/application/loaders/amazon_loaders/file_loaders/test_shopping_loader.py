import pytest

from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.amazon_loaders.file_loaders.shopping_loader import ShoppingLoader
from services.file_service.tests.application.loaders.conftest import (
    TEST_DATA_SOURCE_DIR_PATH,
    TEST_UUID,
    basic_loader_test,
)
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD, PATH_WHICH_DOES_NOT_EXIST


@pytest.fixture
def shopping_loader() -> ShoppingLoader:
    return ShoppingLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.ShoppingActivity,
    )


def test_remove_dollar_sign_function(shopping_loader: ShoppingLoader):
    given_input = [
        "1.99",
        "$1.99",
        "",
        "1",
        "$1",
        "$$",
    ]
    expected_output = [
        "1.99",
        "1.99",
        "",
        "1",
        "1",
        "",
    ]

    for input_val, exp_out in zip(given_input, expected_output):
        assert shopping_loader._remove_dollar_sign(input_val) == exp_out


def test_split_carrier_info_incorrect_values_raise_error(shopping_loader: ShoppingLoader):
    given_input = [
        "AMZN(",
        "AMZN(123",
    ]

    for wrong_input_val in given_input:
        with pytest.raises(ValueError):
            shopping_loader._split_carrier_info(wrong_input_val)


def test_split_carrier_info_correct_input(shopping_loader: ShoppingLoader):
    given_input = [
        "AMZN",
        "AMZN()",
        "AMZN(1)",
        "AMZN(22)",
        "AMZN(333)",
    ]
    expected_output = [
        ("AMZN", None),
        ("AMZN", None),
        ("AMZN", "1"),
        ("AMZN", "22"),
        ("AMZN", "333"),
    ]
    for input_val, exp_out in zip(given_input, expected_output):
        assert shopping_loader._split_carrier_info(input_val) == exp_out


async def test_amazon_shopping_loader_load_files_data(os_client_mock):
    # Arrange
    shopping_loader = ShoppingLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.ShoppingActivity,
        client=os_client_mock,
    )

    # Act & Assert
    await basic_loader_test(loader=shopping_loader, processing_method=shopping_loader.load_files_data)


def test_amazon_shopping_loader_missing_files(caplog, os_client_mock):
    # Arrange
    shopping_loader = ShoppingLoader(
        user_uuid=TEST_UUID,
        data_dir_path=PATH_WHICH_DOES_NOT_EXIST,
        data_type=DataType.ShoppingActivity,
        client=os_client_mock,
    )

    # Act
    shopping_loader.load_files_data()
    # Assert
    assert MESSAGE_UNABLE_TO_LOAD in caplog.text
