import json
from datetime import datetime
from typing import Callable
from unittest import mock
from uuid import UUID, uuid4

from dateutil.parser import parse

from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.time import tz_infos
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.event import Event
from services.base.domain.time import parse_datetime_tz_fallback
from settings.app_config import settings

# @TODO: add separate data for tests and update this path
TEST_DATA_SOURCE_DIR_PATH = settings.DATA_SOURCE_DIR_PATH
TEST_UUID = uuid4()


async def basic_loader_test(loader: LoaderBase, processing_method: Callable):
    with mock.patch.object(loader, "_commit") as mocked_method:
        with mock.patch.object(loader, "_parse_tz_datetime") as mocked_parse_tz_datetime:
            mocked_parse_tz_datetime.side_effect = parse_datetime_tz_fallback
            processing_method()
            called_with_body = mocked_method.call_args[1]
            called_with_entries = called_with_body["entries"]

            single_entry: Event | str = called_with_entries[0]
            if isinstance(single_entry, str):
                single_entry: dict = json.loads(single_entry)
            else:
                single_entry = single_entry.model_dump()

            # Assert
            assert len(called_with_entries) > 0
            assert isinstance(called_with_body, dict)
            assert isinstance(called_with_entries, list)
            assert isinstance(single_entry, dict)
            if single_entry.get(DocumentLabels.USER_UUID, None):
                assert isinstance(UUID(single_entry[DocumentLabels.USER_UUID]), UUID)

            if single_entry.get(DocumentLabels.TIMESTAMP, None):
                datetime_representation = parse(single_entry[DocumentLabels.TIMESTAMP], tzinfos=tz_infos)
                assert isinstance(datetime_representation, datetime)
                assert datetime_representation.tzinfo
