from services.file_service.application.loaders.google_loaders.file_loaders.fit_activity_metrics_loader import (
    FitActivityMetricsLoader,
)
from services.file_service.tests.application.loaders.conftest import TEST_UUID
from settings.app_constants import MESSAGE_NO_FILES_FOUND, PATH_WHICH_DOES_NOT_EXIST


def test_fit_activity_metrics_loader_empty_folder(caplog, os_client_mock):
    metrics_loader = FitActivityMetricsLoader(
        user_uuid=TEST_UUID,
        data_dir_path=PATH_WHICH_DOES_NOT_EXIST,
    )
    metrics_loader.load_files_data()
    assert MESSAGE_NO_FILES_FOUND in caplog.text
