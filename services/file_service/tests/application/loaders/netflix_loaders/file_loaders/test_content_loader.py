import pytest

from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.netflix_loaders.file_loaders.content_loader import ContentLoader
from services.file_service.tests.application.loaders.conftest import (
    TEST_DATA_SOURCE_DIR_PATH,
    TEST_UUID,
    basic_loader_test,
)
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD, PATH_WHICH_DOES_NOT_EXIST


async def test_content_loader_load_files_data(os_client_mock):
    # Arrange
    content_loader = ContentLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.Content,
        client=os_client_mock,
    )

    # Act & Assert
    await basic_loader_test(loader=content_loader, processing_method=content_loader.load_files_data)


def test_content_loader_missing_files(caplog, os_client_mock):
    # Arrange
    content_loader = ContentLoader(
        user_uuid=TEST_UUID,
        data_dir_path=PATH_WHICH_DOES_NOT_EXIST,
        data_type=DataType.Content,
        client=os_client_mock,
    )

    # Act
    content_loader.load_files_data()
    # Assert
    assert MESSAGE_UNABLE_TO_LOAD in caplog.text


@pytest.mark.parametrize(
    ("full_title", "expected_title"),
    [
        ("Black Mirror: Season 6: Beyond the Sea (Episode 3)", "Black Mirror"),
        ("Season 6 Trailer: Virgin River", "Virgin River"),
        ("Trolls Band Together_hook_02_16x9", "Trolls Band Together"),
        ("Amadeus_hook_primary_16x9", "Amadeus"),
    ],
)
def test_extract_track_name_from_title(os_client_mock, full_title: str, expected_title: str):
    loader = ContentLoader(
        user_uuid=TEST_UUID, data_dir_path=PATH_WHICH_DOES_NOT_EXIST, data_type=DataType.Content, client=os_client_mock
    )
    title = loader._extract_track_name_from_title(title=full_title)
    assert title == expected_title


@pytest.mark.parametrize(
    ("full_title", "expected_title"),
    [
        ("The 100: Season 1: Pilot (Episode 1)", "The 100"),
        ("Hunter X Hunter (2011): Season 1_hook_primary_16x9", "Hunter X Hunter (2011)"),
        ("3 Body Problem: Season 1 Teaser 4", "3 Body Problem"),
    ],
)
def test_extract_track_name_from_title_does_not_match(os_client_mock, full_title: str, expected_title: str):
    loader = ContentLoader(
        user_uuid=TEST_UUID, data_dir_path=PATH_WHICH_DOES_NOT_EXIST, data_type=DataType.Content, client=os_client_mock
    )
    title = loader._extract_track_name_from_title(title=full_title)
    assert not title == expected_title
