from types import GeneratorType

from services.file_service.application.utils import find_keys_in_dict, peek_generator


def test_find_keys_in_dict():
    tested_value = "value"
    tested_nested_value = "nested_value"
    # Arrange
    test_dictionary = {"Item1": tested_value, "Item2": {"NestedItem": tested_nested_value}}
    # Act
    result = find_keys_in_dict(test_dictionary, "Item1")
    item = peek_generator(result)
    # Assert
    assert isinstance(result, GeneratorType)
    assert item == tested_value
    # Act
    result = find_keys_in_dict(test_dictionary, "NestedItem")
    item = peek_generator(result)
    # Assert
    assert isinstance(result, GeneratorType)
    assert item == tested_nested_value
