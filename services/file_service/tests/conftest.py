import asyncio
import os
from datetime import timed<PERSON><PERSON>
from typing import As<PERSON><PERSON>enerator, <PERSON>wai<PERSON>, Callable
from unittest.mock import <PERSON><PERSON>ock

import pytest

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.enums.metadata import Organization
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.use_cases.export.process_data_export_use_case import ProcessDataExportUseCase
from services.file_service.dependency_bootstrapper import DependencyBootstrapper


@pytest.fixture
def _all_organizations():
    return [organization.value for organization in Organization]


@pytest.fixture
def _all_exportable_types():
    return [data_type.value for data_type in ExportableType]


@pytest.fixture(scope="session")
async def dependency_bootstrapper() -> AsyncGenerator[DependencyBootstrapper, None]:
    bootstrapper = DependencyBootstrapper().build()
    yield bootstrapper
    await bootstrapper.cleanup()


@pytest.fixture(scope="session")
def depr_event_repository(dependency_bootstrapper: DependencyBootstrapper) -> DeprEventRepository:
    return dependency_bootstrapper.get(interface=DeprEventRepository)


@pytest.fixture(scope="session")
def event_repository(dependency_bootstrapper: DependencyBootstrapper) -> EventRepository:
    return dependency_bootstrapper.get(interface=EventRepository)


@pytest.fixture(scope="session")
def template_repository(dependency_bootstrapper: DependencyBootstrapper) -> TemplateRepository:
    return dependency_bootstrapper.get(interface=TemplateRepository)


@pytest.fixture(scope="session")
def contact_repository(dependency_bootstrapper: DependencyBootstrapper) -> ContactRepository:
    return dependency_bootstrapper.get(interface=ContactRepository)


@pytest.fixture(scope="session")
def use_case_repository(dependency_bootstrapper: DependencyBootstrapper) -> UseCaseRepository:
    return dependency_bootstrapper.get(interface=UseCaseRepository)


@pytest.fixture(scope="session")
def plan_repository(dependency_bootstrapper: DependencyBootstrapper) -> PlanRepository:
    return dependency_bootstrapper.get(interface=PlanRepository)


@pytest.fixture(scope="session")
def environment_repository(dependency_bootstrapper: DependencyBootstrapper) -> EnvironmentRepository:
    return dependency_bootstrapper.get(interface=EnvironmentRepository)


@pytest.fixture(scope="session")
def search_service(dependency_bootstrapper: DependencyBootstrapper) -> DocumentSearchService:
    return dependency_bootstrapper.get(interface=DocumentSearchService)


@pytest.fixture(scope="session")
def object_storage_service(dependency_bootstrapper: DependencyBootstrapper) -> ObjectStorageService:
    return dependency_bootstrapper.get(interface=ObjectStorageService)


@pytest.fixture(scope="session")
async def member_user_repository(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserRepository:
    return dependency_bootstrapper.get(interface=MemberUserRepository)


@pytest.fixture(scope="session")
async def export_task_repository(dependency_bootstrapper: DependencyBootstrapper) -> ExportTaskRepository:
    return dependency_bootstrapper.get(interface=ExportTaskRepository)


@pytest.fixture(scope="session")
def message_broker_client(dependency_bootstrapper: DependencyBootstrapper) -> MessageBrokerClient:
    return dependency_bootstrapper.get(interface=MessageBrokerClient)


@pytest.fixture(scope="session")
def member_user_settings(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserSettingsRepository:
    return dependency_bootstrapper.get(interface=MemberUserSettingsRepository)


@pytest.fixture(scope="session")
def data_export_use_case(dependency_bootstrapper: DependencyBootstrapper) -> ProcessDataExportUseCase:
    return dependency_bootstrapper.get(interface=ProcessDataExportUseCase)


@pytest.fixture(scope="session")
def extension_run_repo(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionRunRepository:
    return dependency_bootstrapper.get(interface=ExtensionRunRepository)


@pytest.fixture(scope="session")
def extension_result_repo(dependency_bootstrapper: DependencyBootstrapper) -> ExtensionResultRepository:
    return dependency_bootstrapper.get(interface=ExtensionResultRepository)


@pytest.fixture(scope="function")
async def user_factory(
    member_user_repository: MemberUserRepository,
) -> AsyncGenerator[Callable[[], Awaitable[MemberUser]], None]:
    created_users: list[MemberUser] = []

    async def create_user() -> MemberUser:
        user = MemberUserBuilder().build()
        user = await member_user_repository.insert_or_update(user)
        assert user is not None
        created_users.append(user)
        return user

    yield create_user

    # Cleanup code: delete all created users
    for user_to_delete in created_users:
        await member_user_repository.delete(user_to_delete)


@pytest.fixture(scope="function")
async def user_headers_factory(
    user_factory: Callable[[], Awaitable[MemberUser]],
) -> AsyncGenerator[Callable[[], Awaitable[tuple[MemberUser, dict]]], None]:
    async def yield_user_with_headers():
        user = await user_factory()
        return user, {
            "Authorization": (
                f"Bearer {generate_access_token(user_uuid=user.user_uuid, time_delta=timedelta(minutes=10))}"
            )
        }

    yield yield_user_with_headers


@pytest.fixture(scope="module")
def os_client_mock(os_empty_index_response):  # pylint: disable=redefined-outer-name
    local_client_mock = MagicMock()
    local_client_mock.search = MagicMock()
    local_client_mock.search.return_value = os_empty_index_response
    return local_client_mock


@pytest.fixture(scope="session")
def os_empty_index_response():
    return {
        "took": 0,
        "timed_out": False,
        "_shards": {"total": 2, "successful": 2, "skipped": 0, "failed": 0},
        "hits": {"total": {"value": 0, "relation": "eq"}, "max_score": None, "hits": []},
        # in future, we may also want to add dummy aggregations here
    }


@pytest.fixture(scope="session")
def event_loop():
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Does not run integration test in normal pytest invocation
def pytest_collection_modifyitems(config, items):
    # Get the value of the environment variable and command-line option
    pipeline_run = os.getenv("PIPELINE_RUN")
    marks_option = config.getoption("-m", "")
    non_skipped_tests = []

    if pipeline_run:
        if "integration" not in marks_option:
            skip_integration = pytest.mark.skip(reason="skipped because it is an integration test")
            for item in items:
                if any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_integration)
                else:
                    non_skipped_tests.append(item)
        else:
            skip_non_integration = pytest.mark.skip(reason="skipped because it not an integration test")
            for item in items:
                if not any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_non_integration)
                else:
                    non_skipped_tests.append(item)
