from datetime import datetime, timezone
from typing import List

from services.base.application.event_models.upload_event_model import UploadEventModel
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.domain.constants.messaging import ATT_NAME_LOADING_EVENT, MessageTopics
from services.base.message_queue.message_handler_base import UseCaseHandlerBase
from services.base.message_queue.utils import create_string_message_attribute
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.file_service.application.event_models.extract_event_model import ExtractEventModel
from services.file_service.application.use_cases.extract_use_case import ExtractUseCase
from services.file_service.dependency_bootstrapper import get_message_broker_transient
from services.file_service.utils.paths import get_user_root_folder
from settings.app_config import settings
from settings.app_secrets import secrets


class ExtractHandler(UseCaseHandlerBase):
    def __init__(self, message_broker: MessageBrokerClient):
        self._message_broker = message_broker

    @classmethod
    def listen_to(cls) -> List[MessageTopics]:
        return [MessageTopics.TOPIC_UPLOAD_FINISHED]

    def execute(self, message_body: dict, *args, **kwargs):
        super()._do_execute_sync(triggering_event_model=UploadEventModel(**message_body), use_case=ExtractUseCase())

    @staticmethod
    def initialize_and_execute(*args, **kwargs):
        TelemetryInstrumentor.initialize(service_name="file_service_worker", settings=settings, secrets=secrets)
        return ExtractHandler(message_broker=get_message_broker_transient()).execute(*args, **kwargs)

    def publish[T](self, triggering_event: T) -> None:
        self._message_broker.publish_topic(
            topic_name=MessageTopics.TOPIC_EXTRACT_FINISHED.value,
            message_body=ExtractEventModel(
                user_uuid=triggering_event.user_uuid,
                timestamp=datetime.now(timezone.utc).isoformat(),
                provider=triggering_event.provider,
                dir_path=get_user_root_folder(triggering_event.user_uuid),
                fallback_timezone=triggering_event.fallback_timezone,
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_LOADING_EVENT, MessageTopics.TOPIC_EXTRACT_FINISHED.value
            ),
        )
