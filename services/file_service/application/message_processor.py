from typing import Dict, Type

from services.base.message_queue.message_handler_base import Use<PERSON><PERSON><PERSON>andlerBase
from services.base.message_queue.message_processor_base import MessageProcessorBase
from services.file_service.application.event_handler.extract_handler import ExtractHandler
from services.file_service.application.event_handler.log_on_upload_handler import LogOnUploadHandler
from services.file_service.application.event_handler.process_handler import ProcessHandler
from services.file_service.application.event_handler.takeout_export_scheduled_handler import (
    TakeoutExportScheduledHandler,
)
from services.file_service.constants import (
    EXTRACT_HANDLER_NAME,
    LOG_ON_UPLOAD_HANDLER_NAME,
    PROCESS_HANDLER_NAME,
    TAKEOUT_EXPORT_SCHEDULED_EVENT_HANDLER,
)


class FileServiceMessageProcessor(MessageProcessorBase):
    def __init__(self):
        self._use_case_handlers: Dict[str, Type[UseCaseHandlerBase]] = {
            EXTRACT_HANDLER_NAME: ExtractHandler,
            PROCESS_HANDLER_NAME: <PERSON><PERSON><PERSON><PERSON>,
            LOG_ON_UPLOAD_HANDLER_NAME: LogOnUploadHandler,
            TAKEOUT_EXPORT_SCHEDULED_EVENT_HANDLER: TakeoutExportScheduledHandler,
        }

    @property
    def use_case_handlers(self) -> Dict[str, Type[UseCaseHandlerBase]]:
        return self._use_case_handlers
