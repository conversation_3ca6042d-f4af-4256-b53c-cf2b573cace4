# -*- coding: utf-8 -*-
import csv
import json
import logging
import os
from datetime import datetime
from typing import Optional
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_last_timestamp
from services.base.infrastructure.database.opensearch.query_methods.utils import is_datetime_not_newer_than_datetime
from services.file_service.application.converters import datetime_to_iso_converter
from services.file_service.application.file_handlers import find_file_paths_matching_regex_format
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.fitbit_loaders.fitbit_constants import FITBIT_ROOT_PATH, FITBIT_SLEEP
from services.file_service.application.loaders.fitbit_loaders.utils import get_person_folder
from settings.app_constants import MESSAGE_NO_FILES_FOUND, MESSAGE_UNABLE_TO_LOAD


class DeviceTemperatureLoader(FileLoaderBase):
    """Loader for Fit Bit export file"""

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_paths = []

        fitbit_sleep_path = os.path.join(get_person_folder(os.path.join(data_dir_path, FITBIT_ROOT_PATH)), FITBIT_SLEEP)
        self.search_path = os.path.join(data_dir_path, fitbit_sleep_path)

    def load_files_data(self) -> None:
        regex_format = r"^Device Temperature"
        self.file_paths = list(find_file_paths_matching_regex_format(self.search_path, regex_format))
        if not self.file_paths:
            logging.exception("%s! Folder: %s", MESSAGE_NO_FILES_FOUND, self.search_path)

        return self.process_data()

    def process_data(self) -> None:
        last_timestamp = get_last_timestamp(self.user_uuid, self.client, self.data_type)
        for file_path in self.file_paths:
            self._process_device_temperature_files(file_path, last_timestamp)

    def _process_device_temperature_files(self, file_path: str, last_timestamp: Optional[datetime]) -> None:
        entries = []
        try:
            with open(file_path, encoding="utf8") as csv_file:
                reader = csv.DictReader(csv_file)
                for row in reader:
                    output_row = dict(row)
                    output_row[DocumentLabels.TIMESTAMP] = self._parse_tz_datetime(
                        self._process_recorded_time(row.get("recorded_time"))
                    )
                    if is_datetime_not_newer_than_datetime(output_row[DocumentLabels.TIMESTAMP], last_timestamp):
                        continue
                    output_row[DocumentLabels.USER_UUID] = str(self.user_uuid)

                    entries.append(json.dumps(output_row, default=json_serializer))
                    self._commit_if_limit_reached(entries)

            self._commit(entries=entries)

        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, file_path)

    def _process_recorded_time(self, input_time: str) -> str:
        date = input_time[0:10]
        time = input_time[11:16]
        return datetime_to_iso_converter(time, date, "%Y-%m-%d %H:%M")
