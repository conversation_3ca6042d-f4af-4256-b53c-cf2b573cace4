# -*- coding: utf-8 -*-
import json
import logging
import os
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_last_timestamp
from services.base.infrastructure.database.opensearch.query_methods.utils import is_datetime_not_newer_than_datetime
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.google_loaders.google_constants import GOOGLE_TAKEOUT_YOUTUBE_PATH
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class YoutubeLikesLoader(FileLoaderBase):
    """Loader for Youtube likes file"""

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_path = ""
        self.search_path = os.path.join(data_dir_path, GOOGLE_TAKEOUT_YOUTUBE_PATH)

    def load_files_data(self) -> None:
        self.file_path = os.path.join(self.search_path, "likes.json")
        return self.process_data()

    def process_data(self) -> None:
        last_timestamp = get_last_timestamp(self.user_uuid, self.client, self.data_type)
        entries = []
        try:
            with open(self.file_path, encoding="utf8") as json_file:
                data: list = json.load(json_file)
                data_entry: dict
                for data_entry in data:
                    data_entry[DocumentLabels.USER_UUID] = str(self.user_uuid)
                    data_entry[DocumentLabels.TIMESTAMP] = self._parse_tz_datetime(data_entry["snippet"]["publishedAt"])
                    if is_datetime_not_newer_than_datetime(data_entry[DocumentLabels.TIMESTAMP], last_timestamp):
                        continue
                    entries.append(json.dumps(data_entry, default=json_serializer))
                    self._commit_if_limit_reached(entries)

            self._commit(entries=entries)

        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, self.file_path)
