import os
from typing import Any, Dict, Optional, Tuple
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.google_loaders.file_loaders.myactivity.json_loaders.myactivity_json_loader_base import (
    MyActivityJsonLoaderBase,
)
from services.file_service.application.loaders.google_loaders.google_constants import (
    GOOGLE_TAKEOUT_MYACTIVITY_YOUTUBE_PATH,
)


class MyActivityYoutubeJsonLoader(MyActivityJsonLoaderBase):
    """Loader for youtube my activity JSON file"""

    _source = "youtube"

    def __init__(
        self,
        user_uuid: UUID,
        data_type: DataType,
        data_dir_path: str,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(
            user_uuid=user_uuid,
            data_type=data_type,
            data_dir_path=os.path.join(data_dir_path, GOOGLE_TAKEOUT_MYACTIVITY_YOUTUBE_PATH),
            client=client,
            fallback_timezone=fallback_timezone,
        )

    def _get_entity_and_url(self, data_entry: Dict[str, Any]) -> Tuple[Optional[str], Optional[str]]:
        title_url: str = data_entry.get(self.KEY_TITLE_URL, None)
        if not title_url:
            return None, None
        title_url = title_url.replace(self.URL_PREFIX, "")
        return data_entry[self.KEY_HEADER], title_url
