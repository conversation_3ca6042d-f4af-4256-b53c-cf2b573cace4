# -*- coding: utf-8 -*-
import logging
import os
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional, Tu<PERSON>
from uuid import UUID
from zoneinfo import ZoneInfo

import ij<PERSON>
from opensearchpy import OpenSearch
from pydantic import Field

from services.base.application.loaders.location_loader_base import LocationLoaderBase
from services.base.application.loaders.location_loader_utils import (
    LocationLoaderUtils,
    LocationStatisticsResponse,
)
from services.base.application.message_broker_client import Message<PERSON>roker<PERSON>lient
from services.base.application.utils.metadata import create_metadata
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.activity_type import ActivityType, ActivityTypeInput
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.location import (
    Location,
    PlaceVisitDetails,
    WaypointDetails,
)
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel
from services.file_service.application.converters import e7_to_coordinates
from services.file_service.application.file_handlers import find_all_file_paths_by_ext
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.google_loaders.google_constants import (
    GOOGLE_TAKEOUT_SEMANTIC_LOCATION_PATH,
)
from services.file_service.dependency_bootstrapper import bootstrapper
from settings.app_constants import MESSAGE_NO_FILES_FOUND

semantic_type_to_place_name_mapping: Dict[str, str] = {
    "TYPE_HOME": "Home",
}


class SourceInfo(BaseDataModel):
    device_tag: int = Field(alias="deviceTag")


class StartLocation(BaseDataModel):
    latitude_e7: int = Field(alias="latitudeE7")
    longitude_e7: int = Field(alias="longitudeE7")
    source_info: Optional[SourceInfo] = Field(default=None, alias="sourceInfo")


class EndLocation(BaseDataModel):
    latitude_e7: int = Field(alias="latitudeE7")
    longitude_e7: int = Field(alias="longitudeE7")
    source_info: Optional[SourceInfo] = Field(default=None, alias="sourceInfo")


class Duration(BaseDataModel):
    start_timestamp: str = Field(alias="startTimestamp")
    end_timestamp: str = Field(alias="endTimestamp")


class Activity(BaseDataModel):
    activity_type: Optional[ActivityTypeInput] = Field(alias="activityType", default=None)
    probability: float = Field(alias="probability")


class Waypoint(BaseDataModel):
    lat_e7: int = Field(alias="latE7")
    lng_e7: int = Field(alias="lngE7")


class RoadSegmentItem(BaseDataModel):
    place_id: str = Field(alias="placeId")
    duration: Optional[str] = Field(default=None, alias="duration")


class WaypointPath(BaseDataModel):
    waypoints: List[Waypoint] = Field(alias="waypoints")
    source: str = Field(alias="source")
    road_segment: Optional[List[RoadSegmentItem]] = Field(default=None, alias="roadSegment")
    distance_meters: Optional[float] = Field(alias="distanceMeters", default=None)
    travelMode: Optional[str] = Field(alias="travelMode", default=None)
    confidence: Optional[float] = Field(alias="confidence", default=None)


class ParkingEventLocation(BaseDataModel):
    latitude_e7: Optional[int] = Field(default=None, alias="latitudeE7")
    longitude_e7: Optional[int] = Field(default=None, alias="longitudeE7")
    accuracy_metres: Optional[int] = Field(default=None, alias="accuracyMetres")


class ParkingEvent(BaseDataModel):
    location: Optional[ParkingEventLocation] = Field(default=None, alias="location")
    method: str = Field(alias="method")
    location_source: str = Field(alias="locationSource")
    timestamp: str = Field(alias="timestamp")


class Point(BaseDataModel):
    lat_e7: int = Field(alias="latE7")
    lng_e7: int = Field(alias="lngE7")
    accuracy_meters: int = Field(alias="accuracyMeters")
    timestamp: str = Field(alias="timestamp")


class SimplifiedRawPath(BaseDataModel):
    points: List[Point] = Field(alias="points")
    source: Optional[str] = Field(default=None, alias="source")
    distance_meters: Optional[float] = Field(default=None, alias="distanceMeters")


class ActivitySegment(BaseDataModel):
    start_location: StartLocation = Field(alias="startLocation")
    end_location: EndLocation = Field(alias="endLocation")
    duration: Duration = Field(alias="duration")
    distance: int = Field(alias="distance")
    activity_type: Optional[ActivityTypeInput] = Field(alias="activityType", default=None)
    confidence: Optional[str] = Field(alias="confidence", default=None)
    activities: List[Activity] = Field(alias="activities")
    waypoint_path: Optional[WaypointPath] = Field(default=None, alias="waypointPath")
    parking_event: Optional[ParkingEvent] = Field(default=None, alias="parkingEvent")
    simplified_raw_path: Optional[SimplifiedRawPath] = Field(default=None, alias="simplifiedRawPath")


class PlaceVisitLocation(BaseDataModel):
    latitude_e7: int = Field(alias="latitudeE7")
    longitude_e7: int = Field(alias="longitudeE7")
    place_id: str = Field(alias="placeId")
    address: str = Field(alias="address")
    semantic_type: Optional[str] = Field(default=None, alias="semanticType")
    source_info: Optional[SourceInfo] = Field(alias="sourceInfo", default=None)
    location_confidence: Optional[float] = Field(alias="locationConfidence", default=None)
    calibrated_probability: Optional[float] = Field(alias="calibratedProbability", default=None)
    name: Optional[str] = Field(default=None, alias="name")


class OtherCandidateLocation(BaseDataModel):
    latitude_e7: int = Field(alias="latitudeE7")
    longitude_e7: int = Field(alias="longitudeE7")
    place_id: str = Field(alias="placeId")
    address: Optional[str] = Field(default=None, alias="address")
    semantic_type: Optional[str] = Field(default=None, alias="semanticType")
    location_confidence: Optional[float] = Field(alias="locationConfidence", default=None)
    calibrated_probability: Optional[float] = Field(alias="calibratedProbability", default=None)
    name: Optional[str] = Field(default=None, alias="name")


class PlaceVisit(BaseDataModel):
    location: PlaceVisitLocation = Field(alias="location")
    duration: Duration = Field(alias="duration")
    place_confidence: str = Field(alias="placeConfidence")
    center_lat_e7: int = Field(alias="centerLatE7")
    center_lng_e7: int = Field(alias="centerLngE7")
    visit_confidence: Optional[int] = Field(alias="visitConfidence", default=None)
    other_candidate_locations: List[OtherCandidateLocation] = Field(alias="otherCandidateLocations")
    edit_confirmation_status: Optional[str] = Field(alias="editConfirmationStatus", default=None)
    location_confidence: Optional[int] = Field(alias="locationConfidence", default=None)
    place_visit_type: Optional[str] = Field(alias="placeVisitType", default=None)
    place_visit_importance: Optional[str] = Field(alias="placeVisitImportance", default=None)


class TimelineObject(BaseDataModel):
    activity_segment: Optional[ActivitySegment] = Field(default=None, alias="activitySegment")
    place_visit: Optional[PlaceVisit] = Field(default=None, alias="placeVisit")
    timestamp: Optional[str] = Field(default=None, alias="timestampMs")


class SemanticLocationInput(BaseDataModel):
    timeline_objects: List[TimelineObject] = Field(alias="timelineObjects")


class SemanticLocationLoader(LocationLoaderBase, FileLoaderBase):
    """Loader for Google Semantic Location History takeout files"""

    KEY_PLACE_VISIT = "placeVisit"
    KEY_ACTIVITY = "activitySegment"

    TIMESTAMP_NAME = "timestamp"
    source = "location_history"
    PROVIDER = Provider.GOOGLE

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(
            user_uuid=user_uuid,
            data_type=data_type,
            client=client,
            fallback_timezone=fallback_timezone,
        )

        self._message_broker_client = bootstrapper.get(interface=MessageBrokerClient)
        self.group_entries_by_indexes = isinstance(data_type, dict)
        self.location_files_paths = []
        self.search_path = os.path.join(data_dir_path, GOOGLE_TAKEOUT_SEMANTIC_LOCATION_PATH)

    def load_files_data(self) -> None:
        file_paths = list(find_all_file_paths_by_ext(self.search_path, ".json"))

        if len(file_paths) == 0:
            logging.exception("%s! Folder: %s", MESSAGE_NO_FILES_FOUND, self.search_path)
            return

        for file_path in file_paths:
            self.location_files_paths.append(file_path)

        return self.process_data()

    def process_data(self) -> None:
        for file_path in self.location_files_paths:
            try:
                self._process_input_data(
                    input_entries=ijson.items(
                        open(os.path.normpath(file_path), encoding="utf8"),
                        "timelineObjects.item",
                    ),
                )

            except FileNotFoundError:
                logging.exception("Unable to load data from file! File path: %s", file_path)
            except Exception as error:  # pylint:disable=broad-except
                logging.exception("Error processing entry: %s", error)

    def _process_input_data(
        self,
        input_entries: List[dict],
        load_aggregation_interval: Optional[timedelta] = None,
    ) -> None:
        to_be_committed_entries: List[str] = []
        # Defines list of entries to be parsed into single database entry
        for input_entry in input_entries:
            try:
                output_data = self._parse_data_to_entry(TimelineObject(**input_entry))
                # Append to be committed entries
                to_be_committed_entries.append(output_data.model_dump_json(by_alias=True))

            except Exception as error:  # pylint:disable=broad-except
                logging.exception("Error processing entry: %s, %s", input_entry, error)
            self._commit_if_limit_reached(to_be_committed_entries)
        self._commit(entries=to_be_committed_entries)

    def _parse_data_to_entry(self, input_entry: TimelineObject) -> Location:
        """Parses data to output location entry"""

        top_entry: PlaceVisit | ActivitySegment = (
            input_entry.activity_segment if input_entry.activity_segment else input_entry.place_visit
        )
        starting_datetime = self._parse_tz_datetime(top_entry.duration.start_timestamp)
        ending_datetime = self._parse_tz_datetime(top_entry.duration.end_timestamp)

        place_details, location_details, statistics = None, None, None
        average_coordinates, waypoint_details = None, None

        if isinstance(top_entry, PlaceVisit):
            place_details: PlaceVisitDetails = self._parse_place_visit(place_visit_entry=top_entry)
            start_latitude, start_longitude = e7_to_coordinates(
                latitudeE7=top_entry.location.latitude_e7,
                longitudeE7=top_entry.location.longitude_e7,
            )

            activity_type = ActivityType.STILL
            activity_type_probability = None
            # Empty fields in case of place visit type

            (
                end_latitude,
                end_longitude,
                _,
                _,
                _,
            ) = (None, None, None, None, None)
        else:
            (
                location_details,
                statistics,
                activity_type,
                activity_type_probability,
            ) = self._parse_activity_segment(top_entry)
            start_latitude, start_longitude = e7_to_coordinates(
                latitudeE7=top_entry.start_location.latitude_e7,
                longitudeE7=top_entry.start_location.longitude_e7,
            )
            end_latitude, end_longitude = e7_to_coordinates(
                latitudeE7=top_entry.end_location.latitude_e7,
                longitudeE7=top_entry.end_location.longitude_e7,
            )

            if location_details:
                average_coordinates = CoordinatesModel(
                    latitude=statistics.latitude_sum / statistics.latitude_count,
                    longitude=statistics.longitude_sum / statistics.longitude_count,
                )
                waypoint_details = (
                    location_details if LocationLoaderUtils.should_details_be_included(statistics) else None
                )

        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            organization=Organization.GOOGLE,
            data_integrity=DataIntegrity.MEDIUM,
        )
        output_location = Location(
            timestamp=starting_datetime,
            duration=int((ending_datetime - starting_datetime).total_seconds()) if ending_datetime else None,
            end_time=ending_datetime,
            start_coordinates=CoordinatesModel(latitude=start_latitude, longitude=start_longitude),
            end_coordinates=CoordinatesModel(latitude=end_latitude, longitude=end_longitude) if end_latitude else None,
            distance=getattr(top_entry, "distance", None),
            average_coordinates=average_coordinates,
            waypoint_details=waypoint_details,
            place_visit_details=place_details or None,
            activity_type=activity_type,
            activity_type_probability=activity_type_probability,
            metadata=metadata,
        )
        return output_location

    def _parse_activity_segment(self, activity_segment_entry: ActivitySegment) -> Tuple[
        List[WaypointDetails],
        LocationStatisticsResponse,
        Optional[ActivityType],
        Optional[float],
    ]:
        statistics = LocationStatisticsResponse()
        location_detail_list: List[WaypointDetails] = []

        entry_collection: SimplifiedRawPath | WaypointPath | None = (
            activity_segment_entry.waypoint_path or activity_segment_entry.simplified_raw_path or None
        )

        activity_type = None
        activity_type_probability = None

        try:
            activity = activity_segment_entry.activities[0]
            activity_type = ActivityType(activity.activity_type.value.lower()) if activity.activity_type else None
            activity_type_probability = round(activity.probability / 100, 4) if activity.probability else None

        except Exception as error:
            logging.warning(
                "Could not load activity from entry %s, error: %s",
                activity_segment_entry.activities,
                error,
            )

        # The specific location paths might be present in different field than expected
        if not entry_collection:
            return (
                location_detail_list,
                statistics,
                activity_type,
                activity_type_probability,
            )

        entry_collection: List[Waypoint] | List[Point] = entry_collection.waypoints or entry_collection.points or []

        for entry in entry_collection:
            try:
                latitude, longitude = e7_to_coordinates(latitudeE7=entry.lat_e7, longitudeE7=entry.lng_e7)
                altitude = None
                statistics = LocationLoaderUtils.calculate_statistics(statistics, latitude, longitude, altitude)
                location_detail_list.append(
                    WaypointDetails(
                        coordinates=CoordinatesModel(latitude=latitude, longitude=longitude),
                        timestamp=self._parse_tz_datetime(getattr(entry, "timestamp", None)),
                    )
                )
            except Exception as e:  # pylint:disable=broad-except
                logging.warning(
                    "Could not process location detail entry %s, skipping. %s",
                    activity_segment_entry,
                    e,
                )

        return (
            location_detail_list,
            statistics,
            activity_type,
            activity_type_probability,
        )

    def _parse_place_visit(self, place_visit_entry: PlaceVisit) -> PlaceVisitDetails:
        try:
            if not place_visit_entry.location:
                raise Exception("place location was not provided")

            place_visit_entry.location.name = (
                place_visit_entry.location.name
                or semantic_type_to_place_name_mapping.get(place_visit_entry.location.semantic_type)
                or place_visit_entry.location.address
                or None
            )

            if not place_visit_entry.location.name:
                raise Exception("place location name was not provided")

            return PlaceVisitDetails(
                name=place_visit_entry.location.name,
                id=place_visit_entry.location.place_id,
                address=place_visit_entry.location.address,
                confidence=place_visit_entry.location.location_confidence,
            )

        except Exception as e:  # pylint:disable=broad-except
            logging.warning(
                "Could not process location detail entry %s, skipping. %s",
                place_visit_entry,
                e,
            )
