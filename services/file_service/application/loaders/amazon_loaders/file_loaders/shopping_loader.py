import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tu<PERSON>
from uuid import UUID
from zoneinfo import ZoneInfo

import pandas as pd
from opensearchpy import OpenSearch

from services.base.application.utils.metadata import create_metadata
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import DataIntegrity, Organization, Service
from services.base.domain.schemas.shopping_activity import ShoppingActivity, ShoppingActivityItemDetail
from services.file_service.application.file_handlers import find_file_names_matching_regex_format
from services.file_service.application.loaders.amazon_loaders.amazon_constants import AMAZON_ROOT_PATH
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.domain.amazon_shopping import ShoppingModel
from services.file_service.domain.labels.amazon_shopping import AmazonShoppingLabels
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class ShoppingLoader(FileLoaderBase):
    """Loader for Amazon Takeout files"""

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_name = ""
        self.path = ""
        self.search_path = os.path.join(data_dir_path, AMAZON_ROOT_PATH)

    def load_files_data(self) -> None:
        # Amazon extracts single file with specific time period with the name format: DD-MMM-YYYY_to_DD-MMM-YYYY.csv
        # For example, 01-Jan-2006_to_24-Feb-2021.csv This loader selects the first such file.

        regex_format = r"\d{2}-\w{3}-\d{4}_to_\d{2}-\w{3}-\d{4}.csv"
        file_names = find_file_names_matching_regex_format(self.search_path, regex_format)
        # Selects first item
        try:
            self.file_name = next(file_names)
        except StopIteration:
            logging.exception("%s! Search path: %s", MESSAGE_UNABLE_TO_LOAD, self.search_path)
        self.path = os.path.join(self.search_path, self.file_name)

        return self.process_data()

    def process_data(self) -> None:
        file_path = self.path
        entries = []

        try:
            df: pd.DataFrame = pd.read_csv(file_path, na_filter=False)
        except FileNotFoundError as error:
            logging.error(f"Could not find a CSV file with PATH: {file_path}. Got error: {repr(error)}")
            return None

        df_dict: List[Dict] = df.to_dict(orient="records")
        shopping_details: List[ShoppingActivityItemDetail] = []
        for i, record in enumerate(df_dict):
            sm = ShoppingModel(**self._transform_raw_dict_to_domain(record))
            shopping_details.append(ShoppingActivityItemDetail(**sm.model_dump()))

            # Checks if the next record is part of the same order id,
            # because we want to create a single object for one order
            if self._peek_next_order_id(index=i, values=df_dict) == sm.order_id:
                continue

            timestamp = self._parse_tz_datetime(
                datetime.strptime(record.get(AmazonShoppingLabels.LABEL_ORDER_DATE), "%m/%d/%y")
            )
            amazon_shopping = ShoppingActivity(
                timestamp=timestamp,
                order_id=sm.order_id,
                item_detail=shopping_details,
                metadata=create_metadata(
                    user_uuid=self.user_uuid,
                    organization=Organization.AMAZON,
                    service=Service.SEARCH,
                    data_integrity=DataIntegrity.MEDIUM,
                ),
            )
            shopping_details = []

            entries.append(amazon_shopping.model_dump_json(by_alias=True))
            self._commit_if_limit_reached(entries)
        self._commit(entries=entries)

    @classmethod
    def _remove_dollar_sign(cls, value: str) -> str:
        return value.replace("$", "")

    @classmethod
    def _split_carrier_info(cls, value: str) -> Tuple[str, Optional[str]]:
        """Removes the parentheses and splits values from the raw string input. E.g. AMZN(12345) -> (AMZN, 12345)"""
        if not value:
            raise ValueError(f'Improper value formatting. Does not match %s(%s). Got "{value}" instead.')

        v = value.split("(")
        if len(v) == 1:
            # Does not contain parentheses, assume only carrier name
            return value, None

        if not v[1].endswith(")"):
            raise ValueError(f'Improper value formatting. Does not match %s(%s). Got "{value}" instead.')

        carrier_name = v[0]
        carrier_no = v[1].replace(")", "")
        if not carrier_no:
            return carrier_name, None

        return carrier_name, carrier_no

    @classmethod
    def _peek_next_order_id(cls, index: int, values: List[Dict]) -> Optional[str]:
        """Peeks the next index and returns the order ID associated with it"""
        next_index = index + 1
        if next_index >= len(values):
            return None
        return values[next_index].get(AmazonShoppingLabels.LABEL_ORDER_ID)

    def _transform_raw_dict_to_domain(self, input_record: Dict) -> Dict:
        """Transforms the raw dictionary values provided by the DataFrame into domain-compliant values"""
        # Transform prices to not include '$'
        input_record[AmazonShoppingLabels.LABEL_LIST_PRICE_PER_UNIT] = self._remove_dollar_sign(
            input_record[AmazonShoppingLabels.LABEL_LIST_PRICE_PER_UNIT]
        )
        input_record[AmazonShoppingLabels.LABEL_PURCHASE_PRICE_PER_UNIT] = self._remove_dollar_sign(
            input_record[AmazonShoppingLabels.LABEL_PURCHASE_PRICE_PER_UNIT]
        )
        input_record[AmazonShoppingLabels.LABEL_ITEM_SUBTOTAL] = self._remove_dollar_sign(
            input_record[AmazonShoppingLabels.LABEL_ITEM_SUBTOTAL]
        )
        input_record[AmazonShoppingLabels.LABEL_ITEM_SUBTOTAL_TAX] = self._remove_dollar_sign(
            input_record[AmazonShoppingLabels.LABEL_ITEM_SUBTOTAL_TAX]
        )
        input_record[AmazonShoppingLabels.LABEL_ITEM_TOTAL] = self._remove_dollar_sign(
            input_record[AmazonShoppingLabels.LABEL_ITEM_TOTAL]
        )

        # Split a single field into carrier name and carrier tracking number
        try:
            name, tracking_no = self._split_carrier_info(
                input_record[AmazonShoppingLabels.LABEL_CARRIER_NAME_AND_TRACKING_NUMBER]
            )
            input_record[AmazonShoppingLabels.LABEL_CARRIER_NAME] = name
            input_record[AmazonShoppingLabels.LABEL_CARRIER_TRACKING_NUMBER] = tracking_no
        except ValueError as err:
            logging.warning(f"Could not transform the carrier number. {repr(err)}")

        # Convert to None if empty string
        # Default values from DataFrame are <class 'str'>, len: 0
        input_record[AmazonShoppingLabels.LABEL_RELEASE_DATE] = (
            input_record[AmazonShoppingLabels.LABEL_RELEASE_DATE]
            if len(input_record[AmazonShoppingLabels.LABEL_RELEASE_DATE]) > 0
            else None
        )
        input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE] = (
            input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE]
            if len(input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE]) > 0
            else None
        )
        # Convert date fields into datetime ISO strings
        input_record[AmazonShoppingLabels.LABEL_RELEASE_DATE] = self._parse_tz_datetime(
            input_record[AmazonShoppingLabels.LABEL_RELEASE_DATE]
        )
        input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE] = self._parse_tz_datetime(
            datetime.strptime(input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE], "%m/%d/%y")
            if input_record[AmazonShoppingLabels.LABEL_SHIPMENT_DATE]
            else None
        )
        return input_record
