# -*- coding: utf-8 -*-
import json
import logging
import os
import re
from typing import Optional
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_last_timestamp
from services.base.infrastructure.database.opensearch.query_methods.utils import is_datetime_not_newer_than_datetime
from services.file_service.application.converters import timestamp_to_iso_converter
from services.file_service.application.loaders.facebook_loaders.facebook_constants import FACEBOOK_REACTIONS_PATH
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class ReactionsPostsAndCommentsLoader(FileLoaderBase):
    """Loader for exported Facebook reactions (likes and reactions) to posts & comments"""

    KEY_REACTIONS = "reactions"
    KEY_REACTION_TIMESTAMP = "timestamp"
    KEY_DATA = "data"
    KEY_REACTION = "reaction"
    KEY_ACTOR = "actor"
    KEY_TITLE = "title"

    KNOWN_PHRASES = ["likes", "liked", "reacts to", "reacted to"]

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_path = ""
        self.search_path = os.path.join(data_dir_path, FACEBOOK_REACTIONS_PATH)

    def load_files_data(self) -> None:
        self.file_path = os.path.join(self.search_path, "posts_and_comments.json")

        return self.process_data()

    def process_data(self) -> None:
        entries = []
        last_timestamp = get_last_timestamp(
            self.user_uuid,
            self.client,
            self.data_type,
        )
        try:
            with open(self.file_path, encoding="utf8") as json_file:
                data = json.load(json_file)

                if self.KEY_REACTIONS in data:
                    for reaction in data[self.KEY_REACTIONS]:
                        if self.KEY_REACTION_TIMESTAMP in reaction:
                            reaction_timestamp = self._parse_tz_datetime(
                                timestamp_to_iso_converter(reaction[self.KEY_REACTION_TIMESTAMP])
                            )
                            if is_datetime_not_newer_than_datetime(reaction_timestamp, last_timestamp):
                                continue

                            reaction.pop(self.KEY_REACTION_TIMESTAMP)
                        else:
                            reaction_timestamp = None

                        reaction = self._process_reaction(reaction)

                        if reaction is None:
                            continue

                        reaction.update(
                            {
                                DocumentLabels.TIMESTAMP: reaction_timestamp,
                                DocumentLabels.USER_UUID: str(self.user_uuid),
                            }
                        )

                        entries.append(json.dumps(reaction, default=json_serializer))
                        self._commit_if_limit_reached(entries)

        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, self.file_path)

        self._commit(entries=entries)

    def _process_reaction(self, reaction: dict) -> Optional[dict]:
        try:
            if len(reaction[self.KEY_DATA]) > 1:
                logging.warning("Unexpected data while importing facebook reactions")
                return None
            # not an overlook, it really is that nastily nested
            if self.KEY_REACTION not in reaction[self.KEY_DATA][0][self.KEY_REACTION]:
                logging.warning("Missing reaction while importing facebook reactions")
                return None
        except KeyError:
            logging.warning("Unexpected data while importing facebook reactions")
            return None

        # All these 3 should be there, if missing => log and set default as if empty (ON KEY ERROR)
        actor = reaction[self.KEY_DATA][0][self.KEY_REACTION].get(self.KEY_ACTOR, None)
        # there really is a reaction key in a reaction key.....
        reaction_type = reaction[self.KEY_DATA][0][self.KEY_REACTION].get(self.KEY_REACTION, None)
        title = reaction.get(self.KEY_TITLE, None)
        target_user = None  # to be determinated from title
        target_type = None  # -- || --

        # Remove actor - but ONLY FROM THE START! (author should ALWAYS be there)
        title_no_actor = title.replace(actor, "", 1).strip()  # FIRST ONLY!

        found_phrase = ""
        for phrase in self.KNOWN_PHRASES:
            if title_no_actor.startswith(phrase):
                found_phrase = phrase
                break

        if found_phrase == "":
            logging.warning("Unknown reaction phrase (skipping) - maybe something new?")
            return None  # unknown action phrase => assume unknown format and skip the record completely

        # remove the action phrase, but only from the start!
        title_target = title_no_actor.replace(found_phrase, "").strip()

        # keeping this as a subfunction for a better over all code readability
        def remove_additional_info(title_target):
            # remove everything after ":" if there is ":"
            target_type = title_target.split(":")[0]
            # same for " on "
            target_type = target_type.split(" on ")[0]
            # same for " in "
            target_type = target_type.split(" in ")[0]
            # final cleanup
            target_type = target_type.replace(".", "").strip()
            # throw away anyway the rest :: ([1] may not be there, if so assume "label" => throw away)

            return target_type

        possessive_appendix = "'s"
        not_possessive = possessive_appendix not in title_target  # missing "'s"? => unknown or "own" target
        possessive_pronouns_pattern = "((her|his|its|their) )"
        own_pattern = "(own )"
        possessive_patterns = "|".join(
            [
                possessive_pronouns_pattern,
                own_pattern,
                "(" + possessive_pronouns_pattern + " " + own_pattern + ")",
            ]
        )
        possessive_pattern_final = "^(" + possessive_patterns + ")"
        # the parentheses around patterns are super important!
        # if omitted, ^ and "or" grouping doesn't work!
        # .match() (searches from the start) wasn't used to be explicit about the ^ in the pattern
        is_own = re.search(possessive_pattern_final, title_target) is not None
        if not_possessive and not is_own:
            # here only test if it's just an article + word (.) - assume that word is the target type
            # "?:" - non-capturing group - will not end up in matches :)
            target_type_matches = re.search(r"^(?:a|an|the) ([a-zA-Z]+)\.?", title_target)
            if target_type_matches is not None:
                target_type = target_type_matches[1]  # 1 => we only need the matched GROUP not the full match

        elif is_own:
            # maybe regex match on whether the own is a second word at maximum?
            # cases like X likes hiw own post, X likes own post
            # ^([^ ]+ )?own # ^ = start of string, ([^ ]+ )? = maybe word with a space (like "her ")
            target_user = actor

            possessive_replace_pattern = possessive_pronouns_pattern + "?" + own_pattern + "?"
            posessive_matches = re.search("^" + possessive_replace_pattern, title_target)
            own_part = posessive_matches[0]  # matches[0] contains the FULL PATTERN match
            # if matched => replace everything till the first "own_part" (cut after)
            target_type = title_target[len(own_part) :]
            target_type = remove_additional_info(target_type)

        else:
            # possessive: expect stuff between know parts and first "'s" to be the target user
            title_target_parts = title_target.split(possessive_appendix)
            target_user = title_target_parts.pop(0)  # pop/shift&store possible user separately
            target_type = possessive_appendix.join(title_target_parts)  # maybe glue back the rest
            target_type = remove_additional_info(target_type)

            # there were multiple possessive apendix's
            # assume the last one is the right one (not ideal, but will work for lot of cases)
            if possessive_appendix in target_type:
                # build back => + add back at least a single space, since whitespace was removed
                target_parts = target_user + possessive_appendix + " " + target_type
                target_parts = target_type.split(possessive_appendix)
                target_type = target_parts.pop().strip()  # pop with no arg means pop last
                target_user = possessive_appendix.join(target_parts)

        # format for ES
        reaction = {
            "actor": actor,  # store actor in case name changes
            "reaction_type": reaction_type,
            "title": title,
            "target_user": target_user,
            "target_type": target_type,
        }

        return reaction
