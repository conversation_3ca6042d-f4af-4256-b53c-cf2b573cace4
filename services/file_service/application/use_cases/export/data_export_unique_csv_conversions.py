import json
from csv import Dict<PERSON>riter
from datetime import datetime
from typing import Optional
from uuid import UUID

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.constants.extension_labels.single_correlation_labels import SingleCorrelationLabels
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from services.base.domain.schemas.extension_output import ExtensionResult
from services.base.domain.schemas.sleep import SleepDetailFields, SleepEvent, SleepFields, SleepSummaryFields
from services.file_service.application.mappings.csv_mappings.v2_mappings import AdditionalTakeoutStringConstants
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


async def sleep_mapper(sleep_event: SleepEvent) -> Optional[dict]:
    selected_fields = {}
    json_loaded_sleep_event = json.loads(sleep_event.model_dump_json())
    sleep_details = json_loaded_sleep_event.get("sleep_detail", [])

    if not sleep_details:
        return None

    timestamp = sleep_details[0].get(DocumentLabels.TIMESTAMP)
    end_time = sleep_details[-1].get(DocumentLabels.END_TIME) or sleep_details[-1].get(DocumentLabels.TIMESTAMP)
    selected_fields[DocumentLabels.TIMESTAMP] = timestamp
    selected_fields[DocumentLabels.END_TIME] = end_time
    selected_fields[DocumentLabels.DURATION] = (
        datetime.fromisoformat(end_time) - datetime.fromisoformat(timestamp)
    ).total_seconds()
    selected_fields[AdditionalTakeoutStringConstants.FIRST_STAGE_IN_SLEEP_EVENT] = sleep_details[0].get(
        SleepDetailFields.STAGE
    )
    selected_fields[AdditionalTakeoutStringConstants.LAST_STAGE_IN_SLEEP_EVENT] = sleep_details[-1].get(
        SleepDetailFields.STAGE
    )

    sleep_event_summary = json_loaded_sleep_event.get(SleepFields.SLEEP_SUMMARY, {})
    selected_fields[SleepSummaryFields.EFFICIENCY] = sleep_event_summary.get(SleepSummaryFields.EFFICIENCY)
    selected_fields[SleepSummaryFields.AWAKE_SECONDS] = sleep_event_summary.get(SleepSummaryFields.AWAKE_SECONDS)
    selected_fields[SleepSummaryFields.RESTLESS_SECONDS] = sleep_event_summary.get(SleepSummaryFields.RESTLESS_SECONDS)
    selected_fields[SleepSummaryFields.FALL_ASLEEP_SECONDS] = sleep_event_summary.get(
        SleepSummaryFields.FALL_ASLEEP_SECONDS
    )
    selected_fields[SleepSummaryFields.IN_BED_SECONDS] = sleep_event_summary.get(SleepSummaryFields.IN_BED_SECONDS)
    selected_fields[SleepSummaryFields.DEEP_SECONDS] = sleep_event_summary.get(SleepSummaryFields.DEEP_SECONDS)
    selected_fields[SleepSummaryFields.LIGHT_SECONDS] = sleep_event_summary.get(SleepSummaryFields.LIGHT_SECONDS)
    selected_fields[SleepSummaryFields.REM_SECONDS] = sleep_event_summary.get(SleepSummaryFields.REM_SECONDS)
    selected_fields[SleepSummaryFields.IS_MAIN_SLEEP] = sleep_event_summary.get(SleepSummaryFields.IS_MAIN_SLEEP)
    selected_fields[SleepSummaryFields.ASLEEP_SECONDS] = sleep_event_summary.get(SleepSummaryFields.ASLEEP_SECONDS)
    return selected_fields


async def extensions_mapper(
    writer: DictWriter,
    extension_id: UUID,
    extension_result: ExtensionResult,
):
    selected_fields = {}
    model_data_dict = json.loads(extension_result.model_dump_json())
    selected_fields[DocumentLabels.TIMESTAMP] = model_data_dict[DocumentLabels.TIMESTAMP]

    output_dict = model_data_dict[ExtensionLabels.OUTPUT]
    if extension_id == TREND_INSIGHTS_EXTENSION_ID:
        selected_fields[TrendInsightsLabels.ANALYTIC_TYPE] = output_dict[TrendInsightsLabels.ANALYTIC_TYPE]
        selected_fields[TrendInsightsLabels.ANALYTIC_SERIES] = output_dict[TrendInsightsLabels.ANALYTIC_SERIES]
        for series_result in output_dict[TrendInsightsLabels.SERIES_ANALYSIS_RESULTS]:
            output_fields = {}
            evaluation_input = series_result[TrendInsightsLabels.EVALUATION_INPUT]
            evaluation_time_input = evaluation_input[TrendInsightsLabels.TIME_INPUT]
            evaluation_output = series_result[TrendInsightsLabels.EVALUATION_OUTPUT]
            if not evaluation_output:
                continue
            trend_output = evaluation_output[TrendInsightsLabels.TREND]
            statistics_output = evaluation_output[TrendInsightsLabels.STATISTICS]
            output_fields[TrendInsightsLabels.AGGREGATION_INTERVAL] = evaluation_input[
                TrendInsightsLabels.AGGREGATION_INTERVAL
            ]
            output_fields[DocumentLabels.TIME_GTE] = evaluation_time_input[DocumentLabels.TIME_GTE]
            output_fields[DocumentLabels.TIME_LTE] = evaluation_time_input[DocumentLabels.TIME_LTE]
            output_fields[TrendInsightsLabels.CONSECUTIVE_TREND] = (
                trend_output[TrendInsightsLabels.CONSECUTIVE_TREND] if trend_output else None
            )
            output_fields[TrendInsightsLabels.INDEPENDENT_VARIABLES_MEAN] = (
                trend_output[TrendInsightsLabels.INDEPENDENT_VARIABLES_MEAN] if trend_output else None
            )
            output_fields[TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_PREVIOUS_BUCKET] = (
                trend_output[TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_PREVIOUS_BUCKET] if trend_output else None
            )
            output_fields[TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_AGGREGATED_BUCKETS] = (
                trend_output[TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_AGGREGATED_BUCKETS] if trend_output else None
            )
            output_fields[TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_PREVIOUS_BUCKET] = (
                trend_output[TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_PREVIOUS_BUCKET] if trend_output else None
            )
            output_fields[TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_AGGREGATED_BUCKETS] = (
                trend_output[TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_AGGREGATED_BUCKETS]
                if trend_output
                else None
            )
            output_fields[TrendInsightsLabels.MAX] = statistics_output[TrendInsightsLabels.MAX]
            output_fields[TrendInsightsLabels.MIN] = statistics_output[TrendInsightsLabels.MIN]
            output_fields[TrendInsightsLabels.MEAN] = statistics_output[TrendInsightsLabels.MEAN]
            output_fields[TrendInsightsLabels.STD] = statistics_output[TrendInsightsLabels.STD]
            output_fields[TrendInsightsLabels.QUARTILE_UPPER] = statistics_output[TrendInsightsLabels.QUARTILE_UPPER]
            output_fields[TrendInsightsLabels.QUARTILE_LOWER] = statistics_output[TrendInsightsLabels.QUARTILE_LOWER]
            output_fields[TrendInsightsLabels.SUM] = statistics_output[TrendInsightsLabels.SUM]

            writer.writerow(selected_fields | output_fields)
    elif extension_id == SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID:
        selected_fields[SingleCorrelationLabels.OUTCOME_NAME] = output_dict[SingleCorrelationLabels.OUTCOME_NAME]
        selected_fields[SingleCorrelationLabels.OUTCOME_DOCUMENT_COUNT] = output_dict[
            SingleCorrelationLabels.OUTCOME_DOCUMENT_COUNT
        ]
        for single_correlation_result in output_dict[SingleCorrelationLabels.SINGLE_CORRELATION_RESULTS]:
            output_fields = {}
            correlation = single_correlation_result[SingleCorrelationLabels.CORRELATION]

            output_fields[SingleCorrelationLabels.TRIGGER_NAME] = single_correlation_result[
                SingleCorrelationLabels.TRIGGER_NAME
            ]
            output_fields[SingleCorrelationLabels.TRIGGER_DOCUMENT_COUNT] = single_correlation_result[
                SingleCorrelationLabels.TRIGGER_DOCUMENT_COUNT
            ]
            output_fields[SingleCorrelationLabels.MAX_CORRELATION] = single_correlation_result[
                SingleCorrelationLabels.MAX_CORRELATION
            ]
            output_fields[SingleCorrelationLabels.CONFIDENCE] = single_correlation_result[
                SingleCorrelationLabels.CONFIDENCE
            ]
            output_fields[SingleCorrelationLabels.RESULT_IMPLICATION] = single_correlation_result[
                SingleCorrelationLabels.RESULT_IMPLICATION
            ]

            output_fields[SingleCorrelationLabels.IMMEDIATE_TERM] = correlation[SingleCorrelationLabels.IMMEDIATE_TERM]
            output_fields[SingleCorrelationLabels.SHORT_TERM] = correlation[SingleCorrelationLabels.SHORT_TERM]
            output_fields[SingleCorrelationLabels.MEDIUM_TERM] = correlation[SingleCorrelationLabels.MEDIUM_TERM]
            output_fields[SingleCorrelationLabels.LONG_TERM] = correlation[SingleCorrelationLabels.LONG_TERM]

            writer.writerow(selected_fields | output_fields)
