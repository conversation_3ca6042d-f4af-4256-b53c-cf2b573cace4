import logging
import os
from datetime import datetime, timezone
from shutil import copyfileobj
from uuid import UUID
from zoneinfo import ZoneInfo

import magic

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.event_models.upload_event_model import UploadEventModel
from services.base.application.exceptions import ConflictingActionInProgress
from services.base.application.utils.data_task_orchestration import DataTaskOrchestration
from services.base.domain.constants.messaging import (
    ATT_NAME_LOADING_EVENT,
    MessageTopics,
)
from services.base.domain.enums.upload_states import UploadStates
from services.base.domain.enums.user_actions import UserActions
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.infrastructure.database.sql_alchemy.db_state_manager import get_db_session
from services.base.infrastructure.database.sql_alchemy.models.upload_state_entity import UploadStateEntity
from services.base.message_queue.utils import create_string_message_attribute
from services.file_service.constants import (
    FILE_TYPE_ZIP,
    MAX_UPLOADED_SEED_DATA_SIZE_B,
)
from services.file_service.utils.cleanup import delete_user_storage_data_from_provider
from services.file_service.utils.paths import get_user_provider_folder, sanitize_user_filename

from .exceptions import (
    CouldNotCreateProviderFolderError,
    CouldNotInitializeUploadStatus,
    FileBadTypeError,
    FileTooBigError,
)
from .upload_use_case_input import UploadUseCaseInput


class UploadUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        message_broker_client: AsyncMessageBrokerClient,
        job_service: DeprJobService,
        os_tasks_repository: MemberUserOSTasksRepository,
    ):
        self._message_broker_client = message_broker_client
        self._job_service = job_service
        self._os_tasks_repository = os_tasks_repository

    async def execute_async(self, user_uuid: UUID, input_object: UploadUseCaseInput, default_timezone: ZoneInfo) -> str:
        if not await DataTaskOrchestration.can_upload_user_data(
            user_uuid=user_uuid, job_service=self._job_service, os_tasks_repository=self._os_tasks_repository
        ):
            raise ConflictingActionInProgress("Can't upload data right now, conflicting action is already in progress.")

        folder_path = get_user_provider_folder(user_uuid, input_object.provider)
        os.makedirs(folder_path, exist_ok=True)
        # ensure creation of provider folder
        if not os.path.isdir(folder_path):
            raise CouldNotCreateProviderFolderError(
                "User provider folder does not exist even though it has been tried to create it"
            )

        form_file = input_object.file
        file = form_file.file

        # @TODO good enough file type check
        file_type = magic.from_buffer(file.read(2048), mime=True)  # read 1st 2KiB
        file.seek(0)  # reset file cursor!
        if file_type != FILE_TYPE_ZIP:
            raise FileBadTypeError("File is not supported type (not " + FILE_TYPE_ZIP + ")")

        file.seek(0, os.SEEK_END)
        file_length = file.tell()
        # Go to beginning / reset cursor!
        file.seek(0)

        if file_length > MAX_UPLOADED_SEED_DATA_SIZE_B:
            raise FileTooBigError("File exceeds max size (" + str(MAX_UPLOADED_SEED_DATA_SIZE_B) + "B) field ")

        # @TODO: Antivirus check? (to be extra sure)

        # Stop other filer service of the same user with the same provider

        # ... now we are pretty much sure it should be a zip file

        # also stops concurrent service runs
        delete_user_storage_data_from_provider(user_uuid=user_uuid, provider=input_object.provider)

        # Save the original user file name for User Logs
        user_filename: str = form_file.filename
        # For internal purposes, sanitize the filename
        filename: str = sanitize_user_filename(user_filename)
        file_path = os.path.join(folder_path, filename)

        # seek(0) was already done above
        with open(file_path, "wb") as target_file:
            copyfileobj(file, target_file)

        try:
            with get_db_session() as db_session:
                UploadStateEntity.initialize_upload_state_to_db(
                    db_session,
                    upload_state=UploadStateEntity(
                        user_uuid=user_uuid,
                        provider=input_object.provider.value,
                        state=UploadStates.UPLOADED.value,
                        internal_filename=filename,
                        user_filename=user_filename,
                    ),
                )
        except Exception as e:
            logging.exception(repr(e))
            raise CouldNotInitializeUploadStatus() from e

        # publish topic
        await self._message_broker_client.publish_topic(
            topic_name=MessageTopics.TOPIC_UPLOAD_FINISHED.value,
            message_body=UploadEventModel(
                user_uuid=user_uuid,
                timestamp=datetime.now(timezone.utc).isoformat(),
                user_filename=user_filename,
                filepath=file_path,
                provider=input_object.provider,
                action=UserActions.UPLOAD,
                fallback_timezone=str(default_timezone),
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_LOADING_EVENT, MessageTopics.TOPIC_UPLOAD_FINISHED.value
            ),
        )

        return "Successfully uploaded"
