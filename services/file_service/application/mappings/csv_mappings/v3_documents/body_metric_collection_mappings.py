from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucoseFields
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressureFields
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricFields
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.v3_documents.v3_non_collection_mappings import (
    V3CSVEventBaseMapping,
)

BodyMetricCollectionCSVMappings = {
    ExportableType.BodyMetric: [
        *V3CSVEventBaseMapping,
        # Body Metric Fields
        BodyMetricFields.VALUE,
        BodyMetricFields.NOTE,
    ],
    ExportableType.BloodPressure: [
        *V3CSVEventBaseMapping,
        # Blood Pressure Fields
        BloodPressureFields.DIASTOLIC,
        BloodPressureFields.SYSTOLIC,
        BloodPressureFields.NOTE,
    ],
    ExportableType.BloodGlucose: [
        *V3CSVEventBaseMapping,
        # Blood Glucose Fields
        BloodGlucoseFields.VALUE,
        BloodGlucoseFields.SPECIMEN_SOURCE,
        BloodGlucoseFields.NOTE,
    ],
}
