from dataclasses import dataclass

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.heart_rate import HeartRateFields
from services.base.domain.schemas.location import LocationFields, PlaceVisitDetailsFields
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateFields
from services.base.domain.schemas.shopping_activity import ShoppingActivityFields
from services.base.domain.schemas.sleep import SleepSummaryFields
from services.base.domain.schemas.steps import StepsFields
from services.file_service.application.enums.exportable_data_type import ExportableType


@dataclass(frozen=True)
class AdditionalTakeoutStringConstants:
    FIRST_STAGE_IN_SLEEP_EVENT = "first_stage_in_sleep_event"
    LAST_STAGE_IN_SLEEP_EVENT = "last_stage_in_sleep_event"
    FIRST_TIMESTAMP_IN_SLEEP_EVENT = "first_timestamp_in_sleep_event"
    LAST_END_TIME_IN_SLEEP_EVENT = "last_end_time_in_sleep_event"


def create_csv_field_string(*args):
    return ".".join(args)


V2CSVMappings = {
    ExportableType.HeartRate: [
        DocumentLabels.TIMESTAMP,
        DocumentLabels.DURATION,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # HeartRate fields
        HeartRateFields.BPM_AVG,
        HeartRateFields.BPM_MAX,
        HeartRateFields.BPM_MIN,
    ],
    ExportableType.RestingHeartRate: [
        DocumentLabels.TIMESTAMP,
        DocumentLabels.DURATION,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # RestingHeartRate fields
        RestingHeartRateFields.BPM_AVG,
        RestingHeartRateFields.BPM_MAX,
        RestingHeartRateFields.BPM_MIN,
    ],
    ExportableType.Sleep: [
        DocumentLabels.TIMESTAMP,
        DocumentLabels.END_TIME,
        DocumentLabels.DURATION,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # Sleep fields
        AdditionalTakeoutStringConstants.FIRST_STAGE_IN_SLEEP_EVENT,
        AdditionalTakeoutStringConstants.LAST_STAGE_IN_SLEEP_EVENT,
        SleepSummaryFields.IS_MAIN_SLEEP,
        SleepSummaryFields.ASLEEP_SECONDS,
        SleepSummaryFields.RESTLESS_SECONDS,
        SleepSummaryFields.AWAKE_SECONDS,
        SleepSummaryFields.IN_BED_SECONDS,
        SleepSummaryFields.DEEP_SECONDS,
        SleepSummaryFields.LIGHT_SECONDS,
        SleepSummaryFields.REM_SECONDS,
        SleepSummaryFields.FALL_ASLEEP_SECONDS,
        SleepSummaryFields.EFFICIENCY,
    ],
    ExportableType.Steps: [
        DocumentLabels.TIMESTAMP,
        DocumentLabels.DURATION,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # Steps fields
        StepsFields.STEPS,
    ],
    ExportableType.Location: [
        DocumentLabels.TIMESTAMP,
        DocumentLabels.DURATION,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # Location fields
        LocationFields.START_COORDINATES,
        LocationFields.END_COORDINATES,
        LocationFields.AVERAGE_COORDINATES,
        LocationFields.START_ALTITUDE,
        LocationFields.END_ALTITUDE,
        LocationFields.AVERAGE_ALTITUDE,
        create_csv_field_string(LocationFields.PLACE_VISIT_DETAILS, PlaceVisitDetailsFields.NAME),
        create_csv_field_string(LocationFields.PLACE_VISIT_DETAILS, PlaceVisitDetailsFields.ADDRESS),
    ],
    ExportableType.ShoppingActivity: [
        DocumentLabels.TIMESTAMP,
        create_csv_field_string(DocumentLabels.METADATA, MetadataFields.ORGANIZATION),
        # ShoppingActivity fields
        ShoppingActivityFields.ORDER_ID,
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.CATEGORY),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ASIN_ISBN),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.UNSPSC_CODE),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.RELEASE_DATE),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.CONDITION),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SELLER),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.LPPU),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.PPPU),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.QTY),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.PAY_TYPE),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.PURCHASE_ORDER_NO),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.PO_LINE_NUMBER),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ORDERING_CUSTOMER_EMAIL),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_DATE),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_NAME),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_STREET1),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_STREET2),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_CITY),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_STATE),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.SHIP_ADDRESS_ZIP),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ORDER_STATUS),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.CARRIER_NAME),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.CARRIER_TRACKING_NO),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ITEM_SUBTOTAL),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ITEM_SUBTOTAL_TAX),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.ITEM_TOTAL),
        create_csv_field_string(ShoppingActivityFields.ITEM_DETAIL, ShoppingActivityFields.CURRENCY),
    ],
}
