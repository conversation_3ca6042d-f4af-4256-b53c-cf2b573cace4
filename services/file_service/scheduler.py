from datetime import timedelta

from apscheduler.schedulers.asyncio import AsyncIOScheduler

from services.file_service.application.use_cases.export.validate_export_status_use_case import (
    ValidateIdleExportTasksUseCase,
)
from services.file_service.dependency_bootstrapper import DependencyBootstrapper


class Scheduler:
    def __init__(self, bootstrapper: DependencyBootstrapper):
        self.bootstrapper = bootstrapper
        self.scheduler = AsyncIOScheduler()
        self.scheduler.add_job(
            func=self._validate_idle_export_tasks,
            trigger="cron",
            hour="*",
            kwargs={"use_case": self.bootstrapper.get(interface=ValidateIdleExportTasksUseCase)},
        )

    async def _validate_idle_export_tasks(self, use_case: ValidateIdleExportTasksUseCase):
        await use_case.execute_async(
            timedelta_considered_idle=timedelta(hours=6),
        )

    def start(self):
        self.scheduler.start()

    def shutdown(self):
        self.scheduler.shutdown()
