#!/usr/bin/env python3
"""
Demo script to show how the Suggest<PERSON>odesFieldsUseCase works.
"""

from services.base.domain.type_tree.type_tree import TreeNode
from services.data_service.application.use_cases.type_tree.suggest_nodes_fields_use_case import (
    SuggestNodesFieldsUseCase,
    SuggestNodesFieldsUseCaseInputBoundary,
)


def demo_single_exercise_node():
    print("=== Single Exercise Node ===")
    use_case = SuggestNodesFieldsUseCase()
    exercise_node = TreeNode.from_path("doc.event.exercise")
    input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node])
    
    result = use_case.execute(input_boundary)
    
    print(f"Fields for exercise node:")
    for field_name, field_type in sorted(result.fields.items()):
        print(f"  {field_name}: {field_type}")
    print()


def demo_single_nutrition_node():
    print("=== Single Nutrition Node (Food) ===")
    use_case = SuggestNodesFieldsUseCase()
    food_node = TreeNode.from_path("doc.event.nutrition.food")
    input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node])
    
    result = use_case.execute(input_boundary)
    
    print(f"Fields for food node (showing hierarchical expansion):")
    for field_name, field_type in sorted(result.fields.items()):
        print(f"  {field_name}: {field_type}")
    print()


def demo_multiple_nutrition_nodes():
    print("=== Multiple Nutrition Nodes (Food + Drink) ===")
    use_case = SuggestNodesFieldsUseCase()
    food_node = TreeNode.from_path("doc.event.nutrition.food")
    drink_node = TreeNode.from_path("doc.event.nutrition.drink")
    input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[food_node, drink_node])
    
    result = use_case.execute(input_boundary)
    
    print(f"Common fields between food and drink:")
    for field_name, field_type in sorted(result.fields.items()):
        print(f"  {field_name}: {field_type}")
    print()


def demo_exercise_and_nutrition_intersection():
    print("=== Exercise + Nutrition Intersection ===")
    use_case = SuggestNodesFieldsUseCase()
    exercise_node = TreeNode.from_path("doc.event.exercise")
    food_node = TreeNode.from_path("doc.event.nutrition.food")
    input_boundary = SuggestNodesFieldsUseCaseInputBoundary(nodes=[exercise_node, food_node])
    
    result = use_case.execute(input_boundary)
    
    print(f"Common fields between exercise and nutrition:")
    for field_name, field_type in sorted(result.fields.items()):
        print(f"  {field_name}: {field_type}")
    print()


if __name__ == "__main__":
    print("SuggestNodesFieldsUseCase Demo")
    print("=" * 50)
    print()
    
    demo_single_exercise_node()
    demo_single_nutrition_node()
    demo_multiple_nutrition_nodes()
    demo_exercise_and_nutrition_intersection()
    
    print("Demo completed!")
